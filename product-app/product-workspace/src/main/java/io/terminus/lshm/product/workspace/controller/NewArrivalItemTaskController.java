package io.terminus.lshm.product.workspace.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import common.bean.request.supply.InnerSupplyListReadQueryRequest;
import common.facade.supplyList.read.InnerSupplyListReadFacade;
import common.model.supply.SupplyListTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.lshm.item.common.bean.request.item.InnerFindByIdItemCategoryRequest;
import io.terminus.lshm.item.common.bean.request.item.ItemQueryRequest;
import io.terminus.lshm.item.common.bean.response.item.InnerItemCategoryResponse;
import io.terminus.lshm.item.common.model.item.ItemMeasureUnitTO;
import io.terminus.lshm.item.common.model.item.ItemTO;
import io.terminus.lshm.item.facade.item.read.InnerItemCategoryReadFacade;
import io.terminus.lshm.item.facade.item.read.ItemReadFacade;
import io.terminus.lshm.product.common.bean.request.newArrival.*;
import io.terminus.lshm.product.common.bean.response.newArrival.ItemBaseDataResponse;
import io.terminus.lshm.product.common.bean.response.newArrival.NewArrivalItemTaskResponse;
import io.terminus.lshm.product.common.recordfiling.response.MyApprovalResponse;
import io.terminus.lshm.product.facade.newArrival.NewArrivalItemTaskReadFacade;
import io.terminus.lshm.product.facade.newArrival.NewArrivalItemTaskWriteFacade;
import io.terminus.lshm.product.common.offshelf.dto.NewArrivalItemTaskTO;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskResponse;
import io.terminus.lshm.product.util.Assert;
import io.terminus.lshm.product.workspace.converter.ItemCommonConverter;
import io.terminus.lshm.product.workspace.excel.service.ExportNewArrivalItemTaskrService;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@Slf4j
@RequestMapping(path = "/product2/api/new-arrival-item-task")
@Api(tags = "商品上新任务接口")
public class NewArrivalItemTaskController {


    @Resource
    private NewArrivalItemTaskReadFacade newArrivalItemTaskReadFacade;

    @Resource
    private NewArrivalItemTaskWriteFacade newArrivalItemTaskWriteFacade;

    @Resource
    private ExportNewArrivalItemTaskrService exportNewArrivalItemTaskrService;
    @Resource
    private ItemCommonConverter itemCommonConverter;

    @Resource
    private ItemReadFacade itemReadFacade;


    @Resource
    private InnerSupplyListReadFacade innerSupplyListReadFacade;
    @Resource
    private InnerItemCategoryReadFacade innerItemCategoryReadFacade;
    /**
     * 分页查询任务
     *
     * @param request 查询请求
     * @return 分页响应
     */
    @ApiOperation("分页查询任务")
    @PostMapping(path = "/page")
    public Response<Paging<NewArrivalItemTaskResponse>> pageOffShelfItemTask(@RequestBody NewArrivalItemTaskPageRequest request) {
        Response<Paging<NewArrivalItemTaskResponse>> pagingResponse = newArrivalItemTaskReadFacade.pageOffShelfItemTask(request);
        return pagingResponse;
    }

    /**
     * 保存
     *
     * @param requestList
     * @return
     */
    @ApiOperation("保存")
    @PostMapping(path = "/batchSave")
    public Response<Map<Integer, Long>> save(@RequestBody List<NewArrivalItemTaskRequest> requestList){
        //参数校验
        requestList.forEach(NewArrivalItemTaskRequest::checkParam);
        return newArrivalItemTaskWriteFacade.save(requestList);
    }

    /**
     * 提交
     *
     * @param requestList
     * @return
     */
    @ApiOperation("提交")
    @PostMapping(path = "/batchSubmit")
    public Response<Boolean> submit(@RequestBody List<NewArrivalItemTaskRequest> requestList){
        //参数校验
        requestList.forEach(NewArrivalItemTaskRequest::checkParam);
        return newArrivalItemTaskWriteFacade.submit(requestList);
    }

    /**
     * 修改
     *
     * @param request
     * @return
     */
    @ApiOperation("修改")
    @PostMapping(path = "/update")
    public Response<Boolean> update(@RequestBody NewArrivalItemTaskRequest request){
        request.checkParam();
        return newArrivalItemTaskWriteFacade.update(request);
    }


    @ApiOperation("审批上新商品任务")
    @PostMapping(path = "/audit")
    public Response<Boolean> audit(@RequestBody NewArrivalTaskAuditRequest request) {
        return newArrivalItemTaskWriteFacade.audit(request);
    }



    /**
     * 根据ID查询任务详情
     *
     * @param request 查询请求
     * @return 商品下架任务详情
     */
    @ApiOperation("根据ID查询任务详情")
    @PostMapping(path = "/get")
    public Response<NewArrivalItemTaskTO> getTask(@RequestBody NewArrivalItemTaskByIdRequest request) {
        Response<NewArrivalItemTaskTO> info = newArrivalItemTaskReadFacade.getTask(request);
        return info;
    }


    /**
     * 获取标签列表
     *
     * @return 获取标签列表
     */
    @ApiOperation("获取标签列表")
    @PostMapping(path = "/getLabelList")
    public Response<List<ItemBaseDataResponse>> getLabelList() {
        return newArrivalItemTaskReadFacade.getLabelList();
    }


    /**
     * 导出
     *
     * @param request 导出
     * @return
     */
    @ApiOperation("上新列表导出")
    @PostMapping(path = "/export")
    public Response<BooleanResult> export(@RequestBody NewArrivalItemTaskExportRequest request) {
        BooleanResult result = exportNewArrivalItemTaskrService.export(request);
        return Response.ok(result);
    }

    /**
     * 根据ID查询商品详情
     *
     * @param request 查询请求
     * @return 查询商品详情
     */
    @ApiOperation("根据ID查询任务详情")
    @PostMapping(path = "/getItemInfo")
    public Response<RecordFilingItemTaskResponse> getItemInfo(@RequestBody ItemQueryRequest request) {
        ItemTO itemTO = Assert.getResult(itemReadFacade.findItemDetailById(request));
        RecordFilingItemTaskResponse itemResponse = itemCommonConverter.itemToRecordFiling(itemTO);
        //处理外箱/内箱条码
        List<String> innerBarcodeList = new ArrayList<>();
        List<ItemMeasureUnitTO> itemMeasureUnitTOS = itemTO.getItemMeasureUnitTOS();
        if (ObjectUtil.isNotEmpty(itemMeasureUnitTOS)) {
            itemMeasureUnitTOS.forEach(itemMeasureUnitTO -> {
                if (itemMeasureUnitTO.getIsMainUnit()) {
                    //外箱条码
                    itemResponse.setBarCodeList(itemMeasureUnitTO.getBarcode());
                }
                if (ObjectUtil.isNotEmpty(itemMeasureUnitTO.getBarcodeSet())) {
                    //内箱条码
                    innerBarcodeList.addAll(itemMeasureUnitTO.getBarcodeSet());
                }

            });
            if (ObjectUtil.isNotEmpty(innerBarcodeList)) {
                itemResponse.setInnerBarcode(String.join(",", innerBarcodeList));
            }
        }

        //查询商品类别
        InnerFindByIdItemCategoryRequest innerFindByIdItemCategoryRequest = new InnerFindByIdItemCategoryRequest();
        innerFindByIdItemCategoryRequest.setIds(ListUtil.of(itemTO.getCategory()));
        Response<List<InnerItemCategoryResponse>> innerItemCategoryResponse = innerItemCategoryReadFacade.findByIds(innerFindByIdItemCategoryRequest);
        if (innerItemCategoryResponse.getSuccess()) {
            List<InnerItemCategoryResponse> innerItemCategoryResponses = innerItemCategoryResponse.getRes();
            if (ObjectUtil.isNotEmpty(innerItemCategoryResponses)) {
                itemResponse.setItemTypeName(innerItemCategoryResponses.get(0).getCategoryName());
            }
        }

        return Response.ok(itemResponse);


    }

    @ApiOperation("上新我的审批")
    @PostMapping(path = "/myApprovals")
    public Response<MyApprovalResponse> myApprovals(){
        return newArrivalItemTaskReadFacade.myApprovals();
    }
}