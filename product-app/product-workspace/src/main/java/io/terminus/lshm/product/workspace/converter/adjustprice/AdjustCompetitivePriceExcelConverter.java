package io.terminus.lshm.product.workspace.converter.adjustprice;

import io.terminus.lshm.product.facade.price.dto.AdjustPriceExcelDTO;
import io.terminus.lshm.product.workspace.excel.dto.AdjustCompetitivePriceExcel;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public interface AdjustCompetitivePriceExcelConverter {

    default List<AdjustCompetitivePriceExcel> convert(List<AdjustPriceExcelDTO> list) {
        List<AdjustCompetitivePriceExcel> adjustCompetitivePriceExcelList = new ArrayList<>();
        list.forEach(excelDTO -> {
            AdjustCompetitivePriceExcel adjustPriceDTO = new AdjustCompetitivePriceExcel();
            adjustPriceDTO.setTitile(excelDTO.getTitile());
            adjustPriceDTO.setCategory(excelDTO.getCategory());
            adjustPriceDTO.setAdjustStatus(excelDTO.getAdjustStatus());
            adjustPriceDTO.setCreatedAt(excelDTO.getCreatedAt());
            adjustPriceDTO.setCreatedName(excelDTO.getCreatedName());
            adjustPriceDTO.setRetailEffectiveType(excelDTO.getRetailEffectiveType());
            adjustPriceDTO.setReason(excelDTO.getReason());
            adjustPriceDTO.setRemark(excelDTO.getRemark());
            adjustPriceDTO.setStoreList(excelDTO.getStoreList());
            adjustPriceDTO.setItemCode(excelDTO.getItemCode());
            adjustPriceDTO.setItemName(excelDTO.getItemName());
            adjustPriceDTO.setItemUnitName(excelDTO.getItemUnitName());
            adjustPriceDTO.setItemArchiveRetailPrice(excelDTO.getItemArchiveRetailPrice());
            adjustPriceDTO.setItemRetailPriceNew(excelDTO.getItemRetailPriceNew());
            adjustPriceDTO.setDiscountRate(excelDTO.getDiscountRate());
            adjustCompetitivePriceExcelList.add(adjustPriceDTO);
        });


        return adjustCompetitivePriceExcelList;
    }

}
