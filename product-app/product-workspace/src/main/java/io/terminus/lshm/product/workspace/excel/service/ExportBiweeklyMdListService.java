package io.terminus.lshm.product.workspace.excel.service;


import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyPageRequest;
import io.terminus.lshm.product.workspace.excel.config.ExportBiweeklyMdListProperties;
import io.terminus.lshm.product.workspace.excel.constant.ExcelConstant;
import io.terminus.lshm.product.workspace.excel.dto.BigExcelReq;
import io.terminus.lshm.product.workspace.excel.dto.BiweeklyMdListExcel;
import io.terminus.lshm.product.workspace.util.ExcelUtil;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.file.ExportProgressChanged;
import io.terminus.trantorframework.file.config.ExportThreadPoolConfig;
import io.terminus.trantorframework.file.enums.ImportExportStatus;
import io.terminus.trantorframework.file.model.ImportExportLogDTO;
import io.terminus.trantorframework.file.model.ModelDataTemplateDTO;
import io.terminus.trantorframework.file.model.ModelExportStatistic;
import io.terminus.trantorframework.file.utils.ExcelUtils;
import io.terminus.trantorframework.sdk.threadlocal.wrapper.ContextRunnable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;

@Service
@Slf4j
public class ExportBiweeklyMdListService {

    @Resource
    @Qualifier(ExportThreadPoolConfig.THREAD_POOL_NAME)
    private ThreadPoolExecutor importThreadPool;

    @Resource
    private TrantorExcelService trantorExcelService;

    @Autowired
    private ExportBiweeklyMdListProperties exportBiweeklyMdListProperties;

    @Qualifier("BiweeklyMdList")
    @Resource
    private IExcelExportServer iExcelExportServer;

    public BooleanResult export(MdSpecialApplyPageRequest request) {
        //配置模版内容
        ModelDataTemplateDTO dataTemplate = trantorExcelService.getTemplate(exportBiweeklyMdListProperties.getId(), exportBiweeklyMdListProperties.getKey());
        String fileName = ExcelConstant.EXPORT_BIWEEKLY_MD_LIST_NAME;

        //创建模型导出记录
        ImportExportLogDTO exportLog = trantorExcelService.createExportLog(dataTemplate, String.format(fileName, "", ExcelConstant.EXCEL_TYPE_XLSX));
        importThreadPool.submit(ContextRunnable.convert(() -> export(exportLog,request)));
        return BooleanResult.TRUE;
    }

    private void export(ImportExportLogDTO importExportLog, MdSpecialApplyPageRequest request) {
        ModelExportStatistic exportResult = new ModelExportStatistic();
        try {
            exportResult = exportInternal(importExportLog.getFileName(), request, exportProgressResult -> trantorExcelService.updateModelExportLog(importExportLog, exportProgressResult));
            importExportLog.setStatus(ImportExportStatus.EXPORT_FINISH);
        } catch (Exception e) {
            log.error("Fail to export model data", e);
            importExportLog.setResult(e.getMessage());
            importExportLog.setStatus(ImportExportStatus.EXPORT_FAIL);
        }
        //更新文件导出记录
        trantorExcelService.updateModelExportLog(importExportLog, exportResult);
    }

    private ModelExportStatistic exportInternal(String fileName, MdSpecialApplyPageRequest request, ExportProgressChanged exportProgressChanged) {
        File excelFile = ExcelUtils.getFullPathFile(fileName);
        ModelExportStatistic exportResult = new ModelExportStatistic();
        exportResult.setExportFile(excelFile);
        String sheetName = ExcelConstant.EXPORT_BIWEEKLY_MD_LIST_NAME;
        final ExportParams exportParams = new ExportParams(null, sheetName, ExcelType.HSSF);
        try {
            BigExcelReq bigExcelReq = new BigExcelReq(request, exportResult, exportProgressChanged);
            ExcelUtil.exportBigPageExcel(exportParams, BiweeklyMdListExcel.class, iExcelExportServer, bigExcelReq, excelFile);
        } catch (IOException e) {
            throw new BusinessException("导出失败", e);
        }

        File exportFile = exportResult.getExportFile();
        Optional<String> fileUploadPath = trantorExcelService.uploadExcelFileToOss(exportFile);
        fileUploadPath.ifPresent(s -> exportResult.setFileOssUrl(s.split("\\?")[0]));
        exportFile.delete();
        return exportResult;
    }

}
