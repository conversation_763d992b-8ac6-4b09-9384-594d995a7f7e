package io.terminus.lshm.product.workspace.excel.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.lshm.product.common.biweekly.dto.MdAttachmentInfoTO;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import io.terminus.lshm.store.common.model.store.InnerStoreTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@ApiModel("双周MD特殊申请列表")
public class BiweeklyMdListExcel implements Serializable  {

    @ApiModelProperty(name = "唯一标识")
    private Long id;

    @Excel(name = "序号", orderNum = "1", height = 20, width = 15)
    @ApiModelProperty(name = "序号")
    private Integer sort;

    @Excel(name = "申请月份", orderNum = "10", height = 20, width = 30, format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(name = "申请月份")
    private Date applyMonthTime;

    @Excel(name = "流程标题", orderNum = "6", height = 20, width = 38)
    @ApiModelProperty(name = "流程标题")
    private String title;

    @ApiModelProperty(name = "应用门店（关联门店数据）")
    private List<MdRelationalStoreTO> mdRelationalStores;

    @Excel(name = "申请理由", orderNum = "6", height = 20, width = 38)
    @ApiModelProperty(name = "申请理由")
    private String applyReason;

    @Excel(name = "审批状态", orderNum = "9", height = 20, width = 15)
    @ApiModelProperty(name = "审批状态")
    private String auditStatus;

    @Excel(name = "审批流程状态", orderNum = "9", height = 20, width = 30)
    @ApiModelProperty(name = "审批流程状态")
    private String auditFlowStatus;

    @Excel(name = "门店数", orderNum = "10", height = 20, width = 25)
    @ApiModelProperty(name = "门店数")
    private Integer storeRelationQuantity;

    @Excel(name = "申请日期", orderNum = "10", height = 20, width = 30, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "申请日期（创建日期）")
    private Date createdAt;

    @Excel(name = "申请人昵称", orderNum = "10", height = 20, width = 25)
    @ApiModelProperty(name = "申请人昵称")
    private String createdName;

    @Excel(name = "申请人手机号", orderNum = "10", height = 20, width = 25)
    @ApiModelProperty(name = "申请人手机号")
    private String createdPhone;

    @Excel(name = "报备周期", orderNum = "10", height = 20, width = 25)
    @ApiModelProperty(name = "报备周期")
    private String filingCycle;

    @Excel(name = "备注", orderNum = "10", height = 20, width = 38)
    @ApiModelProperty(name = "备注")
    private String remark;

    @ApiModelProperty(name = "相关附件（与附件表关联）")
    private List<MdAttachmentInfoTO> attachments;

    @Excel(name = "更新日期", orderNum = "10", height = 20, width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "更新日期")
    private Date updatedAt;

    @Excel(name = "更新人昵称", orderNum = "10", height = 20, width = 25)
    @ApiModelProperty(name = "更新人昵称")
    private String updatedName;

    @ApiModelProperty(name = "关联申请表id")
    private Long specialApplyId;

    @ApiModelProperty("流程实例id")
    private String workflowInstanceId;

    @ApiModelProperty(name = "门店中心的门店信息")
    private List<InnerStoreTO> stores;

}
