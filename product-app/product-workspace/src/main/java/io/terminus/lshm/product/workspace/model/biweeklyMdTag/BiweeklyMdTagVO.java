package io.terminus.lshm.product.workspace.model.biweeklyMdTag;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.terminus.lshm.product.common.biweekly.dto.MdAttachmentInfoTO;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description 双周MD参数管理
 * <AUTHOR>
 * @Date 2025/7/1
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "双周MD参数管理")
public class BiweeklyMdTagVO extends BaseModel<Long> {


    /**
     * 主键id
     * */
    @Field(name = "主键id")
    private Long id;


    /**
     * 主键
     */
    @Field(name = "业务类型")
    private String businessType;


    /**
     * 申请月份
     * */
    @Field(name = "申请月份")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyMonthTime;


    /**
     * 流程标题
     * */
    @Field(name = "流程标题")
    private String title;


    /**
     * 申请理由
     * */
    @Field(name = "申请理由")
    private String applyReason;


    /**
     * 审批状态
     * */
    @Field(name = "审批状态")
    private String auditStatus;



    /**
     * 审批流程状态
     * */
    @Field(name = "审批流程状态")
    private String auditFlowStatus;


    /**
     * 申请人昵称
     * */
    @Field(name = "申请人昵称")
    private String createdName;


    /**
     * 申请人手机号
     * */
    @Field(name = "申请人手机号")
    private String createdPhone;


    /**
     * 报备周期
     * */
    @Field(name = "报备周期")
    private String filingCycle;


    /**
     * 备注
     * */
    @Field(name = "备注")
    private String remark;


    /**
     * 门店id
     * */
    @Field(name = "门店id")
    private String storeId;


    /**
     * 门店编码
     * */
    @Field(name = "门店编码")
    private String storeCode;


    /**
     * 门店名称
     * */
    @Field(name = "门店名称")
    private String storeName;


    /**
     * 申请表ID（一对多）
     * */
    @Field(name = "申请表ID（一对多）")
    private Long specialApplyId;


    /**
     * 流程实例id
     * */
    @Field(name = "流程实例id")
    private String workflowInstanceId;


    /**
     * 附表信息集合
     * */
    @Field(name = "附表信息集合",type = FieldType.Json)
    private List<MdAttachmentInfoTO> attachmentList;


    /**
     * 新建时选择的所有门店集合
     * */
    @Field(name = "新建时选择的所有门店集合",type = FieldType.Json)
    private List<MdRelationalStoreTO> storeList;



}
