package io.terminus.lshm.product.workspace.excel.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品调价
 */
@Data
@ApiModel("商品调价-全国区域常规调价")
public class AdjustNationalPriceExcel implements Serializable {

    /**
     * 标题
     */
    @ApiModelProperty("流程名称")
    @Excel(name = "流程名称", orderNum = "1", height = 20, width = 20)
    private String titile;

    /**
     * 调价类别
     */
    @ApiModelProperty("调价类别")
    @Excel(name = "调价类别", orderNum = "2", height = 20, width = 20)
    private String category;
    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    @Excel(name = "审批状态", orderNum = "3", height = 20, width = 20)
    private String adjustStatus;
    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    @Excel(name = "申请时间", orderNum = "4", height = 20, width = 20)
    private String createdAt;
    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    @Excel(name = "申请人", orderNum = "5", height = 20, width = 20)
    private String createdName;
    /**
     * 配送价调价时间
     */
    @ApiModelProperty("配送价调价时间")
    @Excel(name = "配送价调价时间", orderNum = "6", height = 20, width = 20)
    private String deliveryTime;
    /**
     * 配送价失效后生效价格
     */
    @ApiModelProperty("配送价失效后生效价格")
    @Excel(name = "配送价失效后生效价格", orderNum = "7", height = 20, width = 20)
    private String effectiveType;
    /**
     * 零售价调价时间
     */
    @ApiModelProperty("零售价调价时间")
    @Excel(name = "零售价调价时间", orderNum = "8", height = 20, width = 20)
    private String retailTime;
    /**
     * 零售价失效后生效价格
     */
    @ApiModelProperty("零售价失效后生效价格")
    @Excel(name = "零售价失效后生效价格", orderNum = "9", height = 20, width = 20)
    private String retailEffectiveType;
    /**
     * 档案价调价时间
     */
    @ApiModelProperty("档案价调价时间")
    @Excel(name = "档案价调价时间", orderNum = "10", height = 20, width = 20)
    private String mainTime;
    /**
     * 档案价失效后生效价格
     */
    @ApiModelProperty("档案价失效后生效价格")
    @Excel(name = "档案价失效后生效价格", orderNum = "11", height = 20, width = 20)
    private String mainEffectiveType;
    /**
     * 调价通知类别
     */
    @ApiModelProperty("调价通知类别")
    @Excel(name = "调价通知类别", orderNum = "12", height = 20, width = 20)
    private String adjustNotice;
    /**
     * 申请原因描述
     */
    @ApiModelProperty("申请原因描述")
    @Excel(name = "申请原因描述", orderNum = "13", height = 20, width = 20)
    private String reason;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Excel(name = "备注", orderNum = "14", height = 20, width = 20)
    private String remark;
    /**
     * 门店清单
     */
    @ApiModelProperty("门店清单")
    @Excel(name = "门店清单", orderNum = "15", height = 20, width = 20)
    private String storeList;
    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    @Excel(name = "商品编码", orderNum = "16", height = 20, width = 20)
    private String itemCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    @Excel(name = "商品名称", orderNum = "17", height = 20, width = 20)
    private String itemName;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    @Excel(name = "单位", orderNum = "18", height = 20, width = 20)
    private String itemUnitName;
    /**
     * 原档案配送价
     */
    @Excel(name = "原档案配送价", orderNum = "19", height = 20, width = 20)
    private BigDecimal itemArchiveDeliveryPrice;
    /**
     * 新配送价
     */
    @Excel(name = "新配送价", orderNum = "20", height = 20, width = 20)
    private BigDecimal itemDeliveryPriceNew;
    /**
     * 原档案零售价
     */
    @Excel(name = "原档案零售价", orderNum = "21", height = 20, width = 20)
    private BigDecimal itemArchiveRetailPrice;
    /**
     * 新零售价
     */
    @Excel(name = "新零售价", orderNum = "22", height = 20, width = 20)
    private BigDecimal itemRetailPriceNew;
    /**
     * 原档案会员价
     */
    @Excel(name = "原档案会员价", orderNum = "23", height = 20, width = 20)
    private BigDecimal itemArchiveMemberPrice;
    /**
     * 新会员价
     */
    @Excel(name = "新会员价", orderNum = "24", height = 20, width = 20)
    private BigDecimal itemMemberPriceNew;
    /**
     * 新档案配送价
     */
    @Excel(name = "新档案配送价", orderNum = "25", height = 20, width = 20)
    private BigDecimal itemArchiveDeliveryPriceNew;
    /**
     * 新档案零售价
     */
    @Excel(name = "新档案零售价", orderNum = "26", height = 20, width = 20)
    private BigDecimal itemArchiveRetailPriceNew;
    /**
     * 新档案会员价
     */
    @Excel(name = "新档案会员价", orderNum = "27", height = 20, width = 20)
    private BigDecimal itemArchiveMemberPriceNew;


}