package io.terminus.lshm.product.workspace.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.lshm.product.common.bean.request.biweeklyMd.BiweeklyMdReRequest;
import io.terminus.lshm.product.common.bean.request.bpm.FlowCancelRequest;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import io.terminus.lshm.product.common.offshelf.dto.EmployeeUsersTO;
import io.terminus.lshm.product.facade.biweeklyMd.BiweeklyMdWriteFacade;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.workspace.excel.service.ExportBiweeklyMdListService;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.facade.biweekly.MdSpecialApplyWriteFacade;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.workspace.excel.service.ExportBiweeklyMdListService;
import io.terminus.lshm.store.common.bean.request.store.read.InnerPageAllRequest;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import lombok.extern.slf4j.Slf4j;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyPageRequest;
import io.terminus.lshm.product.facade.biweekly.MdSpecialApplyReadFacade;
import io.terminus.lshm.store.common.bean.request.store.read.InnerPageStoreRequest;
import io.terminus.lshm.store.common.model.store.InnerStoreTO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 双周MD控制类
 * <AUTHOR>
 * @date 2025/6/19 10:51
 */
@Api(tags = "双周MD管理")
@RequestMapping("/product2/api/biweeklyMd")
@RestController
@Slf4j
public class BiweeklyMdController {

    @Resource
    private MdSpecialApplyReadFacade mdSpecialApplyReadFacade;

    @Resource
    private MdSpecialApplyWriteFacade mdSpecialApplyWriteFacade;

    @Resource
    private BiweeklyMdWriteFacade biweeklyMdWriteFacade;

    @Autowired
    private ExportBiweeklyMdListService exportBiweeklyMdListService;

    /**
     * 审批
     *
     * @param request 审批
     * @return
     */
    @PostMapping(value = "/auditApply")
    @ApiOperation("审批")
    public Response<Boolean> auditApply(@RequestBody FlowApplyBizRequest request) {

        return biweeklyMdWriteFacade.audit(request);
    }

    /**
     * 批量审批
     *
     * @param requests 批量审批
     * @return
     */
    @PostMapping(value = "/batchAuditApply")
    @ApiOperation("批量审批")
    public Response<Boolean> batchAuditApply(@RequestBody List<FlowApplyBizRequest> requests) {

        return biweeklyMdWriteFacade.batchAudit(requests);
    }

    /**
     * 催办
     * @param id
     * @return
     */
    @PostMapping(value = "/flowUp")
    @ApiOperation("催办")
    public Response<Boolean> flowUp(@RequestParam("id") Long id) {

        return biweeklyMdWriteFacade.flowUp(id);
    }

    /**
     * 作废
     * @param request
     * @return
     */
    @PostMapping(value = "/cancel")
    @ApiOperation("作废")
    public Response<Boolean> cancel(@RequestBody @Validated FlowCancelRequest request) {

        return biweeklyMdWriteFacade.cancel(request);
    }

    /**
     * 再次提交
     *
     * @param request 再次提交
     * @return
     */
    @PostMapping(value = "/reApply")
    @ApiOperation("再次提交")
    public Response<Boolean> reApply(@RequestBody FlowApplyBizRequest<BiweeklyMdReRequest> request) {
        return biweeklyMdWriteFacade.reApply(request);
    }


    /**
     * 分页查询md特殊申请列表
     * @param request 查询请求
     * @return 分页响应
     * **/
    @RequestMapping("/pageMdSpecialApply")
    Response<Paging<MdSpecialApplyTO>> pageMdSpecialApply(@RequestBody MdSpecialApplyPageRequest request){
        return mdSpecialApplyReadFacade.pageMdSpecialApply(request);
    };


    /**
     * 调用门店中心的列表接口获取门店数据（新建和列表门店查询时都是这个接口，都需要获取所有门店数据）
     * @param request 请求参数（如果需要）
     * @return 门店分页数据
     */
    @RequestMapping("/getStoreList")
    Response<Paging<InnerStoreTO>> getStoreList(@RequestBody InnerPageAllRequest request){
        return mdSpecialApplyReadFacade.getStoreList(request);
    };



    /**
     * 查看md特殊申请详情
     * @param request 查询请求
     * @return 分页响应
     * **/
    @RequestMapping("/getMdSpecialApply")
    Response<MdSpecialApplyTO> getMdSpecialApply(@RequestBody MdSpecialApplyByRequest request){
        return mdSpecialApplyReadFacade.getMdSpecialApply(request);
    };


    /**
     * 查看详情时获门店信息，先查询出门店关联表中的门店id，在根据门店id去查询门店表，分页返回门店信息
     * */
    @RequestMapping("/getRelationalStoreInfo")
    Response<List<MdRelationalStoreTO>> getRelationalStoreInfo(@RequestBody MdSpecialApplyPageRequest request){
        return mdSpecialApplyReadFacade.getRelationalStoreInfo(request);
    };




    /**
     * 点击新建时，把申请人的昵称，手机号，还有申请日期放回给前端
     * */
    @RequestMapping("/echoUser")
    Response<EmployeeUsersTO> echoUser(@RequestBody MdSpecialApplyByRequest request){
        return mdSpecialApplyReadFacade.echoUser(request);
    };



    /**
     * 新增MD特殊申请（提交审批）
     * @param request 添加请求
     * @return 布尔
     * */
    @RequestMapping("/addMdSpecialApply")
    Response<Boolean> addMdSpecialApply(@RequestBody @Validated FlowApplyBizRequest<MdSpecialApplyByRequest> request){
        return mdSpecialApplyWriteFacade.addMdSpecialApply(request);
    };


    /**
     * 再次提交对数据进行修改（只能更改申请理由，备注，附件信息和门店信息）
     * */
    @RequestMapping("/updateMdSpecialApply")
    Response<Boolean> updateMdSpecialApply(@RequestBody @Validated FlowApplyBizRequest<MdSpecialApplyByRequest> request){
        return mdSpecialApplyWriteFacade.updateMdSpecialApply(request);
    };



    /**
     * 点击审批或者查看时，获取审批记录信息
     * */
    @RequestMapping("/getApprovalInfo")
    Response<List<MdAuditRecordTO>> getApprovalInfo(@RequestBody MdSpecialApplyByRequest request){
        return mdSpecialApplyReadFacade.getApprovalRecord(request);
    };

    @ApiOperation("双周MD特殊申请列表导出")
    @PostMapping(path = "/exportBiweeklyMdList")
    Response<BooleanResult> getApprovalRecord(@RequestBody MdSpecialApplyPageRequest request){
        BooleanResult result = exportBiweeklyMdListService.export(request);
        return Response.ok(result);
    };

}
