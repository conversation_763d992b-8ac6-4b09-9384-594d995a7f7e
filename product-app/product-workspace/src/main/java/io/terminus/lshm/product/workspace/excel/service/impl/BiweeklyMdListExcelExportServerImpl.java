package io.terminus.lshm.product.workspace.excel.service.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import io.terminus.lshm.product.common.bean.util.ResponseUtils;
import io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyPageRequest;
import io.terminus.lshm.product.common.enums.AdjustNoticeEnum;
import io.terminus.lshm.product.common.enums.ApplyReasonEnum;
import io.terminus.lshm.product.common.enums.AuditStatusEnum;
import io.terminus.lshm.product.common.enums.biweeklyMd.BiweeklyMdFlowNodeEnum;
import io.terminus.lshm.product.common.offshelf.dto.NodeDTO;
import io.terminus.lshm.product.common.offshelf.dto.OffShelfItemTaskParentTO;
import io.terminus.lshm.product.common.offshelf.request.OffShelfItemTaskPageRequest;
import io.terminus.lshm.product.facade.biweekly.MdSpecialApplyReadFacade;
import io.terminus.lshm.product.facade.offshelf.OffShelfItemTaskReadFacade;
import io.terminus.lshm.product.workspace.excel.dto.BigExcelReq;
import io.terminus.lshm.product.workspace.excel.dto.BiweeklyMdListExcel;
import io.terminus.lshm.product.workspace.excel.dto.OffShelfItemTaskProcessExcel;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import io.terminus.trantorframework.file.ExportProgressChanged;
import io.terminus.trantorframework.file.model.ModelExportStatistic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/8
 * @Version 1.0
 */
@Slf4j
@Service("BiweeklyMdList")
public class BiweeklyMdListExcelExportServerImpl implements IExcelExportServer {

    @Resource
    private MdSpecialApplyReadFacade mdSpecialApplyReadFacade;

    //导出时分页查询每页大小
    private static final int MAX_PAGE_SIZE = 500;

    @Override
    public List<Object> selectListForExcelExport(Object o, int i) {
        BigExcelReq bigExcelReq = (BigExcelReq) o;
        MdSpecialApplyPageRequest request = (MdSpecialApplyPageRequest) bigExcelReq.getRequest();
        request.setPageNo(i);
        request.setPageSize(MAX_PAGE_SIZE);

        Paging<MdSpecialApplyTO> pageResult = ResponseUtils.unbox(mdSpecialApplyReadFacade.pageMdSpecialApply(request));
        if (ObjectUtil.isEmpty(pageResult) || ObjectUtil.isEmpty(pageResult.getData())) {
            return Collections.emptyList();
        }
        List<Object> objects = new ArrayList<>();
        List<BiweeklyMdListExcel> resultList = Convert.toList(BiweeklyMdListExcel.class, pageResult.getData());
        resultList.forEach(x -> {
            x.setApplyReason(ApplyReasonEnum.getDescByCode(x.getApplyReason()));
            x.setAuditStatus(AuditStatusEnum.getDescByCode(x.getAuditStatus()));
            x.setAuditFlowStatus(BiweeklyMdFlowNodeEnum.getDescByCode(x.getAuditFlowStatus()));
            objects.add(x);
        });
        ModelExportStatistic exportResult = bigExcelReq.getExportResult();
        ExportProgressChanged exportProgressChanged = bigExcelReq.getExportProgressChanged();
        exportResult.setSuccessNum(exportResult.getSuccessNum() + objects.size());
        if (exportProgressChanged != null) {
            exportProgressChanged.onProgressChanged(exportResult);
        }
        exportResult.setTotalNum(exportResult.getSuccessNum());
        return objects;
    }

}
