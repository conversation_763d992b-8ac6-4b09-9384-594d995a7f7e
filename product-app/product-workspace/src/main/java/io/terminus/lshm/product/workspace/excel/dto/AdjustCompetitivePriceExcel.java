package io.terminus.lshm.product.workspace.excel.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品调价
 */
@Data
@ApiModel("商品调价-竞争促销调价")
public class AdjustCompetitivePriceExcel implements Serializable {

    /**
     * 标题
     */
    @ApiModelProperty("流程名称")
    @Excel(name = "流程名称", orderNum = "1", height = 20, width = 20)
    private String titile;

    /**
     * 调价类别
     */
    @ApiModelProperty("调价类别")
    @Excel(name = "调价类别", orderNum = "2", height = 20, width = 20)
    private String category;
    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    @Excel(name = "审批状态", orderNum = "3", height = 20, width = 20)
    private String adjustStatus;
    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    @Excel(name = "申请时间", orderNum = "4", height = 20, width = 20)
    private String createdAt;
    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    @Excel(name = "申请人", orderNum = "5", height = 20, width = 20)
    private String createdName;
    /**
     * 促销类型
     */
    @ApiModelProperty("促销类型")
    @Excel(name = "促销类型", orderNum = "6", height = 20, width = 20)
    private String promotionType;
    /**
     * 活动时间
     */
    @ApiModelProperty("活动时间")
    @Excel(name = "活动时间", orderNum = "7", height = 20, width = 20)
    private String activityTime;
    /**
     * 零售价失效后生效价格
     */
    @ApiModelProperty("零售价失效后生效价格")
    @Excel(name = "零售价失效后生效价格", orderNum = "8", height = 20, width = 20)
    private String retailEffectiveType;
    /**
     * 照片附件
     */
    @ApiModelProperty("照片附件")
    @Excel(name = "照片附件", orderNum = "9", height = 20, width = 20)
    private String fileUrl;
    /**
     * 申请原因描述
     */
    @ApiModelProperty("申请原因描述")
    @Excel(name = "申请原因描述", orderNum = "10", height = 20, width = 20)
    private String reason;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Excel(name = "备注", orderNum = "11", height = 20, width = 20)
    private String remark;
    /**
     * 门店清单
     */
    @ApiModelProperty("门店清单")
    @Excel(name = "门店清单", orderNum = "12", height = 20, width = 20)
    private String storeList;
    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    @Excel(name = "商品编码", orderNum = "13", height = 20, width = 20)
    private String itemCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    @Excel(name = "商品名称", orderNum = "14", height = 20, width = 20)
    private String itemName;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    @Excel(name = "单位", orderNum = "15", height = 20, width = 20)
    private String itemUnitName;

    /**
     * 原档案零售价
     */
    @Excel(name = "原档案零售价", orderNum = "16", height = 20, width = 20)
    private BigDecimal itemArchiveRetailPrice;
    /**
     * 新零售价
     */
    @Excel(name = "新零售价", orderNum = "17", height = 20, width = 20)
    private BigDecimal itemRetailPriceNew;
    /**
     * 折扣率
     */
    @Excel(name = "折扣率", orderNum = "18", height = 20, width = 20)
    private BigDecimal discountRate;
    /**
     * 竞争对手售价
     */
    @Excel(name = "竞争对手售价", orderNum = "19", height = 20, width = 20)
    private BigDecimal itemCompetitorPrice;

}