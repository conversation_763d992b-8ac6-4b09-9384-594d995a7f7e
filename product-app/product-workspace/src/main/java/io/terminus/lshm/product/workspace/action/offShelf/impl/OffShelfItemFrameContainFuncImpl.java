package io.terminus.lshm.product.workspace.action.offShelf.impl;

import io.terminus.draco.web.autoconfig.config.LoginConfigProperties;
import io.terminus.lshm.product.workspace.action.offShelf.OffShelfItemFrameContainFunc;
import io.terminus.lshm.product.workspace.model.OffShelfItemCommonVO;
import io.terminus.trantor.module.base.model.result.StringResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.context.TContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.Objects;
import java.util.Optional;


@FunctionImpl
@RequiredArgsConstructor
@Slf4j
public class OffShelfItemFrameContainFuncImpl implements OffShelfItemFrameContainFunc {

    private final LoginConfigProperties loginConfigProperties;


    @Value("${offShelf.frontUrl}")
    private String url;

    @Override
    public StringResult execute(OffShelfItemCommonVO offShelfItemCommonVO) {
        //获取用户token
        Optional<String> cookie = TContext.getCookie(loginConfigProperties.getTokenKey());
        String result = url+cookie.get();
        if(Objects.nonNull(offShelfItemCommonVO) && Objects.nonNull(offShelfItemCommonVO.getId())){
            result+="#/approval-detail?id="+offShelfItemCommonVO.getId()+"&type=approval";
        }
        return new StringResult(result);
    }



}
