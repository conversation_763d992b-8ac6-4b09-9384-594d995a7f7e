package io.terminus.lshm.product.workspace.converter.adjustprice;

import io.terminus.lshm.product.facade.price.dto.AdjustPriceExcelDTO;
import io.terminus.lshm.product.workspace.excel.dto.AdjustNationalPriceExcel;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public interface AdjustNationalPriceExcelConverter {

    default List<AdjustNationalPriceExcel> convert(List<AdjustPriceExcelDTO> list) {
       List<AdjustNationalPriceExcel> adjustNationalPriceExcelList = new ArrayList<>();
        list.forEach(excelDTO -> {
            AdjustNationalPriceExcel adjustPriceDTO = new AdjustNationalPriceExcel();
            adjustPriceDTO.setTitile(excelDTO.getTitile());
            adjustPriceDTO.setCategory(excelDTO.getCategory());
            adjustPriceDTO.setAdjustStatus(excelDTO.getAdjustStatus());
            adjustPriceDTO.setCreatedAt(excelDTO.getCreatedAt());
            adjustPriceDTO.setCreatedName(excelDTO.getCreatedName());
            adjustPriceDTO.setDeliveryTime(excelDTO.getDeliveryTime());
            adjustPriceDTO.setEffectiveType(excelDTO.getEffectiveType());
            adjustPriceDTO.setRetailTime(excelDTO.getRetailTime());
            adjustPriceDTO.setRetailEffectiveType(excelDTO.getRetailEffectiveType());
            adjustPriceDTO.setMainTime(excelDTO.getMainTime());
            adjustPriceDTO.setMainEffectiveType(excelDTO.getMainEffectiveType());
            adjustPriceDTO.setAdjustNotice(excelDTO.getAdjustNotice());
            adjustPriceDTO.setReason(excelDTO.getReason());
            adjustPriceDTO.setRemark(excelDTO.getRemark());
            adjustPriceDTO.setStoreList(excelDTO.getStoreList());
            adjustPriceDTO.setItemCode(excelDTO.getItemCode());
            adjustPriceDTO.setItemName(excelDTO.getItemName());
            adjustPriceDTO.setItemUnitName(excelDTO.getItemUnitName());
            adjustPriceDTO.setItemArchiveDeliveryPrice(excelDTO.getItemArchiveDeliveryPrice());
            adjustPriceDTO.setItemDeliveryPriceNew(excelDTO.getItemDeliveryPriceNew());
            adjustPriceDTO.setItemArchiveRetailPrice(excelDTO.getItemArchiveRetailPrice());
            adjustPriceDTO.setItemRetailPriceNew(excelDTO.getItemRetailPriceNew());
            adjustPriceDTO.setItemArchiveMemberPrice(excelDTO.getItemArchiveMemberPrice());
            adjustPriceDTO.setItemMemberPriceNew(excelDTO.getItemMemberPriceNew());
            adjustPriceDTO.setItemArchiveDeliveryPriceNew(excelDTO.getItemArchiveDeliveryPriceNew());
            adjustPriceDTO.setItemArchiveRetailPriceNew(excelDTO.getItemArchiveRetailPriceNew());
            adjustPriceDTO.setItemArchiveMemberPriceNew(excelDTO.getItemArchiveMemberPriceNew());
            adjustNationalPriceExcelList.add(adjustPriceDTO);
        });


        return adjustNationalPriceExcelList;
    }

}
