package io.terminus.lshm.product.workspace.controller;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.lshm.ncc.response.TreeNccGoodsTypeResponse;
import io.terminus.lshm.ncc.response.TreeNccNewDisplayResponse;
import io.terminus.lshm.product.common.offshelf.response.EmployeeUserResponse;
import io.terminus.lshm.product.common.recordfiling.dto.EmployeeTO;
import io.terminus.lshm.product.common.recordfiling.request.*;
import io.terminus.lshm.product.common.recordfiling.response.*;
import io.terminus.lshm.product.facade.recordfiling.ObtainTheCommoditySpecifications;
import io.terminus.lshm.product.facade.recordfiling.RecordFilingItemTaskReadFacade;
import io.terminus.lshm.product.facade.recordfiling.RecordFilingItemTaskWriteFacade;
import io.terminus.lshm.product.workspace.excel.service.ExportRecordFilingItemTaskrService;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@Slf4j
@RequestMapping(path = "/product2/api/record_filing_item_task/")
@Api(tags = "商品建档任务接口")
public class RecordFilingItemTaskController {

    @Resource
    private RecordFilingItemTaskReadFacade recordFilingItemTaskReadFacade;
    @Resource
    private RecordFilingItemTaskWriteFacade recordFilingItemTaskWriteFacade;
    @Resource
    private ObtainTheCommoditySpecifications obtainTheCommoditySpecifications;
    @Resource
    private ExportRecordFilingItemTaskrService exportRecordFilingItemTaskrService;

    /**
     * 分页查询任务
     *
     * @param request 查询请求
     * @return 分页响应
     */
    @ApiOperation("分页查询建档任务")
    @PostMapping(path = "/page")
    public Response<Paging<RecordFilingItemTaskPageResponse>> pageRecordFilingItemTask(@RequestBody RecordFilingItemTaskPageRequest request) {
        return recordFilingItemTaskReadFacade.pageRecordFilingItemTask(request);
    }

    /**
     * 查询商品建档详情
     *
     * @param id
     * @return
     */
    @ApiOperation("查询商品建档详情")
    @PostMapping(path = "/get")
    public Response<RecordFilingItemTaskResponse> detail(@RequestParam Long id) {
        return recordFilingItemTaskReadFacade.detail(id);
    }


    @ApiOperation("获取商品规格选项")
    @PostMapping(path = "/itemSpecOptions")
    public Response<Paging<ObtainTheCommoditySpecificationsPageResponse>> getItemSpecOptions(@RequestBody ObtainTheCommoditySpecificationsPageRequest request) {
        return obtainTheCommoditySpecifications.pageObtainTheCommoditySpecifications(request);
    }


    @ApiOperation("保存商品建档")
    @PostMapping(path = "/save")
    public Response<Long> saveRecordFilingItemTask(@RequestBody RecordFilingItemTaskRequest request) {
        request.checkParam();
        return recordFilingItemTaskWriteFacade.saveRecordFilingItemTask(request);
    }

    /**
     * 分页查询任务
     *
     * @param request 查询请求
     * @return 分页响应
     */
    @ApiOperation("上新查询建档列表")
    @PostMapping(path = "/newArrivalGetList")
    public Response<List<RecordFilingItemTaskListReaponse>> newArrivalGetList(@RequestBody RecordFilingItemTaskPageRequest request) {
        return recordFilingItemTaskReadFacade.newArrivalGetList(request);
    }

    @ApiOperation("提交审批")
    @PostMapping(path = "/submit")
    public Response<Long> submit(@RequestBody RecordFilingItemTaskRequest request) {
        request.checkParam();
        return recordFilingItemTaskWriteFacade.submit(request);
    }


    @ApiOperation("查询税率")
    @PostMapping(path = "/selectTaxRate")
    public Response<List<TaxRateResponse>> selectTaxRate(@RequestBody TaxRateRequest request) {
        return recordFilingItemTaskReadFacade.selectTaxRate(request);
    }

    @ApiOperation("审批商品建档任务")
    @PostMapping(path = "/audit")
    public Response<Boolean> audit(@RequestBody RecordFilingTaskAuditRequest request) {
        request.checkParam();
        return recordFilingItemTaskWriteFacade.audit(request);
    }

    /**
     * 导出
     *
     * @param request 导出
     * @return
     */
    @ApiOperation("建档列表导出")
    @PostMapping(path = "/export")
    public Response<BooleanResult> export(@RequestBody RecordFilingItemTaskExportRequest request) {
        BooleanResult result = exportRecordFilingItemTaskrService.export(request);
        return Response.ok(result);
    }


    @ApiOperation("查询产品经理")
    @PostMapping(path = "/selectProductManager")
    public Response<List<EmployeeUserResponse>> selectProductManager() {
        return recordFilingItemTaskReadFacade.selectProductManager();
    }


    @ApiOperation("查询新陈列分类")
    @PostMapping(path = "/nccNewDisplayCategory")
    public Response<List<TreeNccNewDisplayResponse>> nccNewDisplayCategory() {
        return recordFilingItemTaskReadFacade.nccNewDisplayCategory();
    }

    @ApiOperation("查询商品分类")
    @PostMapping(path = "/goodsTypeList")
    public Response<List<TreeNccGoodsTypeResponse>> NccGoodsTypeResponse() {
        return recordFilingItemTaskReadFacade.goodsTypeList();
    }

    @ApiOperation("草稿箱批量提交")
    @PostMapping(path = "/draftBatchSubmit")
    public Response<Boolean> draftBatchSubmit(@RequestBody DraftBatchSubmitRequest request) {
        if (ObjectUtil.isEmpty(request.getDraftIdList())) {
            throw new BusinessException("提交数据不能为空");
        }
        if (request.getDraftIdList().size() > 10) {
            throw new BusinessException("提交数据不能大于10条");
        }
        return recordFilingItemTaskWriteFacade.draftBatchSubmit(request);
    }

    /**
     * 分页查询用户中心员工列表
     *
     * @param request
     * @return
     */
    @ApiOperation("分页查询用户中心员工列表")
    @PostMapping(path = "/findEmployeePage")
    public Response<Paging<EmployeeTO>> findEmployeePage(@RequestBody EmployeePageRequest request) {
        return recordFilingItemTaskReadFacade.findEmployeePage(request);

    }

    @ApiOperation("列表我的审批")
    @PostMapping(path = "/myApprovals")
    public Response<MyApprovalResponse> myApprovals(){
        return recordFilingItemTaskReadFacade.myApprovals();
    }
}