package io.terminus.lshm.product.workspace.excel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 商品下架列表导出配置
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "item.export.template.offshelfitemprocess")
@Configuration
public class ExportOffShelfItemTaskProcessProperties {
    private String id;
    private String key;
}