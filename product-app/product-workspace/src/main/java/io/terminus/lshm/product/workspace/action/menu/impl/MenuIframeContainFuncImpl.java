package io.terminus.lshm.product.workspace.action.menu.impl;

import io.terminus.draco.web.autoconfig.config.LoginConfigProperties;
import io.terminus.lshm.product.common.enums.BusinessTypeEnum;
import io.terminus.lshm.product.workspace.action.menu.MenuIframeContainFunc;
import io.terminus.lshm.product.workspace.model.MenuIframeCommonVO;
import io.terminus.trantor.module.base.model.result.StringResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.context.TContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.Objects;
import java.util.Optional;


@FunctionImpl
@RequiredArgsConstructor
@Slf4j
public class MenuIframeContainFuncImpl implements MenuIframeContainFunc {

    private final LoginConfigProperties loginConfigProperties;


    @Value("${offShelf.frontUrl}")
    private String url;

    @Override
    public StringResult execute(MenuIframeCommonVO menuIframeCommonVO) {
        //获取用户token
        Optional<String> cookie = TContext.getCookie(loginConfigProperties.getTokenKey());
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getByCode(menuIframeCommonVO.getBusinessType());
        String result = url+cookie.get();
        if(Objects.nonNull(menuIframeCommonVO) && Objects.nonNull(menuIframeCommonVO.getId())){
            result+="#/"+businessTypeEnum.getPcDetailUrl()+menuIframeCommonVO.getId();
        }else {
            result+="#/"+businessTypeEnum.getPcUrl();
        }
        return new StringResult(result);
    }



}
