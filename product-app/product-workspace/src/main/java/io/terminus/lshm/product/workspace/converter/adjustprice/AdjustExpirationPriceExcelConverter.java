package io.terminus.lshm.product.workspace.converter.adjustprice;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import io.terminus.lshm.product.facade.price.dto.AdjustPriceExcelDTO;
import io.terminus.lshm.product.workspace.excel.dto.AdjustExpirationPriceExcel;
import io.terminus.lshm.product.workspace.excel.dto.FileDTO;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public interface AdjustExpirationPriceExcelConverter {

    default List<AdjustExpirationPriceExcel> convert(List<AdjustPriceExcelDTO> list) {
        List<AdjustExpirationPriceExcel> adjustExpirationPriceExcelList = new ArrayList<>();
        list.forEach(excelDTO -> {
            AdjustExpirationPriceExcel adjustPriceDTO = new AdjustExpirationPriceExcel();
            adjustPriceDTO.setTitile(excelDTO.getTitile());
            adjustPriceDTO.setAdjustStatus(excelDTO.getAdjustStatus());
            adjustPriceDTO.setCreatedAt(excelDTO.getCreatedAt());
            adjustPriceDTO.setCreatedName(excelDTO.getCreatedName());
            adjustPriceDTO.setRetailEffectiveType(excelDTO.getRetailEffectiveType());
            adjustPriceDTO.setReason(excelDTO.getReason());
            adjustPriceDTO.setRemark(excelDTO.getRemark());
            adjustPriceDTO.setStoreList(excelDTO.getStoreList());
            adjustPriceDTO.setItemCode(excelDTO.getItemCode());
            adjustPriceDTO.setItemName(excelDTO.getItemName());
            adjustPriceDTO.setSaleItemUnitName(excelDTO.getSaleItemUnitName());
            adjustPriceDTO.setItemArchiveRetailPrice(excelDTO.getItemArchiveRetailPrice());
            adjustPriceDTO.setItemRetailPriceNew(excelDTO.getItemRetailPriceNew());
            adjustPriceDTO.setDiscountRate(excelDTO.getDiscountRate());
            adjustPriceDTO.setProductionDateStart(excelDTO.getProductionDateStart());
            adjustPriceDTO.setExpiration(excelDTO.getExpiration());
            adjustPriceDTO.setInventoryQuantity(excelDTO.getInventoryQuantity());
            adjustPriceDTO.setItemUnitName(excelDTO.getItemUnitName());
            if (ObjectUtil.isNotEmpty(excelDTO.getFileUrl())) {
                List<String> fileUrlList = new ArrayList<>();
                List<FileDTO> files = JSONUtil.toList(excelDTO.getFileUrl(), FileDTO.class);
                for (FileDTO fileDTO : files) {
                    fileUrlList.add(fileDTO.getUrl());
                }
                adjustPriceDTO.setFileUrl(String.join(",", fileUrlList));
            }
            adjustExpirationPriceExcelList.add(adjustPriceDTO);
        });


        return adjustExpirationPriceExcelList;
    }

}
