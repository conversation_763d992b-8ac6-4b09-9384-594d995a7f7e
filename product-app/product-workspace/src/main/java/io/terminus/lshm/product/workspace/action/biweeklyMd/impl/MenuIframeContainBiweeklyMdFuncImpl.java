package io.terminus.lshm.product.workspace.action.biweeklyMd.impl;

import io.terminus.draco.web.autoconfig.config.LoginConfigProperties;
import io.terminus.lshm.product.workspace.action.biweeklyMd.MenuIframeContainBiweeklyMdFunc;
import io.terminus.lshm.product.workspace.model.biweeklyMdTag.BiweeklyMdTagVO;
import io.terminus.trantor.module.base.model.result.StringResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.context.TContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.Objects;
import java.util.Optional;


@FunctionImpl
@RequiredArgsConstructor
@Slf4j
public class MenuIframeContainBiweeklyMdFuncImpl implements MenuIframeContainBiweeklyMdFunc {

    private final LoginConfigProperties loginConfigProperties;


    @Value("${offShelf.frontUrl}")
    private String url;


    @Override
    public StringResult execute(BiweeklyMdTagVO biweeklyMdTagVO) {
        //获取用户token
        Optional<String> cookie = TContext.getCookie(loginConfigProperties.getTokenKey());
        String result = url+cookie.get();
        if(Objects.nonNull(biweeklyMdTagVO) && Objects.nonNull(biweeklyMdTagVO.getId())){
            //查询列表的接口地址拼接
            result+="#/biweeklyMDList?id="+biweeklyMdTagVO.getId()+"&type=approval";
        }
        return new StringResult(result);
    }
}
