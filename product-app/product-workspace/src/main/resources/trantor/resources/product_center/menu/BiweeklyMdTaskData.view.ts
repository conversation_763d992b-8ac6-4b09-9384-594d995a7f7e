import { Controller, state } from 'nusi-sdk';

interface ExportMessageData {
    type: 'export';
    data: any;
}

export default class extends Controller {
    @state pageUrl = "";
    private static isListenerActive = false;
    private static listenerRefCount = 0;
    private static sharedHandler?: (event: MessageEvent<ExportMessageData>) => void;

    private testurls = [
        "https://product-center-pc-test.noprod.hnlshm.com",
        "https://product-center-pc.noprod.hnlshm.com",
        "https://product-center-pc.prod.hnlshm.com"
    ];

    private handleMessage = (event: MessageEvent<ExportMessageData>) => {
        try {
            if (this.testurls.includes(event.origin) && event.data?.type === 'export') {
                console.log("Received export message", event);
                this.openView('import_export_center_ModelImportExportTO_ExportList', {
                    openViewType: 'Self',
                    env: event.data.data
                });
            }
        } catch (error) {
            console.error('Error handling message:', error);
        }
    }

    getPageUrl() {
        // 其他初始化逻辑...
        let record={businessType:"BIWEEKLY_MD"};
        if (this.pageContext.record) {
            record = {...record,...this.pageContext.record};
        }
        this.triggerLogicFunction("product_center_MenuIframeContainFunc", record)
            .then((res) => {
                this.pageUrl = res?.value
            });

    }

    pageDidLoad() {
        const elements = document.querySelectorAll('div.pk-page-header.pk-page-header-gray.trantor-page-header');
        elements.forEach((element) => {
            element.style.display = 'none';
        });
        this.getPageUrl();
        // 初始化共享监听器
        if (!this.constructor.isListenerActive) {
            console.log("Initializing message listener");
            this.constructor.sharedHandler = this.handleMessage.bind(this);
            window.addEventListener('message', this.constructor.sharedHandler);
            this.constructor.isListenerActive = true;

        }
        this.constructor.listenerRefCount++;
    }

    // 如果有对应的销毁生命周期
    dispose() {
        this.constructor.listenerRefCount--;
        if (this.constructor.listenerRefCount <= 0 &&
            this.constructor.isListenerActive &&
            this.constructor.sharedHandler) {

            window.removeEventListener('message', this.constructor.sharedHandler);
            this.constructor.isListenerActive = false;
            this.constructor.sharedHandler = undefined;
        }
    }
}
