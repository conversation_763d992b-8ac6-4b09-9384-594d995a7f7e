<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.terminus.lshm</groupId>
        <artifactId>product-app</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>product-workspace</artifactId>

    <properties>
        <trantor.metastore.url>
            ${metastore.url}
        </trantor.metastore.url>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor</groupId>
            <artifactId>trantor-api</artifactId>
        </dependency>
        <!-- 数据权限切面服务接口依赖-->
<!--        <dependency>-->
<!--            <groupId>io.terminus.lshm</groupId>-->
<!--            <artifactId>lshm-column-permission</artifactId>-->
<!--            <version>1.0.1.LSHM-DEV-SNAPSHOT</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>store-inner-api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>inventory-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>lshm-workspace-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor</groupId>
            <artifactId>trantor-runtime</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.platform</groupId>
                    <artifactId>autumn-sdk-v2-embedded</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>feature-job</artifactId>
                    <groupId>io.terminus.trantor</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>4.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>item-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>common-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>common-api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>quickbi-openapi-client</artifactId>
                    <groupId>com.alibaba.quickbi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>quickbi-openapi-common</artifactId>
                    <groupId>com.alibaba.quickbi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>item-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-ncc-spring-boot-starter</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>organization-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>io.terminus.trantor</groupId>
                <artifactId>metadata-maven-plugin</artifactId>
                <version>${trantor.version}</version>
                <configuration>
                    <moduleKey>product_center</moduleKey>
                    <repositoryUrl>${trantor.metastore.url}</repositoryUrl>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>deploy</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
