package io.terminus.lshm.open.product.controller.req;

import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

import java.io.Serializable;

// 嵌套类：规格信息
@Data
public  class SaleArchiveTaskItemSpec  extends AbstractRequest {
    private String unitName; // 单位名称
    private String itemNumber; // 行号
    private String conversionFactor; // 换算系数
    private String description; // 规格说明
    private String unitType; // 单位类型
    private Integer sellingUnits; // 是否售卖单位
    private String sellingConditions; // 售卖条件
    private String barCode; // 商品条形码
    private String purchasingUnit; // 采购单位
    private String inventoryUnit; // 库存单位
    private String salesUnit; // 销售单位

    @Override
    public void checkParam() {
        ParamUtil.notBlank(unitName, "单位不能为空");
        ParamUtil.notBlank(conversionFactor, "个数不能为空");
        ParamUtil.notBlank(description, "规格说明不能为空");
        ParamUtil.notBlank(unitType, "单位类型不能为空");
        ParamUtil.nonNull(sellingUnits, "是否售卖单位不能为空");
    }
}