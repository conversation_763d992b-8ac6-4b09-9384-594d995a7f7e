package io.terminus.lshm.open.product.controller.req;

import io.swagger.annotations.ApiModel;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.api.utils.StringUtil;
import io.terminus.lshm.product.common.recordfiling.request.BatchRecordFilingItemTaskRequest;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskSpecificationPriceTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/16
 * @Version 1.0
 */
@Slf4j
@Data
@ApiModel("创建供应商建档入参")
public class SupplierBatchCreateRequest extends AbstractRequest {

    // 供应商账号
    private String toElsAccount;

    // 建档任务编码
    private String archiveTaskCode;

    // 供应商ERP编码
    private String supplierCode;

    // 供应商名称
    private String supplierName;

    // 商品信息列表
    private List<SaleArchiveTaskItem> saleArchiveTaskItemList;

    @Override
    public void checkParam() {
        ParamUtil.notBlank(supplierName, "供应商名称不能为空");
        ParamUtil.notBlank(supplierCode, "供应商ERP编码不能为空");
        ParamUtil.notEmpty(saleArchiveTaskItemList, "建档商品信息不能为空");
        ParamUtil.expectFalse(saleArchiveTaskItemList.size() > 10, "建档商品信息超出最大限制不能为空");
        saleArchiveTaskItemList.forEach(item -> {
            item.checkParam();
        });

    }

    private static final String REGEX = "^-?\\d+(\\.\\d+)?$";

    private static BigDecimal isNumeric(String str) {
        if (str == null || !str.matches(REGEX)) {
            log.error("str is null:{}", str);
            return null;
        }
        return new BigDecimal(str);
    }

    private static Integer safeIntegerValueOf(String str) {
        if (str == null || !str.matches("^-?\\d+$")) {
            log.warn("Invalid input for Integer conversion: {}", str);
            return null;
        }
        return Integer.valueOf(str);
    }


    public List<BatchRecordFilingItemTaskRequest> srmRequest2TaskList() {
        List<BatchRecordFilingItemTaskRequest> list = new ArrayList<>();
        List<RecordFilingItemTaskSpecificationPriceTO> itemSpecificationList;
        RecordFilingItemTaskSpecificationPriceTO recordFilingItemTaskSpecificationPrice;
        BatchRecordFilingItemTaskRequest itemTaskRequest;
        List<SaleArchiveTaskItem> saleArchiveTaskItemList = this.getSaleArchiveTaskItemList();
        for (SaleArchiveTaskItem saleArchiveTaskItem : saleArchiveTaskItemList) {
            itemTaskRequest = new BatchRecordFilingItemTaskRequest();
            itemSpecificationList = new ArrayList<>();
            itemTaskRequest.setSupplierCode(this.getSupplierCode());
            itemTaskRequest.setSupplierName(this.getSupplierName());
            itemTaskRequest.setBrandName(saleArchiveTaskItem.getBrandName());
            itemTaskRequest.setManufacturerName(saleArchiveTaskItem.getManufacturerName());
            itemTaskRequest.setItemName(saleArchiveTaskItem.getTradeName());
            itemTaskRequest.setGroupName(saleArchiveTaskItem.getCombinationName());
            itemTaskRequest.setTasteName(saleArchiveTaskItem.getTasteName());
            itemTaskRequest.setIsFood(saleArchiveTaskItem.getFood());
            itemTaskRequest.setSellCondition(saleArchiveTaskItem.getSellingConditions());
            itemTaskRequest.setMinUnitWeight(isNumeric(saleArchiveTaskItem.getMinimumUnitWeight()));
            itemTaskRequest.setMinimumUnitWeightFlag(saleArchiveTaskItem.getMinimumUnitWeightFlag());
            itemTaskRequest.setFloatingPoint(isNumeric(saleArchiveTaskItem.getFloatingPoint()));
            itemTaskRequest.setSpecificationUnit(saleArchiveTaskItem.getSpecUnit());
            itemTaskRequest.setSpecificationQty(saleArchiveTaskItem.getSpecQuantity());
            itemTaskRequest.setBarCodeList(saleArchiveTaskItem.getOuterBoxBarcode());
            itemTaskRequest.setShelfLifeDays(Long.valueOf(saleArchiveTaskItem.getQualityGuaranteePeriod()));
            itemTaskRequest.setIsCustomizePackage(saleArchiveTaskItem.getCustomizedPackagingMaterial());
            itemTaskRequest.setLength(isNumeric(saleArchiveTaskItem.getOuterBoxLength()));
            itemTaskRequest.setWidth(isNumeric(saleArchiveTaskItem.getOuterBoxWidth()));
            itemTaskRequest.setHeight(isNumeric(saleArchiveTaskItem.getOuterBoxHeight()));
            if (StringUtil.isNotBlank(saleArchiveTaskItem.getGrossWeight())) {
                itemTaskRequest.setBoxWeight(isNumeric(saleArchiveTaskItem.getGrossWeight()));
            }
            itemTaskRequest.setIsWeightNonStandard(saleArchiveTaskItem.getNonStandardWeight());
            itemTaskRequest.setFiveHundredGramsMinValue(isNumeric(saleArchiveTaskItem.getMinimumQuantityOfFiveHundredGrams()));
            itemTaskRequest.setSpecification(saleArchiveTaskItem.getProductSpec());
            itemTaskRequest.setItemFullName(saleArchiveTaskItem.getProductName());
            itemTaskRequest.setTaxCode(saleArchiveTaskItem.getTaxCode());
            itemTaskRequest.setTaxRate(isNumeric(saleArchiveTaskItem.getMaterialTaxRate()));
            for (SaleArchiveTaskItemSpec saleArchiveTaskItemSpec : saleArchiveTaskItem.getSaleArchiveTaskItemSpecList()) {
                recordFilingItemTaskSpecificationPrice = new RecordFilingItemTaskSpecificationPriceTO();
                recordFilingItemTaskSpecificationPrice.setUnitName(saleArchiveTaskItemSpec.getUnitName());
                recordFilingItemTaskSpecificationPrice.setSpecificationRemark(saleArchiveTaskItemSpec.getDescription());
                recordFilingItemTaskSpecificationPrice.setConversionFactor(isNumeric(saleArchiveTaskItemSpec.getConversionFactor()));
                recordFilingItemTaskSpecificationPrice.setUnitType(saleArchiveTaskItemSpec.getUnitType());
                recordFilingItemTaskSpecificationPrice.setIsSellUnit(saleArchiveTaskItemSpec.getSellingUnits());
                recordFilingItemTaskSpecificationPrice.setSellCondition(saleArchiveTaskItemSpec.getSellingConditions());
                recordFilingItemTaskSpecificationPrice.setBarCodeList(saleArchiveTaskItemSpec.getBarCode());
                recordFilingItemTaskSpecificationPrice.setIsPurchaseUnit(safeIntegerValueOf(saleArchiveTaskItemSpec.getPurchasingUnit()));
                recordFilingItemTaskSpecificationPrice.setIsInventoryUnit(safeIntegerValueOf(saleArchiveTaskItemSpec.getInventoryUnit()));
                recordFilingItemTaskSpecificationPrice.setIsSaleUnit(safeIntegerValueOf(saleArchiveTaskItemSpec.getSalesUnit()));
                itemSpecificationList.add(recordFilingItemTaskSpecificationPrice);
            }
            itemTaskRequest.setItemSpecificationList(itemSpecificationList);
            list.add(itemTaskRequest);
        }
        return list;
    }
}
