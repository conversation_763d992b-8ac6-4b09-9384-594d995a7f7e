package io.terminus.lshm.open.product.controller;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.lshm.bizops.annotation.ResponseMethod;
import io.terminus.lshm.open.product.controller.req.SupplierBatchCreateRequest;
import io.terminus.lshm.product.facade.recordfiling.RecordFilingItemTaskWriteFacade;
import io.terminus.trantorframework.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/16
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping(path = "/open/api/srm")
@Api(tags = "供应商商品建档")
public class SrmOpenApi {

    @Autowired
    private RecordFilingItemTaskWriteFacade recordFilingItemTaskWriteFacade;

    @PostMapping("/supplier/batchCreateItem")
    @ApiOperation("创建")
    @ResponseMethod(bizDesc = "供应商商品建档", bizCode = "batchCreateItem")
    public Response<Boolean> batchCreateItem(@RequestBody SupplierBatchCreateRequest request) {
        log.info("== srm batchCreateItem req:{}", JSON.toJSONString(request));
        try {
            request.checkParam();
            return recordFilingItemTaskWriteFacade.batchCreateRecordFilingItemTask(request.srmRequest2TaskList());
        } catch (Exception e) {
            log.error("batchCreateItem error ",e);
            return Response.failure(e.getMessage());
        } catch (Throwable e){
            log.error("batchCreateItem error ",e);
            return Response.failure("服务异常，请联系中台业务管理员!");
        }
    }


}

