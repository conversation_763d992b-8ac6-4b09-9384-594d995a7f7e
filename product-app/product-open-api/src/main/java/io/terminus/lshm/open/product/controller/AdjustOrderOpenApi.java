package io.terminus.lshm.open.product.controller;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.lshm.bizops.annotation.ResponseMethod;
import io.terminus.lshm.open.product.controller.req.SupplierBatchCreateRequest;
import io.terminus.lshm.product.facade.price.AdjustPriceWriteFacade;
import io.terminus.lshm.product.facade.price.request.PromotionCampaignRequest;
import io.terminus.trantorframework.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/3
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping(path = "/open/api/prdouct/adjust/order")
@Api(tags = "供应商商品建档")
public class AdjustOrderOpenApi {

    @Autowired
    private AdjustPriceWriteFacade adjustPriceWriteFacade;


    @PostMapping("/createAdjustOrderFromPromotion")
    @ApiOperation("临期促销创建调价审批单据")
    @ResponseMethod(bizDesc = "临期促销创建调价审批单据", bizCode = "createAdjustOrderFromPromotion")
    public Response<Boolean> createAdjustOrderFromPromotion(@RequestBody PromotionCampaignRequest request) {
        log.info("== srm createAdjustOrderFromPromotion req:{}", JSON.toJSONString(request));
        try {
            request.checkParam();
            return adjustPriceWriteFacade.createAdjustOrderFromPromotion(request);
        } catch (Exception e) {
            log.error("createAdjustOrderFromPromotion error ",e);
            return Response.failure(e.getMessage());
        } catch (Throwable e){
            log.error("createAdjustOrderFromPromotion error ",e);
            return Response.failure("服务异常，请联系中台业务管理员!");
        }
    }

}
