package io.terminus.lshm.open.product.controller.req;

import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.api.utils.StringUtil;
import lombok.Data;

import java.util.List;

@Data
public class SaleArchiveTaskItem extends AbstractRequest {
    private String itemNumber; // 行号
    private String manufacturerName; // 厂商名
    private String brandName; // 品牌名
    private String tradeName; // 商品名
    private String combinationName; // 组合名
    private String tasteName; // 口味名
    private Integer food; // 是否为食品
    private String sellingConditions; // 售卖条件
    private String specQuantity; // 规格数量
    private String specUnit; // 规格单位
    private String outerBoxBarcode; // 外箱条码
    private String qualityGuaranteePeriod; // 保质期/天
    private Integer customizedPackagingMaterial; // 是否为定制包材
    private String outerBoxLength; // 外箱尺寸/米【长度（米）】
    private String outerBoxWidth; // 外箱尺寸/米【宽度（米）】
    private String outerBoxHeight; // 外箱尺寸/米【高度（米）】
    private String grossWeight; // 整箱重量/kg(含外箱)【箱重（kg)】
    private Integer nonStandardWeight; // 是否重量为非标
    private String minimumUnitWeight; // 最小单位重量
    private Integer minimumUnitWeightFlag; // 最小单位重量正负号标识 正：0 负：1
    private String floatingPoint; //浮点
    private String minimumQuantityOfFiveHundredGrams; // 500g数量（个）最小值
    private String productSpec; // 商品规格
    private String productName; // 商品名称
    private String taxCode; // 税收编码
    private String materialTaxRate; // 物料税率
    private List<SaleArchiveTaskItemSpec> saleArchiveTaskItemSpecList; // 规格信息列表

    @Override
    public void checkParam() {
        ParamUtil.notBlank(tradeName, "产品名不能为空");
        //ParamUtil.notBlank(manufacturerName, "厂商名不能为空");
        ParamUtil.notBlank(brandName, "品牌名不能为空");
        ParamUtil.nonNull(food, "是否为食品不能为空");
        ParamUtil.notBlank(sellingConditions, "售卖条件不能为空");
        ParamUtil.notBlank(qualityGuaranteePeriod, "保质期/天不能为空");
        ParamUtil.nonNull(customizedPackagingMaterial, "是否为定制包材不能为空");
        ParamUtil.notBlank(outerBoxLength, "外箱尺寸/米【长度（米）】不能为空");
        ParamUtil.notBlank(outerBoxWidth, "外箱尺寸/米【宽度（米）】不能为空");
        ParamUtil.notBlank(outerBoxHeight, "外箱尺寸/米【高度（米）】不能为空");
        ParamUtil.nonNull(nonStandardWeight, "是否重量为非标不能为空");
        if (nonStandardWeight != null && nonStandardWeight.equals(0)) {
            ParamUtil.notBlank(minimumUnitWeight, "最小单位重量不能为空");
            ParamUtil.notBlank(minimumQuantityOfFiveHundredGrams, "500g数量（个）最小值不能为空");
            if (minimumUnitWeightFlag != null && StringUtil.isBlank(floatingPoint)) {
                ParamUtil.nonNull(null, "浮点不能为空");
            }
            if (minimumUnitWeightFlag == null && StringUtil.isNotBlank(floatingPoint)) {
                ParamUtil.nonNull(null, "最小单位重量正负号标识不能为空");
            }
        }

        ParamUtil.notBlank(productSpec, "商品规格不能为空");
        ParamUtil.notBlank(productName, "商品名称不能为空");
        ParamUtil.notBlank(taxCode, "税收编码不能为空");
        ParamUtil.notBlank(materialTaxRate, "物料税率不能为空");
        ParamUtil.notEmpty(saleArchiveTaskItemSpecList, "规格信息列表不能为空");
        saleArchiveTaskItemSpecList.forEach(unitItem -> {
            unitItem.checkParam();
        });
    }
}