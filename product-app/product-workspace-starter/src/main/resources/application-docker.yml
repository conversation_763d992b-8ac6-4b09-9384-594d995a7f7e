trantor:
  mainModule: product_center

spring:
  application:
    name: product-workspace
  redis:
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DATABASE:10}
    password: ${REDIS_PASSWORD:}
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_ADDRESS:127.0.0.1:8848}
        namespace: ${NACOS_TENANT_ID:}
        enabled: ${DISCOVERY_ENABLE_NACOS:true}
    # 添加以下配置关闭主机名验证
    discovery:
      client:
        simple:
          hostname-verifier: none

logging:
  level:
    root: info
    io.terminus: info
    monitor: info

data.transfer:
  storage:
    active: oss
    local:
      baseDir: ~/data/
    oss:
      baseDir: data/
      endpoint: ${OSS_ENDPOINT}
      key: ${OSS_ACCESS_KEY_ID}
      secret: ${OSS_ACCESS_KEY_SECRET}
      bucket: ${OSS_BUCKET}
      privateBucket: ${OSS_PRIVATE_BUCKET}
product:
  export:
    saleTemplateId: ${product_EXPORT_SALE_TEMPLATE_ID:5b9e4cdcdf48fc92806176d0f4b89ded}
    saleTemplateKey: ${product_EXPORT_SALE_TEMPLATE_KEY:product_center_253a65d0-2509-4a95-978f-7ee08e63ef69}
    deliveryTemplateId: ${product_EXPORT_DELIVERY_TEMPLATE_ID:11980de79161e8cb0890dc0a90f4ff18}
    deliveryTemplateKey: ${product_EXPORT_DELIVERY_TEMPLATE_KEY:product_center_232d7114-19c4-4dcf-ae96-4245eada84cc}
    archivesTemplateId: ${product_EXPORT_ARCHIVES_TEMPLATE_ID:c062511e746a482cf294528191f60d09}
    archivesTemplatekey: ${product_EXPORT_ARCHIVES_TEMPLATE_KEY:product_center_7a158216-e4d9-4761-a6e6-fedf06c56cd4}

jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson
      limit: 100
      expireAfterAccessInMillis: 30000
  remote:
    default:
      type: redis
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:10}
      password: ${REDIS_PASSWORD:}

offShelf:
  frontUrl: ${OFF_SHELF_FRONT_URL:https://product-center-dev.noprod.hnlshm.com/}?token-key=




draco:
  web:
    login-config:
#      token-key: ${LOGIN_SHARE_COOKIE_NAME:trantor_workspace_dev}
#      token-type: JWT
#      pc-expire-time: ${LOGIN_SESSION_EXPIRE_TIME:3600}
#      token-storage: ${TOKEN_STORAGE:Cookie}
#      cookie-domain: ${COOKIE_DOMAIN:.noprod.hnlshm.com}
#      app-expire-time: ${LOGIN_APP_SESSION_EXPIRE_TIME:604800}
#      protocol: ${USER_CENTER_PROTOCOL:http}
      enable-authentication: ${ENABLE_AUTHENTICATION:true}
#      accessKey: ${DRACO_ACCESSKEY:}
#      user-find-by-token-url: ${UC_PROTOCOL:http}://${UC_FRONT_INNER_URL:${UC_FRONT_URL:127.0.0.1:8080}}/api/user/web/token
      path-white-list:
        - /product2/api/off-shelf-common/listAuditFlow
        - /product2/api/off-shelf-common/listOffShelfReason
        - /api/wx/redirect/url
#    swagger:
#      enable: ${SWAGGER_ENABLED:${ENABLE_SWAGGER:true}}
#    enable-response-wrapper: true
#    enable-exception-wrapper: true
#    enable-user-api: true
#    enable-sign-up-api: false
#    enable-qr-code: false
#    enable-login-api: true

off-shelf:
  notice:
    wxPath: ${OFF_SHELF_NOTICE_WX_PATH:}
    pcPath: ${OFF_SHELF_NOTICE_PC_PATH:https://test-trantor-portal-business-middleware.noprod.hnlshm.com}
    loginUrl: ${OFF_SHELF_LOGIN_URL:https://staging-uc-fe.noprod.hnlshm.com/login}?accountId=%s&sign=%s&appKey=%s&redirectUrl=%s
    app-key: ${OFF_SHELF_LOGIN_APPKEY:db23e16566ae40d3a7b3504d7f2c7445}
    app-secret: ${OFF_SHELF_LOGIN_APPSECRET:5712c11d030f4f2db4833e10e9458907}

item:
  export:
    template:
      productarchiving:
        id: ${ITEM_EXPORT_TEMPLATE_PRODUCT_ARCHING_ID:e2e5b9a4ebef874e9cec6bdf5fb881b9}
        key: ${ITEM_EXPORT_TEMPLATE_PRODUCT_ARCHING_KEY:product_center_1a1535d1-eb1e-469f-82da-1c6f7c4f8883}
      new-arrival:
        id: ${ITEM_EXPORT_TEMPLATE_NEW_ARRIVAL_ID:453d9ef83f37eb4f4c29279a4ddd5356}
        key: ${ITEM_EXPORT_TEMPLATE_NEW_ARRIVAL_KEY:product_center_f9904e1a-3818-4bd7-8d00-8ad85b9157eb}
      offshelfitem:
        id: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_ITEM_ID:a6c377711f65817cfed590f312198758}
        key: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_ITEM_KEY:product_center_bb56d1cb-1895-46d3-8081-c367ba153cb2}
      newproduct:
        id: ${ITEM_EXPORT_TEMPLATE_NEW_PRODUCT_ID:3db817adfeba95e68ba2d64fa12543da}
        key: ${ITEM_EXPORT_TEMPLATE_NEW_PRODUCT_KEY:item_center_f70d1913-c1bb-441a-b48a-04d190628575}
      offshelfitemdetail:
        id: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_DETAIL_ID:bc52f66e964a6768229ce643bc3809ca}
        key: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_DETAIL_KEY:product_center_84f677f2-e66d-4ecc-8256-99c6feabe70a}
      offshelfitemprocess:
        id: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_ID:7845abc339c7670ff51c831f75e61e6b}
        key: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_KEY:product_center_9f9a2571-2ee1-4484-b1f8-6b37b09cf17f}
      adjustprice:
        id: ${ITEM_EXPORT_TEMPLATE_ADJUST_PRICE_ID:69ea44990e1ae4377f318e95f628833d}
        key: ${ITEM_EXPORT_TEMPLATE_ADJUST_PRIC_KEY:product_center_6446ab6f-0b28-422f-93d5-1bdb34feff20}
      biweeklymdlist:
        id: ${ITEM_EXPORT_TEMPLATE_BIWEEKLY_MD_LIST_ID:d6be9e5f025f024311e31499891ea09b}
        key: ${ITEM_EXPORT_TEMPLATE_BIWEEKLY_MD_LIST_KEY:product_center_2dc61c7e-1059-421e-bb68-4ab92c18d0fa}
