trantor:
  mainModule: product_center

spring:
  application:
    name: product-workspace
  redis:
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DATABASE:10}
    password: ${REDIS_PASSWORD:}
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_ADDRESS:127.0.0.1:8848}
        namespace: ${NACOS_TENANT_ID:}
        enabled: ${DISCOVERY_ENABLE_NACOS:true}

logging:
  level:
    root: info
    io.terminus: info
    monitor: info

data.transfer:
  storage:
    active: oss
    local:
      baseDir: ~/data/
    oss:
      baseDir: data/
      endpoint: ${OSS_ENDPOINT}
      key: ${OSS_ACCESS_KEY_ID}
      secret: ${OSS_ACCESS_KEY_SECRET}
      bucket: ${OSS_BUCKET}
      privateBucket: ${OSS_PRIVATE_BUCKET}
product:
  export:
    saleTemplateId: ${product_EXPORT_SALE_TEMPLATE_ID:5b9e4cdcdf48fc92806176d0f4b89ded}
    saleTemplateKey: ${product_EXPORT_SALE_TEMPLATE_KEY:product_center_253a65d0-2509-4a95-978f-7ee08e63ef69}
    deliveryTemplateId: ${product_EXPORT_DELIVERY_TEMPLATE_ID:11980de79161e8cb0890dc0a90f4ff18}
    deliveryTemplateKey: ${product_EXPORT_DELIVERY_TEMPLATE_KEY:product_center_232d7114-19c4-4dcf-ae96-4245eada84cc}
    archivesTemplateId: ${product_EXPORT_ARCHIVES_TEMPLATE_ID:c062511e746a482cf294528191f60d09}
    archivesTemplatekey: ${product_EXPORT_ARCHIVES_TEMPLATE_KEY:product_center_7a158216-e4d9-4761-a6e6-fedf06c56cd4}

jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson
      limit: 100
      expireAfterAccessInMillis: 30000
  remote:
    default:
      type: redis
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:10}
      password: ${REDIS_PASSWORD:}

offShelf:
  frontUrl: ${OFF_SHELF_FRONT_URL:https://product-center-dev.noprod.hnlshm.com/}?token-key=




draco:
  web:
    login-config:
#      token-key: ${LOGIN_SHARE_COOKIE_NAME:trantor_workspace_dev}
#      token-type: JWT
#      pc-expire-time: ${LOGIN_SESSION_EXPIRE_TIME:3600}
#      token-storage: ${TOKEN_STORAGE:Cookie}
#      cookie-domain: ${COOKIE_DOMAIN:.noprod.hnlshm.com}
#      app-expire-time: ${LOGIN_APP_SESSION_EXPIRE_TIME:604800}
#      protocol: ${USER_CENTER_PROTOCOL:http}
      enable-authentication: ${ENABLE_AUTHENTICATION:true}
#      accessKey: ${DRACO_ACCESSKEY:}
#      user-find-by-token-url: ${UC_PROTOCOL:http}://${UC_FRONT_INNER_URL:${UC_FRONT_URL:127.0.0.1:8080}}/api/user/web/token
      path-white-list:
        - /product2/api/off-shelf-common/listAuditFlow
        - /product2/api/off-shelf-common/listOffShelfReason
        - /api/wx/redirect/url
#    swagger:
#      enable: ${SWAGGER_ENABLED:${ENABLE_SWAGGER:true}}
#    enable-response-wrapper: true
#    enable-exception-wrapper: true
#    enable-user-api: true
#    enable-sign-up-api: false
#    enable-qr-code: false
#    enable-login-api: true

off-shelf:
  notice:
    wxPath: ${OFF_SHELF_NOTICE_WX_PATH:}
    pcPath: ${OFF_SHELF_NOTICE_PC_PATH:https://test-trantor-portal-business-middleware.noprod.hnlshm.com}
    loginUrl: ${OFF_SHELF_LOGIN_URL:https://staging-uc-fe.noprod.hnlshm.com/login}?accountId=%s&sign=%s&appKey=%s&redirectUrl=%s
    app-key: ${OFF_SHELF_LOGIN_APPKEY:db23e16566ae40d3a7b3504d7f2c7445}
    app-secret: ${OFF_SHELF_LOGIN_APPSECRET:5712c11d030f4f2db4833e10e9458907}

item:
  export:
    template:
      productarchiving:
        id: ${ITEM_EXPORT_TEMPLATE_PRODUCT_ARCHING_ID:f6618b271a194797be524350584d7951}
        key: ${ITEM_EXPORT_TEMPLATE_PRODUCT_ARCHING_KEY:product_center_03673f7c-b7d1-4829-b55a-4795d3f7e99a}
      new-arrival:
        id: ${ITEM_EXPORT_TEMPLATE_NEW_ARRIVAL_ID:3133b88215e5471e6ef61105e3946de2}
        key: ${ITEM_EXPORT_TEMPLATE_NEW_ARRIVAL_KEY:product_center_72678b3d-b25c-4bf5-9190-539da49c1fd3}
      offshelfitem:
        id: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_ITEM_ID:df7301734e845232ce2112160baa8810}
        key: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_ITEM_KEY:product_center_fbf7f764-6037-43a4-81ee-32d39844776a}
      offshelfitemdetail:
        id: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_DETAIL_ID:1e9466ca36f0d63dd38b8cb28431c6d7}
        key: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_DETAIL_KEY:product_center_32f26b83-da1a-4859-9cdb-1b8960a09eda}
      offshelfitemprocess:
        id: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_ID:11995de5cdd78422031ab76268cac64e}
        key: ${ITEM_EXPORT_TEMPLATE_OFF_SHELF_KEY:product_center_027d5302-7465-42cd-bdd3-9131b4764a7a}
      adjustprice:
        id: ${ITEM_EXPORT_TEMPLATE_ADJUST_PRICE_ID:077c1f5dd27f74120da19d83aa8974e6}
        key: ${ITEM_EXPORT_TEMPLATE_ADJUST_PRIC_KEY:product_center_035ced7a-7536-47ed-a968-4e8943607917}