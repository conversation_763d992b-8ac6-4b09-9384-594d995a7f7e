package io.terminus.lshm.flow.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * 流程异常
 *
 * <AUTHOR>
 * @project product-flow
 * @date 2025-05-25
 */
@Getter
@Setter
public class FlowException extends RuntimeException {

    private final String errcode;

    private final String errmsg;


    public FlowException(String errcode, String errmsg) {
        super(errmsg);
        this.errcode = errcode;
        this.errmsg = errmsg;
    }

    public FlowException(FlowBpmExceptionCodeEnum flowBpmExceptionCodeEnum) {
        super(flowBpmExceptionCodeEnum.getErrmsg());
        this.errcode = flowBpmExceptionCodeEnum.getErrcode();
        this.errmsg = flowBpmExceptionCodeEnum.getErrmsg();
    }


}
