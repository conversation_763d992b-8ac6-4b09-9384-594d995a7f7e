package io.terminus.lshm.flow.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description
 * @Date 2025/7/8
 */
@Getter
public enum FlowBpmExceptionCodeEnum {
    BPM_FLOW_CREATE_ERROR("10001", "bpm路程创建失败"),
    BPM_FLOW_APPROVE_ERROR("10002", "bpm路程审核失败"),
    BPM_FLOW_CANCEL_ERROR("10003", "bpm作废失败"),
    BPM_FLOW_RESUBMIT_ERROR("10004", "bpm路程重新提交失败"),
    BPM_USER_LIST_INSTANCE_NOT_EXIST("10005", "bpm用户待办不存在"),
    BPM_LIST_INSTANCE_NOT_EXIST("10006", "bpm待办不存在");


    private final String errcode;

    private final String errmsg;

    FlowBpmExceptionCodeEnum(String errcode, String errmsg) {
        this.errcode = errcode;
        this.errmsg = errmsg;
    }






}
