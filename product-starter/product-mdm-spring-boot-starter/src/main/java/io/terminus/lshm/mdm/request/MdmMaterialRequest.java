package io.terminus.lshm.mdm.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 物料信息
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MdmMaterialRequest {

    /**
     * UUID
     */
    @JsonProperty("UUID")
    private String uuid;

    /**
     * MATERIAL 默认 MATERIAL
     */
    @JsonProperty("FORM_CODE")
    private String formCode = "MATERIAL";

    /**
     * 物料编码
     */
    @JsonProperty("MATERIAL_CODE")
    private String itemCode;

    /**
     * MDM 主数据代码  同物料编码
     */
    @JsonProperty("MDM_CODE")
    private String mdmCode;

    /**
     * 物料名称/商品名称
     */
    @JsonProperty("MDM_NAME")
    private String itemFullName;

    /**
     * 物料分类(物料基本分类编码或名称) 商品分类
     */
    @JsonProperty("MAT_BASIC_CLASS")
    private String goodsType;

    /**
     * 规格
     */
    @JsonProperty("MAT_SPECIFICATION")
    private String specification;

    /**
     * 商品条形码
     */
    @JsonProperty("MAT_BARCODE")
    private String barCodeList;

    /**
     * 主计量单位
     */
    @JsonProperty("MAIN_UNIT")
    private String unitType;

    /**
     * 商品部门(商品部门编码或名称)
     */
    @JsonProperty("MAT_DEPT")
    private String itemDepartment;

    /**
     * 拣货类型(拣货类型编码或名称)
     */
    @JsonProperty("PICK_TYPE")
    private String pickType;

    /**
     * 进项税(门店进销项税率编码或名称)
     */
    @JsonProperty("IN_TAX_TYPE")
    private String inputVat;

    /**
     * 销项税(门店进销项税率编码或名称)
     */
    @JsonProperty("SALE_TAX_TYPE")
    private String outputVat;

    /**
     * 物料简称 /  商品简称
     */
    @JsonProperty("MAT_SHORT_NAME")
    private String materialShortName;

    /**
     * 停售标记 默认 "N"
     */
    @JsonProperty("STOP_SALE_MARKS")
    private String def4 = "N";

    /**
     * 	允许前台折扣(Y/N)  默认 "Y"
     */
    @JsonProperty("ALLOW_FRONTEND_DISCOUNT")
    private String def6 = "Y";

    /**
     * 打印价签(Y/N) 默认 "Y"
     */
    @JsonProperty("PRINT_PRICE_TAG")
    private String  def11 = "Y";

    /**
     * 前台打印标签 默认 "Y"
     */
    @JsonProperty("FRONT_PRINT_TAG")
    private String def15 = "Y";

    /**
     * 允许商品调价 允许商品调价(Y/N)  默认 "Y"
     */
    @JsonProperty("ALLOW_PRODUCT_ADJUSTMENT")
    private String def16 = "Y";

    /**
     * 	打印短码 (Y/N)  默认 "N"
     */
    @JsonProperty("PRINT_SHORT_CODE")
    private String def13 = "Y";

    /**
     * 休眠 默认 "Y"
     */
    @JsonProperty("SLEEP")
    private String def27 = "Y";

    /**
     * 	VARCHAR(2)	是否积分
     */
    @JsonProperty("IS_POINTS")
    private String def37 = "Y";

    /**
     * 是否溯源 默认 "N"
     */
    @JsonProperty("TRACEABILITY")
    private String def29;

    /**
     * 长度（米）
     */
    @JsonProperty("LENGTH")
    private Double length;

    /**
     * 宽度（米）
     */
    @JsonProperty("WIDTH")
    private Double width;

    /**
     * 高度（米）
     */
    @JsonProperty("HEIGHT")
    private Double height;

    /**
     * 单位重量（kg）
     */
    @JsonProperty("UNIT_WEIGHT")
    private Double unitweight;

    /**
     * 500g数量（个）最小值
     */
    @JsonProperty("MIN_WEIGHT_HALFKG")
    private Double fiveHundredGramsMinValue;

    /**
     * 500g数量（个）最大值
     */
    @JsonProperty("MMAX_WEIGHT_HALFKG")
    private Double def67;

    /**
     * 最小单位重量
     */
    @JsonProperty("MIN_UNIT_WEIGHT")
    private Double minUnitWeight;

    /**
     * 箱重（kg）
     */
    @JsonProperty("CASE_WEIGHT")
    private Double boxWeight;

    /**
     * 允许退货(Y/N)  默认 "Y"
     */
    @JsonProperty("ALLOW_RETURN")
    private String def48 = "Y";

    /**
     * 单位体积（立方米）  计算
     */
    @JsonProperty("UNIT_VOLUME")
    private Double unitvolume;

    /**
     * 税收编码
     */
    @JsonProperty("TAX_CODE")
    private String taxCode;

    /**
     * 所属组织
     * AFFILIATED_ORGANIZATION
     */


    /**
     * DELETION_FLAG	VARCHAR(10)	删除标识
     * DELETION_TIMESTAMP	VARCHAR(10)	删除时间戳
     * AFFILIATED_GROUP	VARCHAR(10)	所属集团
     */

    /**
     * 物料税类
     */
    @JsonProperty("MATERIAL_TAX_CATEGORY")
    private String taxRateType;

    /**
     * 商品类型
     */
    @JsonProperty("PRODUCT_TYPE")
    private String itemType;

    /**
     * 商品标签
     * PRODUCT_TAG
     */

    /**
     * 助记码
     */
    @JsonProperty("MNEMONIC_CODE")
    private String helperCode;


    /**
     * 停购标记 默认 "N"
     */
    @JsonProperty("STOP_PURCHASE_FLAG")
    private String def5 = "N";

    // 1 / 0
    private Integer isWeighMark;

    public void setIsWeighMark(Integer isWeighMark) {
        this.isWeighMark = isWeighMark;
        if(isWeighMark != null) {
            this.isWeighMarkStr = isWeighMark == 1 ? "Y" : "N";
        }
    }

    /**
     * 商品称重标记 1:是,0:否 Y N
     */
    @JsonProperty("PRODUCT_WEIGHING_FLAG")
    private String isWeighMarkStr;

    private Integer isBigSingleProduct;

    public void setIsBigSingleProduct(Integer isBigSingleProduct) {
        this.isBigSingleProduct = isBigSingleProduct;
        if(isBigSingleProduct != null) {
            this.isBigSingleProductStr = isBigSingleProduct == 1 ? "Y" : "N";
        }

    }

    /**
     * 是否大单品 1:是,0:否
     */
    @JsonProperty("IS_MAJOR_PRODUCT")
    private String isBigSingleProductStr;





    /**
     * FRONTEND_NEGOTIATION	VARCHAR(2)	前台议价
     * ORIGIN	VARCHAR(20)	产地
     * ALLOW_ONLINE_SHIPMENT	VARCHAR(2)	允许在线发货
     * ALLOW_PRODUCT_ADJUSTMENT	VARCHAR(2)	允许商品调价
     * LEMENG_PRODUCT_CODE	VARCHAR(20)	乐檬商品编码
     * COMPLETE_SET	VARCHAR(2)	成套件
     * PRODUCT_CATEGORY	VARCHAR(20)	产品分类
     * UNIT_WEIGHT_G	VARCHAR(10)	单位克重
     * MAJOR_FACTORY	VARCHAR(2)	大厂造
     * BUSY	VARCHAR(2)	BUSY
     */


    /**
     * 中台商品类型(中台商品类型编码或名称)
     */
    @JsonProperty("MIDDLE_PLATFORM_PRODUCT_TYPE")
    private String ztItemTypeCode;

    /**
     * 启用状态(未启用=1,已启用=2,已停用=3)
     */
    @JsonProperty("STATUS")
    private String enablestate = "2";

    private Integer isOnlyImport;

    public void setIsOnlyImport(Integer isOnlyImport) {
        this.isOnlyImport = isOnlyImport;
        if(isOnlyImport != null) {
            this.isOnlyImportStr = isOnlyImport == 1 ? "Y" : "N";
        }
    }

    /**
     * 是否纯进口 1:是,0:否
     */
    @JsonProperty("PURE_IMPORT")
    private String isOnlyImportStr;

    /**
     * 内包装
     */
    @JsonProperty("INNER_PACKAGING")
    private String def52;

    /**
     * PURE_IMPORT	VARCHAR(2)	纯进口
     * PICTURE	TEXT	图片
     * NEW_END_DATE	DATETIME	新品截止日期
     * REMARK	VARCHAR(20)	备注
     * FREIGHT_COUNT	VARCHAR(20)	运费计件
     */

    /**
     * 巨沃商品类型
     */
    @JsonProperty("JUWO_PRODUCT_TYPE")
    private String jwItemTypeCode;

    /**
     * 供应商  传code
     */
    @JsonProperty("SUPPLIER")
    private String supplier;

    /**
     * 品牌code
     */
    @JsonProperty("BRAND_CODE")
    private String brandCode;

    /**
     * 口味
     */
    @JsonProperty("TASTE_NAME")
    private String tasteName;

    @JsonProperty("DISPLAY_CATEGROY")
    private String newDisplayCategoryCode;

    /**
     * 物料品牌信息
     */
    @JsonProperty("MAT_BRAND")
    private List<MdmMatBrandRequest> matBrandMdmList;

    /**
     * 组合物料明细
     */
    @JsonProperty("PORTFOLIO_MATERIAL")
    private List<MdmPortfolioMaterialRequest> portfolioMaterialMdmList;

    /**
     * 物料库存信息
     */
    @JsonProperty("MAT_STOCK")
    private List<MdmMatStockRequest> matStockMdmList;

    /**
     * 一品多码
     */
    @JsonProperty("MAT_BARCODES")
    private List<MdmMatBarcodesRequest> matBarcodesMdmList;

    /**
     * 采购信息
     */
    @JsonProperty("MAT_PURCHASE")
    private List<MdmMatPurchaseRequest> matPurchaseMdmList;

    /**
     * 是否为食品 1:是，0:否
     */
    @JsonProperty("IS_FOOD")
    private String isFood;

    public void setIsFood(String isFood) {
        this.isFood = "1".equals(isFood)? "Y" : "N";
    }

    /**
     * 是否特价 1:是，0:否
     */
    private Integer isSpecialPrice;

    public void setIsSpecialPrice(Integer isSpecialPrice) {
        this.isSpecialPrice = isSpecialPrice;
        if(isSpecialPrice != null) {
            this.isSperialPriceStr = isSpecialPrice == 1 ? "Y" : "N";
        }
    }

    @JsonProperty("IS_SPECIAL_PRICE")
    private String isSperialPriceStr;


    /**
     * 售卖条件 SINGLE:单个/单件,COMBINE:连包/组合,FCL:整箱/件,SCATTER :散称
     */
    @JsonProperty("SALE_CONDITION")
    private String sellCondition;

    /**
     * 品牌名称
     */
    @JsonProperty("BRAND_NAME")
    private String brandName;

    /**
     * 厂商名称
     */
    @JsonProperty("VENDOR_NAME")
    private String manufacturerName;

    /**
     * 税率
     */
    @JsonProperty("TAX_RATE")
    private BigDecimal taxRate;

    /**
     * 上新品牌
     */
    @JsonProperty("NEW_BRAND")
    private String addNewComingBrand;

    private Integer isExclusiveOperate;

    public void setIsExclusiveOperate(Integer isExclusiveOperate) {
        this.isExclusiveOperate = isExclusiveOperate;
        if(isExclusiveOperate != null) {
            this.isExclusiveOperateStr = isExclusiveOperate == 1 ? "Y" : "N";
        }
    }

    /**
     * 是否独家经营 1:是,0:否
     */
    @JsonProperty("IS_EXCLUSIVE_OPERATION")
    private String isExclusiveOperateStr;


    private Integer isWeightNonStandard;

    public void setIsWeightNonStandard(Integer isWeightNonStandard) {
        this.isWeightNonStandard = isWeightNonStandard;
        if(isWeightNonStandard != null) {
            this.isWeightNonStandardStr = isWeightNonStandard == 1 ? "Y" : "N";
        }
    }

    /**
     * 是否重量为非标 1:是,0:否
     */
    @JsonProperty("IS_NON_STANDARD_WEIGHT")
    private String isWeightNonStandardStr;

    private Integer isOldProduct;

    public void setIsOldProduct(Integer isOldProduct) {
        this.isOldProduct = isOldProduct;
        if(isOldProduct != null) {
            this.isOldProductStr = isOldProduct == 1 ? "Y" : "N";
        }
    }

    /**
     * 是否为老品 1:是,0:否
     */
    @JsonProperty("IS_OLD_PRODUCT")
    private String isOldProductStr;

    private Integer isThirdProduct;

    public void setIsThirdProduct(Integer isThirdProduct) {
        this.isThirdProduct = isThirdProduct;
        if(isThirdProduct != null) {
            this.isThirdProductStr = isThirdProduct == 1 ? "Y" : "N";
        }
    }

    /**
     * 是否为第三方产品 1:是,0:否
     */
    @JsonProperty("IS_THIRD_PARTY_PRODUCT")
    private String isThirdProductStr;

    /**
     * 产品卖点 PRODUCE_AREA:原料产地，CAPACITY_INTRODUCE:产能介绍，NO_ADDITION:产品0添加，PERFECT_MATERIAL:优质材料，GRADE_TEST:等级检测，OTHER:其他
     */
    @JsonProperty("PRODUCT_SELLING_POINT")
    private String productSellPointList;

    private Integer isReplaceNewComing;

    public void setIsReplaceNewComing(Integer isReplaceNewComing) {
        this.isReplaceNewComing = isReplaceNewComing;
        if(isReplaceNewComing != null) {
            this.isReplaceNewComingStr = isReplaceNewComing == 1 ? "Y" : "N";
        }
    }
    /**
     * 是否替换上新 1:是，0:否
     */
    @JsonProperty("IS_REPLACE_NEW")
    private String isReplaceNewComingStr;

    /**
     * 已在售品牌  赵一鸣:ZYM,零食很忙:LSHM
     */
    @JsonProperty("EXISTING_BRAND")
    private String onSaleBrand;

    /**
     * 替换下架商品名称集合
     */
    @JsonProperty("REPLACED_PRODUCT_NAME")
    private String replaceItemNameList;

    /**
     * 经营范围 NATIONAL:全国品, REGION:区域品
     */
    @JsonProperty("OPERATING_SCOPE")
    private String businessScope;

    public void setBusinessScope(String businessScope) {
        this.businessScope = "REGION".equals(businessScope)? "01" : "02";
    }

    private Integer isCustomizePackage;

    public void setIsCustomizePackage(Integer isCustomizePackage) {
        this.isCustomizePackage = isCustomizePackage;
        if(isCustomizePackage != null) {
            this.isCustomizePackageStr = isCustomizePackage == 1 ? "Y" : "N";
        }
    }

    /**
     * 是否为定制包装 1:是，0:否
     */
    @JsonProperty("is_customize_package")
    private String isCustomizePackageStr;


    private Integer isMiniShopPurchase;

    public void setIsMiniShopPurchase(Integer isMiniShopPurchase) {
        this.isMiniShopPurchase = isMiniShopPurchase;
        if(isMiniShopPurchase != null) {
            this.isCustomizePackageStr = isMiniShopPurchase == 1 ? "Y" : "N";
        }
    }

    /**
     * 迷你店是否去货 1:是,0:否
     */
    @JsonProperty("is_mini_shop_purchase")
    private String isMiniShopPurchaseStr;

    /**
     * 上新原因
     */
    @JsonProperty("NEW_REASON")
    private String newComingReason;

    private Long shelfLifeDays;

    public void setShelfLifeDays(Long shelfLifeDays) {
        this.shelfLifeDays = shelfLifeDays;
        if(shelfLifeDays != null) {
            this.shelfLifeDaysStr = shelfLifeDays + "";
        }
    }

    /**
     * 保质期
     */
    @JsonProperty("SHELF_LIFE")
    private String shelfLifeDaysStr;

    /**
     * PK_MATERIAL	VARCHAR(100)	下游主键
     * TEXT_DESCRIPTION	VARCHAR(20)	文字说明
     * MIN_UNIT_WEIGHT_SIGN	VARCHAR(20)	最小单位重量正负号标识:
     * COMBO_QUANTITY	VARCHAR(20)	连包数量/散称件数
     * COMBO_UNIT	VARCHAR(20)	连包单位
     * HAS_SPEC_SHEET	VARCHAR(2)	是否有产品规格书
     * TEXT_DESCRIPTION1	VARCHAR(20)	文字说明
     * SHARP_PACKAGING	VARCHAR(20)	包装锋利(割手）
     * BARCODE_WRINKLES	VARCHAR(20)	条码褶皱
     * DATE_HIDDEN	VARCHAR(20)	日期隐蔽
     * QC_LABEL	VARCHAR(20)	品控标签
     * QUALITY_INSPECTION	VARCHAR(20)	质检结论
     */
}
