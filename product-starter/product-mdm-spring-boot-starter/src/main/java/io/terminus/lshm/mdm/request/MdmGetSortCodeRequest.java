package io.terminus.lshm.mdm.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025.06.07
 * @desc 获取MDM分类编码请求体，目前已知计量单位、业务单元(组织)、商品分类、物料基本分类编码 都可使用该请求体
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MdmGetSortCodeRequest {

    @JsonProperty("UUID")
    private String uuid;

    @JsonProperty("PAGE")
    private String page = "1";

    @JsonProperty("PAGE_SIZE")
    private String pageSize = "10";

    @JsonProperty("FORM_CODE")
    private String formCode;

    @JsonProperty("DATA")
    private RequestData data;

    @Data
    public static class RequestData{

        @JsonProperty("MDM_NAME")
        private String mdmName;
    }
}
