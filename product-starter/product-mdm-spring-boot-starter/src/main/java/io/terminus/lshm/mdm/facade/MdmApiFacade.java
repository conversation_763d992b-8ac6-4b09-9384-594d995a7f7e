package io.terminus.lshm.mdm.facade;

import io.terminus.lshm.mdm.config.MdmFeignConfiguration;
import io.terminus.lshm.mdm.request.MdmCheckGoodsRequest;
import io.terminus.lshm.mdm.request.MdmGetSortCodeRequest;
import io.terminus.lshm.mdm.request.MdmSaveGoodsRequest;
import io.terminus.lshm.mdm.response.MdmCheckGoodsResponse;
import io.terminus.lshm.mdm.response.MdmGetSortCodeResponse;
import io.terminus.lshm.mdm.response.MdmSaveGoodsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @project product-flow
 * @date 2025-05-25
 *
 */
@FeignClient(name = "mdmApiFacade", contextId = "mdm-mdmApi", url = "${product.mdm.url:''}", configuration = MdmFeignConfiguration.EnableMdmAutoConfiguration.class)
public interface MdmApiFacade {

    /**
     * 获取物料基本分类编码 如：食品、酒水、饼干
     * @param request 请求体
     * @return 物料基本分类编码
     */
    @PostMapping(value = "/demdm-api/open/api/v2/selectApi/MATERIAL_CLASS")
    MdmGetSortCodeResponse getMdmMaterialCode(MdmGetSortCodeRequest request);

    /**
     * 获取计量单位编码 如：个、包、袋、盒、支、ml、L
     * @param request 请求体
     * @return 计量单位编码
     */
    @PostMapping(value = "/demdm-api/open/api/v2/selectApi/UNIT")
    MdmGetSortCodeResponse getMdmUnitCode(MdmGetSortCodeRequest request);

    /**
     * 获取业务单元（组织）编码 如：长沙很忙零食食品有限公司
     * @param request 请求体
     * @return 业务单元（组织）编码
     */
    @PostMapping(value = "/demdm-api/open/api/v2/selectApi/BUSINESS_UNIT")
    MdmGetSortCodeResponse getMdmOrgCode(MdmGetSortCodeRequest request);

    /**
     * 获取商品类型编码 如：成分商品、制单组合、组合商品、标准
     * @param request 请求体
     * @return 商品类型编码
     */
    @PostMapping(value = "/demdm-api/open/api/v2/selectApi/LOOKUP")
    MdmGetSortCodeResponse getMdmMatTypeCode(MdmGetSortCodeRequest request);

    /**
     * 建档商品推送MDM 保存物料(商品)信息到MDM
     * @param request 请求体
     * @return mdm返回商品编码
     */
    @PostMapping(value = "/demdm-back/custom/material/in/saveOrUpdate")
   // @PostMapping(value = "/demdm-api/open/api/v2/saveOrUpdateApi/MATERIAL")
    MdmSaveGoodsResponse saveMdmGoods(MdmSaveGoodsRequest request);

    /**
     * 校验商品数据是否满足推送MDM
     * @param request
     * @return
     */
    @PostMapping(value = "/demdm-back/custom/material/check")
    MdmCheckGoodsResponse checkMdmGoods(MdmCheckGoodsRequest request);

}
