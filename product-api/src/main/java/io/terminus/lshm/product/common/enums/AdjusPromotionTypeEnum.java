package io.terminus.lshm.product.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 促销类型
 *
 */
@Getter
@AllArgsConstructor
public enum AdjusPromotionTypeEnum {
    /**
     * 新店开业活动
     */
    NEW_STORE_OPENING("NEW_STORE_OPENING", "新店开业活动",1),
    /**
     * 外场大单品调价
     */
    OUTDOOR_MARKET("OUTDOOR_MARKET", "外场大单品调价",2),

    /**
     * 常规商品外场活动
     */
    REGULAR_OUTDOOR_ACTIVITIES("REGULAR_OUTDOOR_ACTIVITIES", "常规商品外场活动",3),

    /**
     * 竞争调价
     */
    COMPETITIVE_PRICE("COMPETITIVE_PRICE", "竞争调价",4);


    private String key;

    private String description;

    private int code;



    public static String getDescByType(String code) {
        for (AdjusPromotionTypeEnum value : values()) {
            if (value.getKey().equals(code)) {
                return value.getDescription();
            }
        }
        return null;
    }
    public static String getKeyByValue(int num) {
        for (AdjusPromotionTypeEnum value : values()) {
            if (value.getCode() == num) {
                return value.getKey();
            }
        }
        return null;
    }

    public static int getValueByKey(String key) {
        for (AdjusPromotionTypeEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getCode();
            }
        }
        return 0;
    }
}