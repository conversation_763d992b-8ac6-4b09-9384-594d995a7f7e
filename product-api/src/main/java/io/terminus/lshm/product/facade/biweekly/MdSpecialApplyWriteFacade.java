package io.terminus.lshm.product.facade.biweekly;


import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.trantorframework.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import static io.terminus.lshm.product.common.constants.ProductCenterConstant.*;

@FeignClient(name = SERVICE_ID, contextId = WRITE_MD_SPECIAL_APPLY_CONTEXT_ID)
public interface MdSpecialApplyWriteFacade {


    /**
     * 新增MD特殊申请（提交审批）
     * @param request 添加请求
     * @return 布尔
     * */
    @PostMapping("/api/biweeklyMd/addMdSpecialApply")
    Response<Boolean> addMdSpecialApply(@RequestBody FlowApplyBizRequest<MdSpecialApplyByRequest> request);



    /**
     * 再次提交对数据进行修改（只能更改申请理由，备注，附件信息和门店信息）
     * */
    @PostMapping("/api/biweeklyMd/updateMdSpecialApply")
    Response<Boolean> updateMdSpecialApply(@RequestBody FlowApplyBizRequest<MdSpecialApplyByRequest> request);

}
