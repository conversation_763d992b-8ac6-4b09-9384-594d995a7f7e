package io.terminus.lshm.product.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum ItemTypeEnum {

    PRICE_BAND_ITEM("PRICE_BAND_ITEM","系列商品"),
    COMMON_ITEM("COMMON_ITEM","常规商品"),
    WEIGHT_ITEM("WEIGHT_ITEM","称重商品"),
    COMBINE_ITEM("COMBINE_ITEM","组合商品"),
    VIRTUAL_ITEM("VIRTUAL_ITEM","虚拟商品"),
    SERVICE_ITEM("SERVICE_ITEM","服务商品"),
    GOODS_ITEM("GOODS_ITEM","物料商品"),
;
    private String type;

    private String desc;

    public static String getDescByType(String type){
        for (ItemTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return null;
    }


}
