package io.terminus.lshm.product.common.constants;


public interface ProductCenterConstant {
    String SERVICE_ID = "product-server";

    String CONTEXT_READ_ID = SERVICE_ID+"-read-";

    String CONTEXT_WRITE_ID = SERVICE_ID+"-write-";

    String READ_OFF_SHELF_ITEM_TASK_CONTEXT_ID = CONTEXT_READ_ID + "off-shelf-item-task";

    String READ_MD_SPECIAL_APPLY_CONTEXT_ID = CONTEXT_READ_ID + "md-special-apply";

    String WRITE_MD_SPECIAL_APPLY_CONTEXT_ID = CONTEXT_READ_ID + "md-special-apply";

    String READ_OFF_SHELF_COMMON_CONTEXT_ID = CONTEXT_READ_ID + "off-shelf-common";

    String WRITE_OFF_SHELF_ITEM_TASK_CONTEXT_ID = CONTEXT_WRITE_ID + "off-shelf-item-task";

    String READ_RECORD_FILING_ITEM_TASK_CONTEXT_ID = CONTEXT_READ_ID + "record-filing-item-task";

    String WRITE_RECORD_FILING_ITEM_TASK_CONTEXT_ID = CONTEXT_WRITE_ID + "record-filing-item-task";

    String READ_RECORD_FILING_ITEM_TASK_DRAFT_CONTEXT_ID = CONTEXT_READ_ID + "record-filing-item-task-draft";

    String WRITE_RECORD_FILING_ITEM_TASK_DRAFT_CONTEXT_ID = CONTEXT_WRITE_ID + "record-filing-item-task-draft";

    String WRITE_NEW_ARRIVAL_ITEM_TASK_CONTEXT_ID = CONTEXT_WRITE_ID + "new-arrival-item-task";
    String READ_NEW_ARRIVAL_ITEM_TASK_CONTEXT_ID = CONTEXT_READ_ID + "new-arrival-item-task";

    String READ_FLOW_CONFIG_CONTEXT_ID = CONTEXT_READ_ID + "flow-config-read";

    String READ_MANUFACTURER_CONTEXT_ID = CONTEXT_READ_ID + "manufacturer-read";

    String NEW_ARRIVAL_STORE_ORG_CONTEXT_ID = CONTEXT_READ_ID + "new-arrival-store-center";
    String READ_FLOW_CONFIG_SPECIFICATION_ID = CONTEXT_WRITE_ID + "flow-config-specification";


    String READ_BRAND_CONTEXT_ID = CONTEXT_READ_ID + "brand";

    String WRITE_BRAND_CONTEXT_ID = CONTEXT_WRITE_ID + "brand";

    String READ_SUPPLIERS_CONTEXT_ID = CONTEXT_READ_ID + "suppliers";

    String WRITE_SUPPLIERS_CONTEXT_ID = CONTEXT_WRITE_ID + "suppliers";

    String WRITE_MANUFACTURER_CONTEXT_ID = CONTEXT_WRITE_ID + "manufacturer";

    String BRAND_START_NUMBER = "GP";

    String MANUFACTURER_START_NUMBER = "CS";

    String WRITE_STORE_ITEM_IDENTIFICATION_CONTEXT_ID = CONTEXT_WRITE_ID + "store-item-identification";

    String READ_SERIESRE_NAME_CONTEXT_ID = CONTEXT_READ_ID + "series-Rename";



    String WRITE_ADJUST_PRICE_CONTEXT_ID = CONTEXT_WRITE_ID + "adjust-price";

    String READ_ADJUST_PRICE_CONTEXT_ID = CONTEXT_READ_ID + "adjust-price";

    String WRITE_COMMON_CONTEXT_ID = CONTEXT_WRITE_ID + "flow-common";

    String READ_FLOW_BPM_CONTEXT_ID = CONTEXT_READ_ID + "flow-bpm";

    String WRITE_FLOW_BPM_CONTEXT_ID = CONTEXT_WRITE_ID + "flow-bpm";

    String WRITE_BIWEEKLY_MD_CONTEXT_ID = CONTEXT_WRITE_ID + "biweekly-md";

}
