package io.terminus.lshm.product.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
    OFF_SHELF_ITEM_TASK("OFF_SHELF_ITEM_TASK", "商品下架","approval","approval-detail?type=edit&offTaskId=","#/delistingProduct/pages/%s/index?offTaskId=%s"),
    PRODUCT_ARCHIVING("PRODUCT_ARCHIVING", "商品建档","RecordList","RecordDetail?type=review&id=","#/createProduct/pages/%s/index?businessId=%s&businessType=%s"),
    PRODUCT_LAUNCHING("PRODUCT_LAUNCHING", "商品上新","NewProductList","Resubmit?id=","#/product-upnew/pages/%s/index?businessId=%s&businessType=%s"),
    ADJUST_PRICE("ADJUST_PRICE", "商品调价","AdjustPriceList","AdjustPriceDetail?id=","#/adjust-price/index?businessType=%s&adjustId=%s&adjustStatus=%s"),
    BIWEEKLY_MD("BIWEEKLY_MD", "双周MD特殊申请","biweeklyMDList","MdSpecialApplyDetail?id=","#/biweeklyMDauditsList?id=%s&type=audit&auditFlowStatus=%s&workflowInstanceId=%s&auditStatus=%s"),
    PRODUCT_NOTIFICATION("PRODUCT_NOTIFICATION", "商品消息通知","notify",null,null);

    private final String code;
    private final String name;
    private final String pcUrl;
    private final String pcDetailUrl;
    private final String h5DetailUrl;




    public static BusinessTypeEnum getByCode(String code) {
        for (BusinessTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return BusinessTypeEnum.OFF_SHELF_ITEM_TASK;
    }
}
