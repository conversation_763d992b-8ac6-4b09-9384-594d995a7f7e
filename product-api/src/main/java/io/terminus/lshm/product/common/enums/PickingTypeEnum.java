package io.terminus.lshm.product.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PickingTypeEnum {


    /**
     * 拣货类型枚举
     * */
    UNPARTITIONED("1","不分区"),
    THE_WHOLE_PIECE("2","整箱"),
    LOOSE_PARTS("3","散件"),
    MEDIUM_PACKAGE("4","中包"),
    HALF_A_BOX("5","半箱");


    private String type;

    private String desc;

    public static String getDescByType(String type){
        for (PickingTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return null;
    }


}
