package io.terminus.lshm.product.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 调价通知类别
 *
 */
@Getter
@AllArgsConstructor
public enum AdjustNoticeEnum {
    /**
     * 常规调价
     */
    CONVENTIONAL("CONVENTIONAL", "常规调价",1),
    /**
     * 紧急调价
     */
    URGENCY("URGENCY", "紧急调价",2);

    private String key;

    private String description;

    private int code;

    public static String getDescByType(int code) {
        for (AdjustNoticeEnum value : values()) {
            if (value.getCode()== code) {
                return value.getDescription();
            }
        }
        return null;
    }

    public static String getDescByKey(String key) {
        for (AdjustNoticeEnum value : values()) {
            if (value.getKey()== key) {
                return value.getDescription();
            }
        }
        return null;
    }

    public static String getKeyByValue(int num) {
        for (AdjustNoticeEnum value : values()) {
            if (value.getCode() == num) {
                return value.getKey();
            }
        }
        return null;
    }

    public static int getValueByKey(String key) {
        for (AdjustNoticeEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getCode();
            }
        }
        return 0;
    }
}