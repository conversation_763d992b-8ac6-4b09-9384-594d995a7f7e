package io.terminus.lshm.product.facade.price.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.trantor.module.base.model.User;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 促销活动方案包聚合对象
 *
 * <AUTHOR>
 * @date ：Created in 2022/1/6 2:38 下午
 */
@Data
@ApiModel(value = "促销活动业务模型")
public class PromotionCampaignRequest extends AbstractRequest {

    private Long id;

    private User createdBy;

    private String brand;

    @ApiModelProperty(name = "活动开始时间")
    private Date promotionCampaignStartTime;

    @ApiModelProperty(name = "活动结束时间")
    private Date promotionCampaignEndTime;


    @ApiModelProperty(name = "活动描述")
    private String promotionCampaignDesc;

    @ApiModelProperty(value = "商品信息")
    private PromotionCampaignItemScopeDO itemScope;
//    @ApiModelProperty(value = "拼团活动")
//    private PromotionCampaignGroupBuyingDO groupBuying;

    @ApiModelProperty(value = "参与活动门店")
    private List<ExtPromotionCampaignShop> shopList;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(id,"活动ID不能为空");
        ParamUtil.nonNull(createdBy,"活动创建人不能为空");
        ParamUtil.nonNull(createdBy.getId(),"活动创建人Id不能为空");
        ParamUtil.nonNull(createdBy.getNickname(),"活动创建人姓名不能为空");
        ParamUtil.notBlank(brand,"品牌不能为空");
        ParamUtil.nonNull(promotionCampaignStartTime,"活动开始时间不能为空");
        ParamUtil.nonNull(promotionCampaignEndTime,"活动结束时间不能为空");
        ParamUtil.notBlank(promotionCampaignDesc,"活动描述不能为空");
        ParamUtil.nonNull(itemScope,"商品信息不能为空");
        ParamUtil.notEmpty(itemScope.getItemList(),"商品信息不能为空");
        itemScope.getItemList().forEach(e->{
            ParamUtil.notBlank(e.getItemCode(),"商品编码不能为空");
            ParamUtil.nonNull(e.getItemId(),"商品Id不能为空");
            ParamUtil.notBlank(e.getItemUnit(),"商品单位不能为空");
            ParamUtil.notBlank(e.getItemName(),"商品名称不能为空");
            ParamUtil.nonNull(e.getPreferentialPrice(),"折扣比例不能为空");
            ParamUtil.nonNull(e.getItemBatch(),"批次不能为空");
            ParamUtil.notBlank(e.getItemImages(),"图片不能为空");
        });
        ParamUtil.notEmpty(shopList,"参与活动门店不能为空");
        shopList.forEach(e->{
            ParamUtil.notBlank(e.getShopID(),"店铺Id不能为空");
            ParamUtil.notBlank(e.getShopCode(),"店铺Code不能为空");
        });
    }
}
