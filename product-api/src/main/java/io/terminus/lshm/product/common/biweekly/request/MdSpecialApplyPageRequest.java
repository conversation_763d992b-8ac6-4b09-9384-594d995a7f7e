package io.terminus.lshm.product.common.biweekly.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.lshm.product.common.bean.request.AbstractPageRequestExt;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MdSpecialApplyPageRequest extends AbstractPageRequestExt {

    /**
     * 唯一标识
     * */
    @ApiModelProperty(name = "唯一标识")
    private Long id;


    /**
     * 申请月份
     * */
    @ApiModelProperty(name = "申请月份")
    private String applyMonthTime;


    /**
     * 流程标题
     * */
    @ApiModelProperty(name = "流程标题")
    private String title;


    /**
     * 应用门店（存入的是门店id）
     * */
    @ApiModelProperty(name = "应用门店（存入的是门店id）")
    private Long applyStore;


    /**
     * 门店名称
     * */
    @ApiModelProperty(name = "门店名称")
    private String storeName;


    /**
     * 申请理由
     * */
    @ApiModelProperty(name = "申请理由")
    private String applyReason;


    /**
     * 审批状态
     * */
    @ApiModelProperty(name = "审批状态")
    private String auditStatus;


    /**
     * 审批状态查询
     * */
    @ApiModelProperty(name = "审批状态查询")
    private List<String > auditStatusList;


    /**
     * 审批流程状态
     * */
    @ApiModelProperty(name = "审批流程状态")
    private String auditFlowStatus;


    /**
     * 审批流程状态查询
     * */
    @ApiModelProperty(name = "审批流程状态查询")
    private List<String > auditFlowStatusList;



    /**
     * 序号
     * */
    @ApiModelProperty(name = "序号")
    private Integer sort;


    /**
     * 门店数
     * */
    @ApiModelProperty(name = "门店数")
    private Integer storeRelationQuantity;


    /**
     * 申请日期（创建日期）
     * */
    @ApiModelProperty(name = "申请日期（创建日期）")
    private Date createdAt;


    /**
     *申请人（创建人id）
     * */
    @ApiModelProperty(name = "申请人（创建人id）")
    private String createdBy;


    /**
     * 申请人昵称
     * */
    @ApiModelProperty(name = "申请人昵称")
    private String createdName;


    /**
     * 申请人手机号
     * */
    @ApiModelProperty(name = "申请人手机号")
    private String createdPhone;


    /**
     * 报备周期
     * */
    @ApiModelProperty(name = "报备周期")
    private String filingCycle;


    /**
     * 备注
     * */
    @ApiModelProperty(name = "备注")
    private String remark;


    /**
     * 相关附件（与附件表关联）
     * */
    @ApiModelProperty(name = "相关附件（与附件表关联）")
    private Long attachmentId;


    /**
     * 更新日期
     * */
    @ApiModelProperty(name = "更新日期")
    private Date updatedAt;


    /**
     * 更新人（更新人id）
     * */
    @ApiModelProperty(name = "更新人（更新人id）")
    private String updatedBy;


    /**
     * 更新人昵称
     * */
    @ApiModelProperty(name = "更新人昵称")
    private String updatedName;


    /**
     * 开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(name = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(name = "结束时间")
    private String endTime;


    /**
     * 门店id
     * */
    @ApiModelProperty("门店id")
    private String storeId;


    /**
     * 门店编码
     * */
    @ApiModelProperty("门店编码")
    private String storeCode;


    /**
     * 申请表id（一对多）
     * */
    @ApiModelProperty("申请表id（一对多）")
    private Set<Long> specialApplyId;



}
