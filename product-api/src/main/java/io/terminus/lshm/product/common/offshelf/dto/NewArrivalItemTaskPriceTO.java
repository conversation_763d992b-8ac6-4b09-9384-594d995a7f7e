package io.terminus.lshm.product.common.offshelf.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.lshm.product.common.bean.model.ApiBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class NewArrivalItemTaskPriceTO extends ApiBean<Long> {

    /**
     * 商品建档id
     */
    @ApiModelProperty(name = "商品建档id")
    private Long newComingItemId;

    /**
     * 单位id
     */
    @ApiModelProperty(name = "单位id")
    private Long unitId;

    /**
     * 单位名称
     */
    @ApiModelProperty(name = "单位名称")
    private String unitName;

    /**
     * 规格
     */
    @ApiModelProperty(name = "规格")
    private String specification;

    /**
     * 换算系数
     */
    @ApiModelProperty(name = "换算系数")
    private BigDecimal conversionFactor;

    /**
     * 单位类型
     */
    @ApiModelProperty(name = "单位类型")
    private String unitType;

    /**
     * 配送价-基本单位
     */
    @ApiModelProperty(name = "配送价-基本单位")
    private BigDecimal baseUnitDeliveryPrice;

    /**
     * 零售价
     */
    @ApiModelProperty(name = "零售价")
    private BigDecimal retailPrice;

    /**
     * 会员零食价
     */
    @ApiModelProperty(name = "会员零食价")
    private BigDecimal memberPrice;

    /**
     * 其他规格零售价
     */
    @ApiModelProperty(name = "其他规格零售价")
    private BigDecimal otherSpecificationPrice;

    /**
     * 24%高成本地区价格
     */
    @ApiModelProperty(name = "24%高成本地区价格")
    private BigDecimal twentyFourPercent;

    /**
     * 22%高成本地区价格
     */
    @ApiModelProperty(name = "22%高成本地区价格")
    private BigDecimal twentyTwoPercent;

    /**
     * 21%高成本地区价格
     */
    @ApiModelProperty(name = "21%高成本地区价格")
    private BigDecimal twentyOnePercent;
    /**
     * 配送价目表
     */
    @ApiModelProperty("配送价目表")
    private BigDecimal deliveryPrice;
}
