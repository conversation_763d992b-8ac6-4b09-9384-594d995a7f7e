package io.terminus.lshm.product.common.biweekly.request;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractPageRequest;
import io.terminus.lshm.product.common.bean.request.AbstractPageRequestExt;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InnerPageStoreRequest extends AbstractPageRequest {



    @ApiModelProperty("门店编码")
    private String storeCode;


    @ApiModelProperty("门店名称")
    private String storeName;

}
