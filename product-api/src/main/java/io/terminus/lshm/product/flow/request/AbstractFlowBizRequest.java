package io.terminus.lshm.product.flow.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务发起流程操作
 * <AUTHOR>
 * @date 2025-05-20
 */
@Getter
@Setter
public abstract class AbstractFlowBizRequest<T> extends AbstractRequest {

    /**
     * 业务表单对象
     */
    private T bizForm;


    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private String workItemId;


    /**
     * 实例id
     */
    @ApiModelProperty("实例id")
    private String workflowInstanceId;

    /**
     * 审批意见
     */
    @ApiModelProperty("审批意见")
    private String comment;
    /**
     * 驳回到指定的节点,多个使用英文逗号隔开
     */
    @ApiModelProperty("驳回到指定的节点,多个使用英文逗号隔开")
    private String rejectToActivityCode;
    /**
     * 是成功还是驳回
     */
    @ApiModelProperty("是成功还是驳回")
    private Boolean submitToReject;


    /**
     * 流程编码
     */
    @ApiModelProperty("流程编码")
    private String flowCode;

}
