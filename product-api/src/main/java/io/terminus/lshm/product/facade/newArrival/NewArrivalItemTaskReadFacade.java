package io.terminus.lshm.product.facade.newArrival;


import io.terminus.lshm.product.common.bean.model.NewArrivalItemTaskDTO;
import io.terminus.lshm.product.common.bean.request.newArrival.NewArrivalItemTaskByIdRequest;
import io.terminus.lshm.product.common.bean.request.newArrival.NewArrivalItemTaskExportRequest;
import io.terminus.lshm.product.common.bean.request.newArrival.NewArrivalItemTaskPageRequest;
import io.terminus.lshm.product.common.bean.response.newArrival.ItemBaseDataResponse;
import io.terminus.lshm.product.common.bean.response.newArrival.NewArrivalItemTaskResponse;
import io.terminus.lshm.product.common.offshelf.dto.NewArrivalItemTaskTO;
import io.terminus.lshm.product.common.recordfiling.response.MyApprovalResponse;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static io.terminus.lshm.product.common.constants.ProductCenterConstant.*;

@FeignClient(name = SERVICE_ID, contextId = READ_NEW_ARRIVAL_ITEM_TASK_CONTEXT_ID)
public interface NewArrivalItemTaskReadFacade {

    /**
     * 分页查询商品下架任务
     *
     * @param request 查询请求
     * @return 分页响应
     */
    @PostMapping("/api/new-arrival-item-task/page")
    Response<Paging<NewArrivalItemTaskResponse>> pageOffShelfItemTask(@RequestBody NewArrivalItemTaskPageRequest request);

    /**
     * 审核记录列表查询
     * @param request
     * @return
     */

//    @PostMapping("/api/new-arrival-item-task/listAuditRecord")
//    Response<List<TaskAuditRecordResponse>> listAuditRecord(@RequestBody TaskByIdRequest request);

    /**
     * 获取详情方法
     * @param request
     * @return
     */
    @PostMapping("/api/new-arrival-item-task/get")
    Response<NewArrivalItemTaskTO> getTask(@RequestBody NewArrivalItemTaskByIdRequest request);


    /**
     * 获取详情方法
     * @return
     */
    @PostMapping("/api/new-arrival-item-task/getLabelList")
    Response<List<ItemBaseDataResponse>> getLabelList();

    /**
     * 导出用查询
     * @param request
     * @return
     */
    @PostMapping("/api/new-arrival-item-task/queryNewArrivalItemTaskList")
    Response<Paging<NewArrivalItemTaskDTO>> queryNewArrivalItemTaskList(@RequestBody NewArrivalItemTaskExportRequest request);


    /**
     * 我的审批
     *
     */
    @PostMapping("/api/new-arrival-item-task/myApprovals")
    Response<MyApprovalResponse> myApprovals();
}