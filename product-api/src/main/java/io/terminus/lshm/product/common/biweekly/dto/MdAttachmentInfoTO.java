package io.terminus.lshm.product.common.biweekly.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.lshm.product.common.bean.model.ApiBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MdAttachmentInfoTO   extends ApiBean<Long> {

    /**
     * 唯一标识
     * */
    @ApiModelProperty(value = "唯一标识")
    private Long id;


    /**
     * 图片或PDF文件地址
     * */
    @ApiModelProperty(name = "图片或PDF文件地址")
    private String fileUrl;


    /**
     * 文件名
     * */
    @ApiModelProperty(name = "图片名")
    private String fileName;


    /**
     * 申请表ID（一对多）
     * */
    @ApiModelProperty(name = "申请表ID（一对多）")
    private Long specialApplyId;

    /**
     * 创建人昵称
     * */
    @ApiModelProperty(name = "创建人昵称")
    private String createdName;


    /**
     * 更新人名称
     * */
    @ApiModelProperty(name = "更新人名称")
    private String updatedName;


    /**
     * 创建人id
     * */
    @ApiModelProperty("创建人id")
    private String createdBys;


    /**
     * 更新人id
     * */
    @ApiModelProperty("更新人id")
    private String updatedBys;

}
