package io.terminus.lshm.product.common.bean.request.adjust;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 调价商品表实体类
 */
@Data
public class AdjustItemAddRequest {
    /**
     * 商品id
     */
    @ApiModelProperty("商品id")
    private String itemId;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String itemCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String itemName;

    /**
     * 商品单位id
     */
    @ApiModelProperty("商品单位id")
    private String itemUnitId;

    /**
     * 商品单位名称
     */
    @ApiModelProperty("促销/售卖/商品单位名称")
    private String itemUnitName;

    /**
     * 商品单位编码
     */
    @ApiModelProperty("商品单位编码")
    private String itemUnitCode;

    /**
     * '原配送价
     */
    @ApiModelProperty("原配送价")
    private BigDecimal itemDeliveryPrice;
    /**
     * 新配送价
     */
    @ApiModelProperty("新配送价")
    private BigDecimal itemDeliveryPriceNew;
    /**
     * 原零售价
     */
    @ApiModelProperty("原零售价")
    private BigDecimal itemRetailPrice;
    /**
     * 新零售价
     */
    @ApiModelProperty("新零售价")
    private BigDecimal itemRetailPriceNew;

    /**
     *  原会员价
     */
    @ApiModelProperty("原会员价")
    private BigDecimal itemMemberPrice;

    /**
     *  新会员价
     */
    @ApiModelProperty("新会员价")
    private BigDecimal itemMemberPriceNew;

    /**
     * 原档案配送价
     */
    @ApiModelProperty("原档案配送价")
    private BigDecimal itemArchiveDeliveryPrice;

    /**
     * 新档案配送价
     */
    @ApiModelProperty("新档案配送价")
    private BigDecimal itemArchiveDeliveryPriceNew;

    /**
     * 原档案零售价
     */
    @ApiModelProperty("原档案零售价")
    private BigDecimal itemArchiveRetailPrice;

    /**
     * 新档案零售价
     */
    @ApiModelProperty("新档案零售价")
    private BigDecimal itemArchiveRetailPriceNew;

    /**
     * 原档案会员价
     */
    @ApiModelProperty("原档案会员价")
    private BigDecimal itemArchiveMemberPrice;
    /**
     * 新档案会员价
     */
    @ApiModelProperty("新档案会员价")
    private BigDecimal itemArchiveMemberPriceNew;

    /**
     * 折扣率
     */
    @ApiModelProperty("折扣率")
    private Double discountRate;

    /**
     * 调价生效日期
     */
    @ApiModelProperty("调价生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryTimeStart;

    /**
     * 调价结束日期
     */
    @ApiModelProperty("调价结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryTimeEnd;

    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 换算率
     */
    @ApiModelProperty("换算率")
    private String conversion;

    /**
     * 临期/大库存
     */
    @ApiModelProperty("临期/大库存")
    private Integer expirationType;

    /**
     * 物料生产开始日期
     */
    @ApiModelProperty("物料生产开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDateStart;

    /**
     * 物料生产结束日期
     */
    @ApiModelProperty("物料生产结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDateEnd;

    /**
     * 保质期
     */
    @ApiModelProperty("保质期")
    private Integer expiration;

    /**
     * 剩余保质期
     */
    @ApiModelProperty("剩余保质期")
    private Integer expirationRemaining;

    /**
     * 剩余保质期占比
     */
    @ApiModelProperty("剩余保质期占比")
    private Double expirationFloat;

    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    private Double itemQuantity;

    /**
     * 日均销量
     */
    @ApiModelProperty("日均销量")
    private Double dailySales;

    /**
     * 可销售天数
     */
    @ApiModelProperty("可销售天数")
    private Integer saleableDays;

    /**
     * 滞销原因
     */
    @ApiModelProperty("滞销原因")
    private String unsoldReason;



    /**
     * 售卖单位id
     */
    @ApiModelProperty("售卖单位id")
    private String saleItemUnitId;


    /**
     *  售卖单位名称
     */
    @ApiModelProperty("售卖/基本单位名称")
    private String saleItemUnitName;


    /**
     * 销售商品单位code
     */
    @ApiModelProperty("销售商品单位code")
    private  String saleItemUnitCode;


    /**
     * 竞争对手售价
     */
    @ApiModelProperty("竞争对手售价")
    private BigDecimal itemCompetitorPrice;


    /**
     * 库存量
     */
    @ApiModelProperty("库存量")
    private Double inventoryQuantity;


    /**
     * 计量单位id
     */
    @ApiModelProperty("计量单位id")
    private String measureUnitId;



}