package io.terminus.lshm.product.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommodityTypeEnum {

    /**
     * 建档商品类型枚举
     * */
    STANDARD("1","标准"),
    COMBINE_ITEM("4","组合商品"),
    COMBINED_ORDER("6","制单组合"),
    COMPONENT_ITEM("9","成分商品");

    private String code;
    private String desc;

    public static String getDescByCode(String code) {
        for (CommodityTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
