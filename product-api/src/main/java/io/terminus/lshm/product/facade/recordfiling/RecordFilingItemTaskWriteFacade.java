package io.terminus.lshm.product.facade.recordfiling;

import io.terminus.lshm.product.common.recordfiling.request.*;
import io.terminus.trantorframework.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static io.terminus.lshm.product.common.constants.ProductCenterConstant.SERVICE_ID;
import static io.terminus.lshm.product.common.constants.ProductCenterConstant.WRITE_RECORD_FILING_ITEM_TASK_CONTEXT_ID;

/**
 * 商品建档接口
 */
@FeignClient(name = SERVICE_ID, contextId = WRITE_RECORD_FILING_ITEM_TASK_CONTEXT_ID)
public interface RecordFilingItemTaskWriteFacade {


    /**
     * 创建供应商任务
     *
     * @param request 查询请求
     * @return 分页响应
     */
    @PostMapping("/api/item/supplier/createSrmTask")
    Response<Boolean> createSrmTask(@RequestBody CreateSrmTaskRequest request);

    /**
     * 保存商品建档
     * @param request
     * @return
     */
    @PostMapping("/api/record-filing-item-task/save")
    Response<Long> saveRecordFilingItemTask(@RequestBody RecordFilingItemTaskRequest request);
    /**
     * 批量创建商品建档
     * @param request
     * @return
     */
    @PostMapping("/api/record-filing-item-task/batchCreate")
    Response<Boolean> batchCreateRecordFilingItemTask(@RequestBody List<BatchRecordFilingItemTaskRequest> request);

    /**
     * 提交审批
     * @param request
     * @return
     */
    @PostMapping("/api/record-filing-item-task/submit")
    Response<Long> submit(@RequestBody RecordFilingItemTaskRequest request);

    /**
     * 审批商品建档任务
     * @param request
     * @return
     */
    @PostMapping("/api/record-filing-item-task/audit")
    Response<Boolean> audit(@RequestBody RecordFilingTaskAuditRequest request);

    /**
     * 草稿箱批量提交
     * @param request
     * @return
     */
    @PostMapping("/api/record-filing-item-task/draftBatchSubmit")
    Response<Boolean> draftBatchSubmit(@RequestBody DraftBatchSubmitRequest request);
}