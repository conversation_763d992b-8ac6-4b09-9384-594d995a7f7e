package io.terminus.lshm.product.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ItemDepartmentEnum {

    /**
     * 商品部门枚举
     * */

    FOOD_DEPARTMENT("001","食品部"),
    THIRD_PARTY_PRODUCTS("002","第三方产品"),
    ORDER_10("003","订单10"),
    ORDER_1("004","订单1"),
    ORDER_2("005","订单2"),
    ORDER_3("006","订单3"),
    ORDER_4("007","订单4"),
    ORDER_5("008","订单5"),
    ORDER_6("009","订单6"),
    ORDER_7("010","订单7"),
    ORDER_8("011","订单8"),
    ORDER_9("012","订单9"),
    ORDER_11("013","订单11（耗材）"),
    ORDER_12("014","订单12"),
    ORDER_13("015","订单13"),
    ORDER_14("017","订单14"),
    THIRD_PARTY_LOW_TEMPERATURE_DELIVERY("016","三方低温配送"),
    WUHU_REGION("018","芜湖区域"),
    SNACKS_VERY_SPICY("019","零食很辣部"),
    COLD_CHAIN_DEPARTMENT("020","冷链部");


    private String code;
    private String desc;

    public static String getDescByCode(String code) {
        for (ItemDepartmentEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }


}
