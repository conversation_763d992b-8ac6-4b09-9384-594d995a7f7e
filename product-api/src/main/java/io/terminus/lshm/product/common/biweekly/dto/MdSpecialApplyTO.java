package io.terminus.lshm.product.common.biweekly.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.lshm.product.common.bean.model.ApiBean;
import io.terminus.lshm.store.common.model.store.InnerStoreTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MdSpecialApplyTO extends ApiBean<Long> {


    /**
     * 主键
     */
    @ApiModelProperty(value = "id")
    private Long id;



    /**
     * 申请月份
     * */
    @ApiModelProperty(name = "申请月份")
    private String applyMonthTime;


    /**
     * 流程标题
     * */
    @ApiModelProperty(name = "流程标题")
    private String title;


    /**
     * 应用门店（关联门店数据）
     * */
    @ApiModelProperty(name = "应用门店（关联门店数据）")
    private List<MdRelationalStoreTO> mdRelationalStores;



    /**
     * 申请理由
     * */
    @ApiModelProperty(name = "申请理由")
    private String applyReason;


    /**
     * 审批状态
     * */
    @ApiModelProperty(name = "审批状态")
    private String auditStatus;


    /**
     * 审批流程状态
     * */
    @ApiModelProperty(name = "审批流程状态")
    private String auditFlowStatus;




    /**
     * 序号
     * */
    @ApiModelProperty(name = "序号")
    private Integer sort;


    /**
     * 门店数
     * */
    @ApiModelProperty(name = "门店数")
    private Integer storeRelationQuantity;


    /**
     * 申请日期（创建日期）
     * */
    @ApiModelProperty(name = "申请日期（创建日期）")
    private Date createdAt;



    /**
     * 申请人昵称
     * */
    @ApiModelProperty(name = "申请人昵称")
    private String createdName;


    /**
     * 申请人手机号
     * */
    @ApiModelProperty(name = "申请人手机号")
    private String createdPhone;


    /**
     * 报备周期
     * */
    @ApiModelProperty(name = "报备周期")
    private String filingCycle;


    /**
     * 备注
     * */
    @ApiModelProperty(name = "备注")
    private String remark;


    /**
     * 相关附件（与附件表关联）
     * */
    @ApiModelProperty(name = "相关附件（与附件表关联）")
    private List<MdAttachmentInfoTO> attachments;


    /**
     * 更新日期
     * */
    @ApiModelProperty(name = "更新日期")
    private Date updatedAt;



    /**
     * 更新人昵称
     * */
    @ApiModelProperty(name = "更新人昵称")
    private String updatedName;



    /**
     * 关联申请表id
     * */
    @ApiModelProperty(name = "关联申请表id")
    private Long specialApplyId;



    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private String workflowInstanceId;


    /**
     * 品牌id 赵一鸣/零食很忙
     */
    @ApiModelProperty(name = "品牌id 0 零食很忙 1赵一鸣")
    private Long brandId;



    /**
     * 门店中心的门店信息
     * */
    @ApiModelProperty(name = "门店中心的门店信息")
    private List<InnerStoreTO> stores;


}
