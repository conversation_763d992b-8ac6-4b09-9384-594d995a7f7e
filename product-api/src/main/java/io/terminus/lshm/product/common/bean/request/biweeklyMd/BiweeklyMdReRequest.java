package io.terminus.lshm.product.common.bean.request.biweeklyMd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description 双周MD新增入参请求实体
 * <AUTHOR>
 * @date 2025/6/19 14:28
 */
@Data
@ApiModel("双周MD新增入参")
public class BiweeklyMdReRequest {

    /**
     * 双周MD特殊请求id
     */
    @ApiModelProperty("双周MD特殊请求id")
    @NotBlank(message = "双周MD特殊请求id不能为空")
    private Long id;
}
