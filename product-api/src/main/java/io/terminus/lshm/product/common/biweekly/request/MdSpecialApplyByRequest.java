package io.terminus.lshm.product.common.biweekly.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractPageRequest;
import io.terminus.api.request.AbstractRequest;
import io.terminus.lshm.product.common.bean.model.ApiBean;
import io.terminus.lshm.product.common.biweekly.dto.MdAttachmentInfoTO;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MdSpecialApplyByRequest extends ApiBean<Long> {


    @ApiModelProperty(name = "md申请id")
    private Long id;

    @ApiModelProperty(name = "申请月份")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyMonthTime;

    @ApiModelProperty("流程标题")
    private String title;

    @ApiModelProperty(name = "申请理由")
    private String applyReason;

    @ApiModelProperty(name = "审批状态")
    private String auditStatus;

    @ApiModelProperty(name = "审批流程状态")
    private String auditFlowStatus;

    @ApiModelProperty(name = "序号")
    private Integer sort;

    @ApiModelProperty(name = "申请人昵称")
    private String createdName;

    @ApiModelProperty(name = "更改人名称")
    private String updatedName;

    @ApiModelProperty(name = "申请人手机号")
    private String createdPhone;

    @ApiModelProperty(name = "报备周期")
    private String filingCycle;

    @ApiModelProperty(name = "备注")
    private String remark;

    @ApiModelProperty(name = "门店数）")
    private Integer storeRelationQuantity;

    @ApiModelProperty(name = "门店id")
    private String storeId;

    @ApiModelProperty(name = "门店编码")
    private String storeCode;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("门店状态")
    private String storeStatus;

    @ApiModelProperty("申请表ID（一对多）")
    private Long specialApplyId;

    @ApiModelProperty("流程实例id")
    private String workflowInstanceId;

    @ApiModelProperty(name = "品牌id 0 零食很忙 1赵一鸣")
    private Long brandId;

    @ApiModelProperty("附表信息集合")
    private List<MdAttachmentInfoTO> attachmentList;


    @ApiModelProperty("新建时选择的所有门店集合")
    private List<MdRelationalStoreTO> storeList;



}
