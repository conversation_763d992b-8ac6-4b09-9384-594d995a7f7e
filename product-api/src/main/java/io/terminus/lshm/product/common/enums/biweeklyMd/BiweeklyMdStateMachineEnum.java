package io.terminus.lshm.product.common.enums.biweeklyMd;

import java.util.EnumMap;
import java.util.Map;

/**
 * @Description 获取双周MD特殊申请下一个状态的流转
 * <AUTHOR>
 * @date 2025/6/23 11:59
 */
public class BiweeklyMdStateMachineEnum {

    private static final Map<BiweeklyMdFlowNodeEnum, BiweeklyMdFlowNodeEnum> NEXT_NODES = new EnumMap<>(BiweeklyMdFlowNodeEnum.class);

    static {
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.BIWEEKLY_MD_START, BiweeklyMdFlowNodeEnum.DEPARTMENT_DIRECTOR_APPROVAL);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.DEPARTMENT_DIRECTOR_APPROVAL, BiweeklyMdFlowNodeEnum.CATEGORY_OPERATIONS_MANAGEMENT_APPROVAL);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.CATEGORY_OPERATIONS_MANAGEMENT_APPROVAL, BiweeklyMdFlowNodeEnum.PRODUCT_OPERATION_SUPERVISOR_APPROVAL);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.PRODUCT_OPERATION_SUPERVISOR_APPROVAL, BiweeklyMdFlowNodeEnum.DESIGN_MANAGER_APPROVAL);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.DESIGN_MANAGER_APPROVAL, BiweeklyMdFlowNodeEnum.PRODUCT_OPERATION_DIRECTOR_APPROVAL);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.PRODUCT_OPERATION_DIRECTOR_APPROVAL, BiweeklyMdFlowNodeEnum.OPERATION_MAINTENANCE_MANAGEMENT_APPROVAL);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.OPERATION_MAINTENANCE_MANAGEMENT_APPROVAL, BiweeklyMdFlowNodeEnum.OATROL_SHOP_CUSTOMER_SERVICE_EXECUTE_APPROVAL);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.OATROL_SHOP_CUSTOMER_SERVICE_EXECUTE_APPROVAL, BiweeklyMdFlowNodeEnum.OPERATION_MAINTENANCE_EXECUTE_APPROVAL);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.OPERATION_MAINTENANCE_EXECUTE_APPROVAL, BiweeklyMdFlowNodeEnum.COMPLETED);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.COMPLETED, null);
        NEXT_NODES.put(BiweeklyMdFlowNodeEnum.CANCELLED, null);
    }

    public static BiweeklyMdFlowNodeEnum getNextNode(BiweeklyMdFlowNodeEnum current) {
        return NEXT_NODES.get(current);
    }

}
