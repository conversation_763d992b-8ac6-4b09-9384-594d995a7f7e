package io.terminus.lshm.product.common.biweekly.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.lshm.product.common.bean.model.ApiBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MdRelationalStoreTO extends ApiBean<Long> {

    /**
     * 门店ID
     * */
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    /**
     * 排序
     * */
    @ApiModelProperty(name = "排序")
    private Integer sort;


    /**
     * 门店编码
     * */
    @ApiModelProperty(name = "门店编码")
    private String storeCode;


    /**
     * 门店名称
     * */
    @ApiModelProperty(name = "门店名称")
    private String storeName;


    /**
     * 门店状态
     * */
    @ApiModelProperty(name = "门店状态")
    private String storeStatus;


    /**
     * 申请表ID（一对多
     * */
    @ApiModelProperty(name = "申请表ID（一对多")
    private Long specialApplyId;


    /**
     * 创建人昵称
     * */
    @ApiModelProperty(name = "创建人昵称")
    private String createdName;


    /**
     * 更新人名称
     * */
    @ApiModelProperty(name = "更新人名称")
    private String updatedName;


    /**
     * 创建人id
     * */
    @ApiModelProperty("创建人id")
    private String createdBys;


    /**
     * 更新人id
     * */
    @ApiModelProperty("更新人id")
    private String updatedBys;


    /**
     * 创建时间
     * */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;


    /**
     * 更新时间
     * */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

}
