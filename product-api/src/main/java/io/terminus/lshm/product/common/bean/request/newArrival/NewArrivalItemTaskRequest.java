package io.terminus.lshm.product.common.bean.request.newArrival;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.lshm.product.common.enums.SellingPointEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class NewArrivalItemTaskRequest extends AbstractRequest {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(name = "id")
    private Long id;

    /**
     * 前端临时id
     */
    @ApiModelProperty(name = "前端临时id")
    private Integer tempId;


    /**
     * 商品id
     */
    @ApiModelProperty(name = "商品id")
    private Long itemId;

    /**
     * 商品名称
     */
    @ApiModelProperty(name = "商品名称")
    private String itemName;

    /**
     * 商品编码
     */
    @ApiModelProperty(name = "商品编码")
    private String itemCode;

    /**
     * 商品类别id
     */
    @ApiModelProperty(name = "商品类别id")
    private String itemTypeId;

    /**
     * 商品类别
     */
    @ApiModelProperty(name = "商品类别名称")
    private String itemTypeName;

    /**
     * 是否为散称
     */
    @ApiModelProperty(name = "是否为散称")
    private Integer isScatteredWeighing;

    /**
     * 是否为第三方产品
     */
    @ApiModelProperty(name = "是否为第三方产品")
    private Integer isThirdPartyProduct;


    /**
     * 厂家合作情况说明
     */
    @ApiModelProperty(name = "厂家合作情况说明")
    private String cooperationDetail;

    /**
     * 供应商编码
     */
    @ApiModelProperty(name = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "供应商名称")
    private String supplierName;

    /**
     * 保质期/天
     */
    @ApiModelProperty(name = "保质期/天")
    private Integer shelfLifeDays;

    /**
     * 外条码
     */
    @ApiModelProperty(name = "外条码")
    private String barCodeList;

    /**
     * 内条码
     */
    @ApiModelProperty(name = "内条码")
    private String innerBarcodeList;

    /**
     * 建议上新仓库id集合
     */
    @ApiModelProperty(name = "建议上新仓库id集合")
    private String suggestionWarehouseIdList;

    /**
     * 建议上新仓库名称集合
     */
    @ApiModelProperty(name = "建议上新仓库名称集合")
    private String suggestionWarehouseNameList;

    /**
     * 建议上新区域id集合
     */
    @ApiModelProperty(name = "很忙建议上新区域id集合")
    private String areaIdList;

    /**
     * 建议上新区域名称集合
     */
    @ApiModelProperty(name = "很忙建议上新区域名称集合")
    private String areaNameList;

    /**
     * 建议上新区域id集合
     */
    @ApiModelProperty(name = "一鸣建议上新区域id集合")
    private String ymAreaIdList;

    /**
     * 建议上新区域名称集合
     */
    @ApiModelProperty(name = "一鸣建议上新区域名称集合")
    private String ymAreaNameList;

    /**
     * 门店类型集合
     */
    @ApiModelProperty(name = "门店类型集合")
    private String storeTypeList;

    /**
     * 门店店型分类集合
     */
    @ApiModelProperty(name = "门店店型分类集合")
    private String storeShopTypeList;

    /**
     * 门店类型集合名称
     */
    @ApiModelProperty("门店类型集合名称")
    private String storeTypeListName;

    /**
     * 门店店型分类集合名称
     */
    @ApiModelProperty("门店店型分类集合名称")
    private String storeShopTypeListName;

    /**
     * 计划首批到仓上新时间
     */
    @ApiModelProperty(name = "计划首批到仓上新时间")
    private Date firstComingTime;

    /**
     * 门店首批去货数量/件（四位小数）
     */
    @ApiModelProperty(name = "门店首批去货数量/件（四位小数）")
    private BigDecimal firstArrivedNumber;

    /**
     * 规格单位id
     */
    @ApiModelProperty(name = "规格单位id")
    private Long specificationUnitId;

    /**
     * 规格单位
     */
    @ApiModelProperty(name = "规格单位")
    private String specificationUnit;

    /**
     * 规格数量
     */
    @ApiModelProperty(name = "规格数量")
    private String specificationQty;

    /**
     * 规格（自动生成）
     */
    @ApiModelProperty(name = "规格（自动生成）")
    private String specification;

    /**
     * 售卖条件 SINGLE:单个/单件,COMBINE:连包/组合,FCL:整箱/件,SCATTER :散称
     */
    @ApiModelProperty(name = "售卖条件 SINGLE:单个/单件,COMBINE:连包/组合,FCL:整箱/件,SCATTER :散称")
    private String sellCondition;

    /**
     * 上新商品类型
     */
    @ApiModelProperty(name = "上新商品类型")
    private String comingItemType;

    /**
     * 替换下架商品编码集合
     */
    @ApiModelProperty(name = "替换下架商品编码集合")
    private String replaceItemCodeList;

    /**
     * 替换下架商品名称集合
     */
    @ApiModelProperty(name = "替换下架商品名称集合")
    private String replaceItemNameList;

    /**
     * 是否纳入双周MD
     */
    @ApiModelProperty(name = "是否纳入双周MD")
    private Integer isMd;

    /**
     * 门店毛利率
     */
    @ApiModelProperty(name = "门店毛利率")
    private BigDecimal shopGrossMargin;

    /**
     * 预计新品月度销售额
     */
    @ApiModelProperty(name = "预计新品月度销售额")
    private BigDecimal estimateSales;

    /**
     * 产能预估 件/月
     */
    @ApiModelProperty(name = "产能预估 件/月")
    private BigDecimal capacityEstimation;

    /**
     * 销售预估量件/月
     */
    @ApiModelProperty(name = "销售预估量件/月")
    private BigDecimal salesForecast;

    /**
     * 特批凭证地址
     */
    @ApiModelProperty(name = "特批凭证地址")
    private String specialVoucher;

    /**
     * 是否有产品合规书
     */
    @ApiModelProperty(name = "是否有产品合规书")
    private Integer isComplianceDocument;

    /**
     * 产品卖点
     */
    @ApiModelProperty(name = "产品卖点")
    private String productSellingPoint;

    /**
     * 申请状态
     */
    @ApiModelProperty(name = "申请状态")
    private String applyStatus;

    /**
     * 是否特价
     */
    @ApiModelProperty(name = "是否特价")
    private Integer isSpecialPrice;

    /**
     * 品牌id
     */
    @ApiModelProperty(name = "品牌id")
    private Integer brandId;

    /**
     * 品牌名称
     */
    @ApiModelProperty(name = "品牌名称")
    private String brandName;


    /**
     * 品牌code
     */
    @ApiModelProperty(name = "品牌code")
    private String brandCode;
    /**
     * 产品卖点：原料产地，产能介绍，产品0添加，优质材料，等级检测，其他
     */
    @ApiModelProperty("产品卖点")
    private String productSellPointList;
    /**
     * 卖点说明
     */
    @ApiModelProperty("卖点说明")
    private String sellPointRemark;
    /**
     * 检测证明
     */
    @ApiModelProperty("检测证明")
    private String testCertificateFile;
    /**
     * 迷你店是否去货 1:是,0:否
     */
    @ApiModelProperty("迷你店是否去货")
    private Integer isMiniShopPurchase;
    /**
     * 外箱图片
     */
    @ApiModelProperty("外箱图片")
    private String outBoxImage;

    /**
     * 门店首批去货数量单位
     */
    @ApiModelProperty("门店首批去货数量单位")
    private String firstArrivedNumberUnit;
    /**
     * 价格信息集合
     */
    @ApiModelProperty(name = "价格信息集合")
    private List<NewArrivalItemTaskPriceRequest> priceRequestList;


    @Override
    public void checkParam() {
        ParamUtil.notBlank(itemName, "商品名称不能为空");
        ParamUtil.notBlank(itemCode, "商品代码不能为空");
        ParamUtil.nonNull(isScatteredWeighing, "是否为散称不能为空");
        ParamUtil.nonNull(isThirdPartyProduct, "是否为第三方产品不能为空");
        ParamUtil.notBlank(cooperationDetail, "厂家合作情况说明不能为空");
        ParamUtil.notBlank(innerBarcodeList, "内条码不能为空");
        ParamUtil.nonNull(firstComingTime, "计划首批到仓上新时间不能为空");
        ParamUtil.nonNull(firstArrivedNumber, "门店首批去货数量不能为空");
        ParamUtil.nonNull(isMd, "是否纳入双周MD不能为空");
        ParamUtil.nonNull(estimateSales, "预计新品月度销售额不能为空");
        ParamUtil.nonNull(capacityEstimation, "产能预估不能为空");
        ParamUtil.nonNull(salesForecast, "销售预估量不能为空");
        ParamUtil.nonNull(isMiniShopPurchase, "迷你店是否去货不能为空");
        ParamUtil.nonNull(isComplianceDocument, "是否有产品合规书不能为空");
        ParamUtil.notBlank(productSellPointList, "产品卖点不能为空");
        if (productSellPointList.contains(SellingPointEnum.PRODUCE_AREA.getDesc()) || productSellPointList.contains(SellingPointEnum.CAPACITY_INTRODUCE.getDesc())
                || productSellPointList.contains(SellingPointEnum.OTHER.getDesc())) {
            ParamUtil.notBlank(sellPointRemark, "卖点说明不能为空");
            ParamUtil.notBlank(testCertificateFile, "检测证明不能为空");
        }
    }

}
