package io.terminus.lshm.product.common.biweekly.request;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MdRelationalStoreRequest extends AbstractPageRequest {


    @ApiModelProperty("门店id")
    private String storeId;


    @ApiModelProperty("门店编码")
    private String storeCode;


    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("门店关联主表id")
    private String mdRelationalStoreId;

}
