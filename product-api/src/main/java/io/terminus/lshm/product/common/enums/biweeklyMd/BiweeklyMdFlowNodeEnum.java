package io.terminus.lshm.product.common.enums.biweeklyMd;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 双周MD特殊申请流程节点枚举
 * <AUTHOR>
 * @date 2025/6/19 16:41
 */
@Getter
@AllArgsConstructor
public enum BiweeklyMdFlowNodeEnum {

    BIWEEKLY_MD_START("start", "Activity2", "发起申请", 1),

    DEPARTMENT_DIRECTOR_APPROVAL("departmentDirector", "Activity3","部门负责人", 2),
    CATEGORY_OPERATIONS_MANAGEMENT_APPROVAL("categoryOperationsManagement", "Activity5","品类运营副经理", 3),
    PRODUCT_OPERATION_SUPERVISOR_APPROVAL("productOperationSupervisor","Activity6", "产品运营主管", 4),
    DESIGN_MANAGER_APPROVAL("designManager", "Activity9", "企划经理", 5),
    PRODUCT_OPERATION_DIRECTOR_APPROVAL("productOperationDirector", "Activity10","产品经营总监", 6),
    OPERATION_MAINTENANCE_MANAGEMENT_APPROVAL("operationMaintenanceManagement", "Activity13","运维副经理", 7),
    OATROL_SHOP_CUSTOMER_SERVICE_EXECUTE_APPROVAL("oatrolShopCustomerServiceExecute", "Activity11","巡店客服执行", 8),
    OPERATION_MAINTENANCE_EXECUTE_APPROVAL("operationMaintenanceExecute","Activity8", "运维执行", 9),

    /**
     *审批完成
     */
    COMPLETED("completed","Activity12", "审批完成", 10),

    CANCELLED("cancelled","","已作废", 11);

    private String code;
    private String bpmCode;
    private String desc;
    private int value;

    public static String getDescByCode(String code) {
        for (BiweeklyMdFlowNodeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static String getBpmCodeByCode(String code) {
        for (BiweeklyMdFlowNodeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getBpmCode();
            }
        }
        return null;
    }

    public static BiweeklyMdFlowNodeEnum getByCode(String code) {
        for (BiweeklyMdFlowNodeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getCodeByBpmCode(String bpmCode) {
        for (BiweeklyMdFlowNodeEnum value : values()) {
            if (value.getBpmCode().equals(bpmCode)) {
                return value.getCode();
            }
        }
        return null;
    }
}
