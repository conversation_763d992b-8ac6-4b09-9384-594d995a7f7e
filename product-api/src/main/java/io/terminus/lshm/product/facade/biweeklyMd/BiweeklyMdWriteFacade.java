package io.terminus.lshm.product.facade.biweeklyMd;

import io.terminus.lshm.product.common.bean.request.biweeklyMd.BiweeklyMdReRequest;
import io.terminus.lshm.product.common.bean.request.bpm.FlowCancelRequest;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.trantorframework.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

import static io.terminus.lshm.product.common.constants.ProductCenterConstant.SERVICE_ID;
import static io.terminus.lshm.product.common.constants.ProductCenterConstant.WRITE_BIWEEKLY_MD_CONTEXT_ID;

/**
 * @Description 双周MD写入接口
 * <AUTHOR>
 * @date 2025/6/19 11:33
 */
@FeignClient(name = SERVICE_ID, contextId = WRITE_BIWEEKLY_MD_CONTEXT_ID)
public interface BiweeklyMdWriteFacade {

    /**
     * 审批
     * @param request
     * @return
     */
    @PostMapping("/api/biweeklyMd/audit")
    Response<Boolean> audit(@RequestBody FlowApplyBizRequest request);

    /**
     * 批量审批
     * @param request
     * @return
     */
    @PostMapping("/api/biweeklyMd/batchAudit")
    Response<Boolean> batchAudit(@RequestBody List<FlowApplyBizRequest>  request);

    /**
     * 催办
     * @param id
     * @return
     */
    @PostMapping("/api/biweeklyMd/flowUp")
    Response<Boolean> flowUp(@RequestParam Long id);

    /**
     * 作废
     * @param request
     * @return
     */
    @PostMapping("/api/biweeklyMd/cancel")
    Response<Boolean> cancel(@RequestBody FlowCancelRequest request);

    /**
     * 重新提交
     * @param request
     * @return
     */
    @PostMapping("/api/biweeklyMd/reApply")
    Response<Boolean> reApply(@RequestBody FlowApplyBizRequest<BiweeklyMdReRequest> request);
}
