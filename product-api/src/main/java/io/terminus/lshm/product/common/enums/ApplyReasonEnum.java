package io.terminus.lshm.product.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApplyReasonEnum {

    /**
     * MD申请理由枚举
     * */
    /**
     * 属于抵消门店，可能会产生库存积压
     */
    OFFSETSTORE("OFFSETSTORE", "属于抵消门店，可能会产生库存积压"),

    /**
     * 已启动闭店流程或已闭店的门店
     */
    CLOSETHESTORE("CLOSETHESTORE", "已启动闭店流程或已闭店的门店"),

    /**
     * 活动执行率差，非常不配合的门店
     * */
    EXECUTIONRATESTORE("EXECUTIONRATESTORE","活动执行率差，非常不配合的门店");


    private String code;
    private String desc;

    public static String getDescByCode(String code) {
        for (ApplyReasonEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }



}
