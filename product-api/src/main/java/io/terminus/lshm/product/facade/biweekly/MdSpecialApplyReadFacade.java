package io.terminus.lshm.product.facade.biweekly;


import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.common.offshelf.dto.EmployeeUsersTO;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.store.common.bean.request.store.read.InnerPageAllRequest;
import io.terminus.lshm.store.common.bean.request.store.read.InnerPageStoreRequest;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyPageRequest;
import io.terminus.lshm.store.common.model.store.InnerStoreTO;
import io.terminus.trantorframework.Paging;
import org.springframework.cloud.openfeign.FeignClient;
import io.terminus.trantorframework.Paging;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import io.terminus.trantorframework.Response;

import java.util.List;

import static io.terminus.lshm.product.common.constants.ProductCenterConstant.READ_MD_SPECIAL_APPLY_CONTEXT_ID;
import static io.terminus.lshm.product.common.constants.ProductCenterConstant.SERVICE_ID;

/**
 * <AUTHOR>
 */
@FeignClient(name = SERVICE_ID, contextId = READ_MD_SPECIAL_APPLY_CONTEXT_ID)
public interface MdSpecialApplyReadFacade {


    /**
     * 分页查询md特殊申请列表
     * @param request 查询请求
     * @return 分页响应
     * **/
    @PostMapping("/api/biweeklyMd/pageMdSpecialApply")
    Response<Paging<MdSpecialApplyTO>> pageMdSpecialApply(@RequestBody MdSpecialApplyPageRequest request);



    /**
     * 调用门店中心的列表接口获取门店数据
     * @param request 请求参数（如果需要）
     * @return 门店分页数据
     */
    @PostMapping("/api/biweeklyMd/getStoreList")
    Response<Paging<InnerStoreTO>> getStoreList(@RequestBody InnerPageAllRequest request);



    /**
     * 查看md特殊申请详情
     * @param request 查询请求
     * @return 分页响应
     * **/
    @PostMapping("/api/biweeklyMd/getMdSpecialApply")
    Response<MdSpecialApplyTO> getMdSpecialApply(@RequestBody MdSpecialApplyByRequest request);



    /**
     * 查看详情时获门店信息，先查询出门店关联表中的门店id，在根据门店id去查询门店表，分页返回门店信息
     * */
    @PostMapping("/api/biweeklyMd/getRelationalStoreInfo")
    Response<List<MdRelationalStoreTO>> getRelationalStoreInfo(@RequestBody MdSpecialApplyPageRequest request);



    /**
     * 点击新建时，把申请人的昵称，手机号，还有申请日期放回给前端
     * */
    @PostMapping("/api/biweeklyMd/echoUser")
    Response<EmployeeUsersTO> echoUser(@RequestBody MdSpecialApplyByRequest request);



    /**
     * 点击审批或者查看时，获取审批记录信息
     * */
    @PostMapping("/api/biweeklyMd/getApprovalRecord")
    Response<List<MdAuditRecordTO>> getApprovalRecord(@RequestBody MdSpecialApplyByRequest request);


}
