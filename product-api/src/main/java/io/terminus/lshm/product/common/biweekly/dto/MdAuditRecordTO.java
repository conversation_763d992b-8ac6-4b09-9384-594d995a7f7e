package io.terminus.lshm.product.common.biweekly.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.lshm.product.common.bean.model.ApiBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MdAuditRecordTO extends ApiBean<Long> {

    /**
     * 唯一标识
     * */
    @ApiModelProperty("唯一标识")
    private Long id;

    /**
     * 申请表id
     * */
    @ApiModelProperty("申请表id")
    private Long specialApplyId;



    /**
     * 流程审批节点
     * */
    @ApiModelProperty("审批流程节点")
    private String specialApply;


    /**
     * 审批状态
     * */
    @ApiModelProperty("审批状态")
    private String auditStatus;



    /**
     * 去审批人
     * */
    @ApiModelProperty("去审批人")
    private String toAuditor;


    /**
     * 审批原因
     * */
    @ApiModelProperty("审批原因")
    private String auditReason;



    /**
     * 审批人
     * */
    @ApiModelProperty("审批人")
    private String auditor;



    /**
     * 审批人姓名
     * */
    @ApiModelProperty("审批人姓名")
    private String auditorName;


    /**
     * 审批人工号
     * */
    @ApiModelProperty("审批人工号")
    private String auditorEmployeeCode;


    /**
     * 审批人角色名称
     * */
    @ApiModelProperty("审批人角色名称")
    private String auditorRoleName;



    /**
     * 审批时间
     * */
    @ApiModelProperty("审批时间")
    private Date auditAt;


    /**
     * 创建人昵称
     * */
    @ApiModelProperty("创建人昵称")
    private String createdName;


    /**
     * 创建时间
     * */
    @ApiModelProperty("创建时间")
    private Date createdAt;


    /**
     * 创建人id
     * */
    @ApiModelProperty("创建人id")
    private String createdBys;


    /**
     * 更新人id
     * */
    @ApiModelProperty("更新人id")
    private String updatedBys;



    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private String workflowInstanceId;

    /**
     * 流程节点任务id
     */
    @ApiModelProperty("流程节点任务id")
    private String workItemId;

    /**
     * 审计流程节点
     */
    @ApiModelProperty("审计流程节点")
    private String auditFlowNode;

    /**
     * 审批节点名称*
     */
    @ApiModelProperty("流程节点名称")
    private String auditFlowNodeName;
}
