package io.terminus.lshm.product.common.biweekly.request;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * <AUTHOR> 2025/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MdAttachmentInfoRequest extends AbstractPageRequest {

    /**
     * 唯一标识
     * */
    @ApiModelProperty(name = "唯一标识")
    private Long id;


    /**
     *申请表ID（一对多）
     * */
    @ApiModelProperty(name = "申请表ID（一对多）")
    private Set<Long> specialApplyId;


}
