package io.terminus.lshm.product.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MaterialsTaxRateEnum {


    /**
     * 物料税类枚举
     * */

    GENERAL_TAXABLE_GOODS("CN001","一般税率商品"),
    CONSUMER_GOODS("CN002","民生商品"),
    VALUE_ADDED_TAX_SIX("CN003","简易征税货物（6%）"),
    VALUE_ADDED_TAX_FOUR("CN004","简易征税特殊货物（4%）"),
    THE_FREIGHT_IS_DEDUCTIBLE("CN005","运费（可抵扣）"),
    THE_FREIGHT_IS_NON_DEDUCTIBLE("CN006","运费（不可抵扣）"),
    ZERO_RATE_TAX_ITEMS("CN007","零税率商品"),
    VALUE_ADDED_TAX_THREE("CN1801","小规模纳税人3%"),
    VALUE_ADDED_TAX_FIVE("CN1802","小规模纳税人5%"),
    GENERAL_SCALE_TAXATION_SIX("CN1803","一般规模纳税人6%"),
    GENERAL_SCALE_TAXATION_TEN("CN1804","一般规模纳税人10%"),
    GENERAL_SCALE_TAXATION_TWELVE("CN1805","一般规模纳税人16%"),
    SMALL_SCALE_TAXATION_ONE_HUNDRED_AND_FIFTY("CN1901","小规模纳税人1.5%"),
    SMALL_SCALE_TAXATION_TWO("CN1902","小规模纳税人2%"),
    GENERAL_SCALE_TAXATION_NINE("CN1903","一般规模纳税人9%"),
    GENERAL_SCALE_TAXATION_THIRTEEN("CN1904","一般规模纳税人13%"),
    SMALL_SCALE_TAXATION_ONE("CN1905","小规模纳税人1%");


    private String code;
    private String desc;

    public static String getDescByCode(String code) {
        for (MaterialsTaxRateEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

}
