package io.terminus.lshm.product.common.offshelf.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.lshm.product.common.bean.model.ApiBean;
import io.terminus.lshm.product.common.flow.dto.FlowNodeDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class NewArrivalItemTaskTO extends ApiBean<Long> {

    /**
     * 商品id
     */
    @ApiModelProperty(name = "商品id")
    private Long itemId;

    /**
     * 商品名称
     */
    @ApiModelProperty(name = "商品名称")
    private String itemName;

    /**
     * 商品编码
     */
    @ApiModelProperty(name = "商品编码")
    private String itemCode;

    /**
     * 商品类别id
     */
    @ApiModelProperty(name = "商品类别id")
    private String itemTypeId;

    /**
     * 商品类别名称
     */
    @ApiModelProperty(name = "商品类别名称")
    private String itemTypeName;

    /**
     * 是否为散称
     */
    @ApiModelProperty(name = "是否为散称")
    private Integer isScatteredWeighing;

    /**
     * 是否为第三方产品
     */
    @ApiModelProperty(name = "是否为第三方产品")
    private Integer isThirdPartyProduct;


    /**
     * 厂家合作情况说明
     */
    @ApiModelProperty(name = "厂家合作情况说明")
    private String cooperationDetail;

    /**
     * 供应商编码
     */
    @ApiModelProperty(name = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "供应商名称")
    private String supplierName;

    /**
     * 保质期/天
     */
    @ApiModelProperty(name = "保质期/天")
    private Integer shelfLifeDays;

    /**
     * 外条码
     */
    @ApiModelProperty(name = "外条码")
    private String barCodeList;

    /**
     * 内条码
     */
    @ApiModelProperty(name = "内条码")
    private String innerBarcodeList;

    /**
     * 建议上新仓库id集合
     */
    @ApiModelProperty(name = "建议上新仓库id集合")
    private String suggestionWarehouseIdList;

    /**
     * 建议上新仓库名称集合
     */
    @ApiModelProperty(name = "建议上新仓库名称集合")
    private String suggestionWarehouseNameList;

    /**
     * 建议上新区域id集合
     */
    @ApiModelProperty(name = "很忙建议上新区域id集合")
    private String areaIdList;

    /**
     * 建议上新区域名称集合
     */
    @ApiModelProperty(name = "很忙建议上新区域名称集合")
    private String areaNameList;

    /**
     * 建议上新区域id集合
     */
    @ApiModelProperty(name = "一鸣建议上新区域id集合")
    private String ymAreaIdList;

    /**
     * 建议上新区域名称集合
     */
    @ApiModelProperty(name = "一鸣建议上新区域名称集合")
    private String ymAreaNameList;

    /**
     * 门店类型集合
     */
    @ApiModelProperty(name = "门店类型集合")
    private String storeTypeList;

    /**
     * 门店类型集合名称
     */
    @ApiModelProperty(name = "门店类型集合名称")
    private String storeTypeListName;

    /**
     * 门店店型分类集合名称
     */
    @ApiModelProperty(name = "门店店型分类集合名称")
    private String storeShopTypeListName;

    /**
     * 门店店型分类集合
     */
    @ApiModelProperty(name = "门店店型分类集合")
    private String storeShopTypeList;

    /**
     * 计划首批到仓上新时间
     */
    @ApiModelProperty(name = "计划首批到仓上新时间")
    private Date firstComingTime;

    /**
     * 门店首批去货数量/件（四位小数）
     */
    @ApiModelProperty(name = "门店首批去货数量/件（四位小数）")
    private BigDecimal firstArrivedNumber;

    /**
     * 规格单位id
     */
    @ApiModelProperty(name = "规格单位id")
    private Long specificationUnitId;

    /**
     * 规格单位
     */
    @ApiModelProperty(name = "规格单位")
    private String specificationUnit;

    /**
     * 规格数量
     */
    @ApiModelProperty(name = "规格数量")
    private String specificationQty;


    /**
     * 规格
     */
    @ApiModelProperty(name = "规格")
    private String specification;

    /**
     * 售卖条件 SINGLE:单个/单件,COMBINE:连包/组合,FCL:整箱/件,SCATTER :散称
     */
    @ApiModelProperty(name = "售卖条件 SINGLE:单个/单件,COMBINE:连包/组合,FCL:整箱/件,SCATTER :散称")
    private String sellCondition;

    /**
     * 上新商品类型
     */
    @ApiModelProperty(name = "上新商品类型")
    private String comingItemType;

    /**
     * 替换下架商品编码集合
     */
    @ApiModelProperty(name = "替换下架商品编码集合")
    private String replaceItemCodeList;

    /**
     * 替换下架商品名称集合
     */
    @ApiModelProperty(name = "替换下架商品名称集合")
    private String replaceItemNameList;

    /**
     * 是否纳入双周MD
     */
    @ApiModelProperty(name = "是否纳入双周MD")
    private Integer isMd;

    /**
     * 门店毛利率
     */
    @ApiModelProperty(name = "门店毛利率")
    private BigDecimal shopGrossMargin;

    /**
     * 预计新品月度销售额
     */
    @ApiModelProperty(name = "预计新品月度销售额")
    private BigDecimal estimateSales;

    /**
     * 产能预估 件/月
     */
    @ApiModelProperty(name = "产能预估 件/月")
    private BigDecimal capacityEstimation;

    /**
     * 销售预估量件/月
     */
    @ApiModelProperty(name = "销售预估量件/月")
    private BigDecimal salesForecast;

    /**
     * 特批凭证地址
     */
    @ApiModelProperty(name = "特批凭证地址")
    private String specialVoucher;

    /**
     * 是否有产品合规书
     */
    @ApiModelProperty(name = "是否有产品合规书")
    private Integer isComplianceDocument;

    /**
     * 产品卖点
     */
    @ApiModelProperty(name = "产品卖点")
    private String productSellingPoint;

    /**
     * 申请状态
     */
    @ApiModelProperty(name = "申请状态")
    private String applyStatus;

    /**
     * 是否特价
     */
    @ApiModelProperty(name = "是否特价")
    private Integer isSpecialPrice;

    /**
     * 品牌id
     */
    @ApiModelProperty(name = "品牌id")
    private Integer brandId;

    /**
     * 品牌名称
     */
    @ApiModelProperty(name = "品牌名称")
    private String brandName;


    /**
     *品牌code
     * */
    @ApiModelProperty(name = "品牌code")
    private String brandCode;

    /**
     * 流程-产品卖点
     */
    @ApiModelProperty("产品卖点")
    private String flowProductSellingPoint;

    /**
     * 流程-卖点说明
     */
    @ApiModelProperty("卖点说明")
    private String flowSellingPointExplain;

    /**
     * 流程-检测证明地址
     */
    @ApiModelProperty("检测证明地址")
    private String flowInspectionCertificate;

    /**
     * 流程-卖点卡
     */
    @ApiModelProperty("卖点卡")
    private String flowSellingCard;

    /**
     * 流程-确认首批到仓上新时间
     */
    @ApiModelProperty("确认首批到仓上新时间")
    private Date flowFirstComingTime;

    /**
     * 流程-包装锋利(割手)
     */
    @ApiModelProperty("包装锋利(割手)")
    private Integer isSharp;

    /**
     * 流程-条码褶皱
     */
    @ApiModelProperty("条码褶皱")
    private Integer isCodeWrinkle;

    /**
     * 流程-日期隐蔽
     */
    @ApiModelProperty("日期隐蔽")
    private Integer isDateConceal;

    /**
     * 流程-品控标签
     */
    @ApiModelProperty("品控标签")
    private String qualityLable;

    /**
     * 流程-产品合规书地址
     */
    @ApiModelProperty("产品合规书地址")
    private String complianceDocument;

    /**
     * 流程-质检结论
     */
    @ApiModelProperty("质检结论")
    private String qualityTestingResult;

    /**
     * 流程-供应商盖章风险承诺函地址
     */
    @ApiModelProperty("供应商盖章风险承诺函地址")
    private String commitmentLetter;

    /**
     * 流程-高风险原因
     */
    @ApiModelProperty("高风险原因")
    private String highRiskReason;

    /**
     * 流程-相关附件
     */
    @ApiModelProperty("相关附件")
    private String relatedAccessory;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    private String auditStatus;


    /**
     * 审计流程节点
     */
    @ApiModelProperty("审计流程节点")
    private String auditFlowNode;

    /**
     * 审批流程状态
     */
//    @ApiModelProperty("审批流程状态")
//    private String auditFlowStatus;

    @ApiModelProperty("审批流程状态列表")
    private List<FlowNodeDTO> auditFlowStatusList;

    /**
     * 白底图
     */
    @ApiModelProperty("白底图")
    private String whiteBackgroundImg;

    /**
     * 场景图
     */
    @ApiModelProperty("场景图")
    private String sceneImg;

    /**
     * 商品成列图
     */
    @ApiModelProperty("商品成列图")
    private String columnImg;

    /**
     * 成列说明
     */
    @ApiModelProperty("成列说明")
    private String columnExplain;

    /**
     * 陈列单个产品图
     */
    @ApiModelProperty("陈列单个产品图")
    private String productImg;

    /**
     * 产品支持品牌所需图
     */
    @ApiModelProperty("产品支持品牌所需图")
    private String brandImg;

    /**
     * 产品支持文字说明
     */
    @ApiModelProperty("产品支持文字说明")
    private String brandExplain;

    /**
     * 产品支持备注
     */
    @ApiModelProperty("产品支持备注")
    private String brandRemark;

    /**
     * 品牌总图单个产品图
     */
    @ApiModelProperty("品牌总图单个产品图")
    private String brandProductImg;

    /**
     * 品牌总图
     */
    @ApiModelProperty("品牌总图")
    private String brandTotalImg;


    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String createdName;

    /**
     * 审批完成时间
     */
    @ApiModelProperty("审批完成时间")
    private Date auditCompletionTime;

    /**
     * 上新商品价格集合
     */
    @ApiModelProperty(name = "上新商品价格集合")
    private List<NewArrivalItemTaskPriceTO>  newArrivalItemTaskPriceTOList;

    /**
     * 审批权限key
     */
    @ApiModelProperty("审批权限key")
    private List<String> keyList;

    /**
     * 一鸣区域and很忙区域id集合
     */
    @ApiModelProperty("一鸣区域and很忙区域id集合")
    private String yiMingBusyIdList;

    /**
     * 一鸣区域and很忙区域名称集合
     */
    @ApiModelProperty("一鸣区域and很忙区域名称集合")
    private String yiMingBusyNameList;
    /**
     * 产品卖点：原料产地，产能介绍，产品0添加，优质材料，等级检测，其他
     */
    @ApiModelProperty("产品卖点")
    private String productSellPointList;
    /**
     * 卖点说明
     */
    @ApiModelProperty("卖点说明")
    private String sellPointRemark;
    /**
     * 检测证明
     */
    @ApiModelProperty("检测证明")
    private String testCertificateFile;
    /**
     * 迷你店是否去货 1:是,0:否
     */
    @ApiModelProperty("迷你店是否去货")
    private Integer isMiniShopPurchase;
    /**
     * 外箱图片
     */
    @ApiModelProperty("外箱图片")
    private String outBoxImage;
    /**
     * 门店首批去货数量单位
     */
    @ApiModelProperty("门店首批去货数量单位")
    private String firstArrivedNumberUnit;


}
