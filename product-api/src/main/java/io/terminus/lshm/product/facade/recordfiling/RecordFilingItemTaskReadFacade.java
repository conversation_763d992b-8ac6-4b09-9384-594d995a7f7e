package io.terminus.lshm.product.facade.recordfiling;

import io.terminus.lshm.ncc.response.TreeNccGoodsTypeResponse;
import io.terminus.lshm.ncc.response.TreeNccNewDisplayResponse;
import io.terminus.lshm.product.common.offshelf.response.EmployeeUserResponse;
import io.terminus.lshm.product.common.recordfiling.dto.EmployeeTO;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskDTO;
import io.terminus.lshm.product.common.recordfiling.request.EmployeePageRequest;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskExportRequest;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskPageRequest;
import io.terminus.lshm.product.common.recordfiling.request.TaxRateRequest;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskListReaponse;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskPageResponse;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskResponse;
import io.terminus.lshm.product.common.recordfiling.response.TaxRateResponse;
import io.terminus.lshm.product.common.recordfiling.response.*;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskDTO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import static io.terminus.lshm.product.common.constants.ProductCenterConstant.READ_RECORD_FILING_ITEM_TASK_CONTEXT_ID;
import static io.terminus.lshm.product.common.constants.ProductCenterConstant.SERVICE_ID;

/**
 * 商品建档接口
 */
@FeignClient(name = SERVICE_ID, contextId = READ_RECORD_FILING_ITEM_TASK_CONTEXT_ID)
public interface RecordFilingItemTaskReadFacade {

    /**
     * 分页查询商品建档信息
     *
     * @param request 查询请求
     * @return 分页响应
     */
    @PostMapping("/api/record-filing-item-task/page")
    Response<Paging<RecordFilingItemTaskPageResponse>> pageRecordFilingItemTask(@RequestBody RecordFilingItemTaskPageRequest request);

    /**
     * 查询商品建档详情
     *
     * @param id
     * @return
     */
    @PostMapping("/api/record-filing-item-task/get")
    Response<RecordFilingItemTaskResponse> detail(@RequestParam Long id);

    /**
     * 分页查询商品建档信息
     *
     * @param request 查询请求
     * @return 分页响应
     */
    @PostMapping("/api/record-filing-item-task/newArrivalGetList")
    Response<List<RecordFilingItemTaskListReaponse>> newArrivalGetList(@RequestBody RecordFilingItemTaskPageRequest request);

    @PostMapping("/api/record-filing-item-task/selectTaxRate")
    Response<List<TaxRateResponse>> selectTaxRate(@RequestBody TaxRateRequest request);

    @PostMapping("/api/record-filing-item-task/queryRecordFilingItemTaskList")
    Response<Paging<RecordFilingItemTaskDTO>> queryRecordFilingItemTaskList(@RequestBody RecordFilingItemTaskExportRequest request);


    /**
     * 查询产品经理
     */
    @PostMapping("/api/record-filing-item-task/selectProductManager")
    Response<List<EmployeeUserResponse>> selectProductManager();


    /**
     * 查询新陈列分类
     */
    @PostMapping("/api/record-filing-item-task/nccNewDisplayCategory")
    Response<List<TreeNccNewDisplayResponse>> nccNewDisplayCategory();

    /**
     * 商品分类
     */
    @PostMapping("/api/record-filing-item-task/goodsTypeList")
    Response<List<TreeNccGoodsTypeResponse>> goodsTypeList();

    /**
     * 查询用户中心员工列表
     */
    @PostMapping("/api/record-filing-item-task/findEmployeePage")
    Response<Paging<EmployeeTO>> findEmployeePage(@RequestBody EmployeePageRequest request);

    /**
     * 我的审批
     *
     */
    @PostMapping("/api/record-filing-item-task/myApprovals")
    Response<MyApprovalResponse> myApprovals();
}