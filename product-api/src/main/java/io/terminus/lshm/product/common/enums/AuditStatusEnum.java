package io.terminus.lshm.product.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AuditStatusEnum {

    APPROVED("APPROVED", "通过"),
    UNDER_REVIEW("UNDER_REVIEW", "审核中"),
    REJECTED("REJECTED", "驳回"),
    COMPLETED("COMPLETED","已完成"),
    NEW("NEW","新建"),
    CANCELLED("CANCELLED","作废");

    private String code;
    private String desc;

    public static String getDescByCode(String code) {
        for (AuditStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
