package io.terminus.lshm.product.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description:流程code
 * @Date 2025/6/7
 */
@Getter
@AllArgsConstructor
public enum WorkFlowCodeEnum {

    ADJUST_PRICE("product_center_test_flow","调价流程"),

    MD_SPECIAL_APPLY("monthly_matters_approvalFlow","MD申请流程");

    private String  code;

    private String name;

}
