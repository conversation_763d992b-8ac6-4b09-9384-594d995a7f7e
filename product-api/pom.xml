<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.terminus.lshm</groupId>
        <artifactId>product-center</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>product-api</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor</groupId>
            <artifactId>trantor-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor</groupId>
            <artifactId>trantor-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.terminus</groupId>
            <artifactId>api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-ncc-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>store-inner-api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-flow-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-mdm-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>

</project>
