create table push_event_message
(
    `id`              bigint auto_increment comment '主键id' primary key,
    `biz_Id`       bigint(20) not null comment '业务id',
    `biz_code`       varchar(64) null comment '业务code',
    `biz_type` varchar(32) not null comment '业务类型',
    `state` varchar(20) not null comment '业务状态',
    `pre_State` varchar(20) null comment '业务前一个状态',
    `to_system` varchar(32) not null comment '对端系统',
    `biz_time`           varchar(20) null comment '业务更新时间',
    `retry_times`       tinyint(1) DEFAULT '0' COMMENT '重试次数',
    `extend_data` varchar(64) null comment '扩展数据',
    `message_status` tinyint(1) not null comment '消息状态0:处理中，1：执行失败，2：执行成功',
    `return_message` varchar(256) null comment '返回消息',
    `msg_id` varchar(64) null comment '消息id',
    `created_by`    varchar(32) DEFAULT NULL COMMENT '创建人',
    `updated_by`    varchar(32) DEFAULT NULL COMMENT '更新人',
    `created_at`    datetime NOT NULL COMMENT '创建时间',
    `updated_at`    datetime NOT NULL COMMENT '更新时间',
    `deleted_at`    bigint(20) DEFAULT NULL COMMENT '删除时间',
    `is_deleted`    tinyint(1)      DEFAULT b'0' COMMENT '是否删除',
    `version`       int(11) DEFAULT '1' COMMENT '信息版本号',
    INDEX `idx_biz_type_system` (`biz_Id`,`biz_type`,`to_system` ,`state`)
) ENGINE=InnoDB comment '业务消息表' charset = utf8mb4;





create table state_event_message
(
    `id`              bigint auto_increment comment '主键id' primary key,
    `biz_Id`       bigint(20) not null comment '业务id',
    `biz_code`       varchar(64) null comment '业务code',
    `biz_type` varchar(32) not null comment '业务类型',
    `state` varchar(20) not null comment '业务状态',
    `pre_State` varchar(20) null comment '业务前一个状态',
    `biz_time`           varchar(20) null comment '业务更新时间',
    `retry_times`       tinyint(1) DEFAULT '0' COMMENT '重试次数',
    `extend_data` varchar(64) null comment '扩展数据',
    `message_status` char(1) not null comment '消息状态0:处理中，1：执行失败，2：执行成功',
    `return_message` varchar(256) null comment '返回消息',
    `msg_id` varchar(64) null comment '消息id',
    `created_by`    varchar(32) DEFAULT NULL COMMENT '创建人',
    `updated_by`    varchar(32) DEFAULT NULL COMMENT '更新人',
    `created_at`    datetime NOT NULL COMMENT '创建时间',
    `updated_at`    datetime NOT NULL COMMENT '更新时间',
    `deleted_at`    bigint(20) DEFAULT NULL COMMENT '删除时间',
    `is_deleted`    tinyint(1)      DEFAULT b'0' COMMENT '是否删除',
    `version`       int(11) DEFAULT '1' COMMENT '信息版本号',
    INDEX `idx_biz_type_state` (`biz_Id`,`biz_type`,`state`)
) ENGINE=InnoDB comment '业务消息表' charset = utf8mb4;
