<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.terminus.lshm.product.server.domain.biweekly.dao.MdRelationalStoreMapper">
    <insert id="addMdRelationalStore">
        INSERT INTO md_relational_store (
        store_id,store_code, store_name, special_apply_id,
        created_name
        ) VALUES
        <foreach collection="storeList" item="item" separator=",">
        (
        #{item.storeId}, #{item.storeCode}, #{item.storeName}, #{item.specialApplyId},
        #{item.createdName}
        )
        </foreach>
    </insert>
    <update id="updateMdRelationalStoreInfo">
        UPDATE md_relational_store
        SET
        is_deleted = 1
        WHERE id IN
        <if test="storeList != null">
            <foreach collection="storeList" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
    </update>



    <select id="selectRelationalStoreList"
            resultType="io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO">
        SELECT
        mdrs.id,
        mdrs.store_id,
        mdrs.store_code,
        mdrs.store_name,
        mdrs.special_apply_id
        FROM md_relational_store mdrs
        WHERE mdrs.is_deleted = 0
        <if test="ew.storeId != null">
            AND mdrs.store_id = #{ew.storeId}
        </if>
        <if test="ew.storeName != null and ew.storeName != ''">
            AND mdrs.store_name LIKE CONCAT('%', #{ew.storeName}, '%')
        </if>
        ORDER BY mdrs.created_at DESC
    </select>
    <select id="selectRelationalStoreListByIds"
            resultType="io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO">
        SELECT
        mdrs.id,
        mdrs.store_id,
        mdrs.store_code,
        mdrs.store_name,
        mdrs.special_apply_id
        FROM md_relational_store mdrs
        WHERE  mdrs.is_deleted = 0
        <if test="ew.id != null">
            AND mdrs.special_apply_id = #{ew.id}
        </if>
        <if test="ew.storeCode != null and ew.storeCode != ''">
            AND mdrs.store_code LIKE CONCAT('%', #{ew.storeCode}, '%')
        </if>
        <if test="ew.storeName != null and ew.storeName != ''">
            AND mdrs.store_name LIKE CONCAT('%', #{ew.storeName}, '%')
        </if>
    </select>
    <select id="selectMdRelationalStoreList"
            resultType="io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO">
        SELECT
        mdrs.id,
        mdrs.store_id,
        mdrs.store_code,
        mdrs.store_name,
        mdrs.special_apply_id
        FROM md_relational_store mdrs
        WHERE mdrs.is_deleted = 0
        <if test="id != null">
            AND mdrs.special_apply_id = #{id}
        </if>
    </select>
</mapper>
