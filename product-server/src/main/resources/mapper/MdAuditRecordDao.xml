<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.terminus.lshm.product.server.domain.biweekly.dao.MdAuditRecordMapper">
    <insert id="addMdSpecialApply">
        INSERT INTO md_audit_record(
            special_apply_id,
            special_apply,
            audit_status,
            audit_reason,
            auditor,
            auditor_name,
            auditor_employee_code,
            work_item_id,
            to_auditor,
            created_by,
            created_name,
            created_at,
            updated_by,
            updated_at
        ) VALUES (
            #{ew.specialApplyId},
            #{ew.specialApply},
            #{ew.auditStatus},
            #{ew.auditReason},
            #{ew.auditor},
            #{ew.auditorName},
            #{ew.auditorEmployeeCode},
            #{ew.workflowInstanceId},
            #{ew.toAuditor},
            #{ew.createdBys},
            #{ew.createdName},
            #{ew.createdAt},
            #{ew.updatedBys},
            #{ew.updatedAt}
        )
    </insert>
    <select id="getApprovalRecord" resultType="io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO">
        SELECT
            mdar.id,
            mdar.special_apply_id,
            mdar.special_apply,
            mdar.audit_status,
            mdar.audit_reason,
            mdar.auditor,
            mdar.auditor_name,
            mdar.auditor_employee_code,
            mdar.auditor_role_name,
            mdar.to_auditor,
            mdar.audit_at,
            mdar.created_name,
            mdar.created_at,
            mdar.work_item_id
        FROM md_audit_record mdar
        WHERE mdar.special_apply_id = #{ew.specialApplyId}
        ORDER BY mdar.created_at DESC
    </select>
</mapper>
