<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.terminus.lshm.product.server.domain.biweekly.dao.MdAttachmentInfoMapper">
    <insert id="addMdAttachmentInfo">
        INSERT INTO md_attachment_info (
        file_url,
        file_name,
        special_apply_id,
        created_at,
        updated_at
        ) VALUES
        <foreach collection="attachmentList" item="item" separator=",">
            (
            #{item.fileUrl},
            #{item.fileName},
            #{item.specialApplyId},
            #{item.createdAt},
            #{item.updatedAt}
            )
        </foreach>
    </insert>
    <update id="updateMdAttachmentInfo">
        UPDATE md_attachment_info
        SET is_deleted = 1
        WHERE id IN
        <if test="attachmentList != null">
            <foreach collection="attachmentList" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
    </update>


    <select id="selectMdAttachmentInfoList"
            resultType="io.terminus.lshm.product.common.biweekly.dto.MdAttachmentInfoTO">
        SELECT
            mdai.id,
            mdai.file_url,
            mdai.file_name,
            mdai.special_apply_id
        FROM md_attachment_info mdai
        WHERE mdai.is_deleted = 0
        <if test="id != null">
            AND mdai.special_apply_id = #{id}
        </if>
        ORDER BY mdai.created_at DESC
    </select>
</mapper>
