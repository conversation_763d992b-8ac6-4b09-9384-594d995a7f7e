<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.terminus.lshm.product.server.domain.biweekly.dao.MdSpecialApplyMapper">
    <update id="updateMdSpecialApply">
        UPDATE md_special_apply
        SET
            brand_id = #{ew.brandId},
            remark = #{ew.remark},
            apply_reason = #{ew.applyReason},
            store_relation_quantity = #{ew.storeRelationQuantity},
            updated_at = #{ew.updatedAt},
            updated_by = #{ew.updatedBy},
            updated_name = #{ew.updatedName}
        WHERE id = #{ew.id}
    </update>

    <!--    <insert id="addMdSpecialApply" keyProperty="ew.id" useGeneratedKeys="true">-->
<!--        INSERT INTO md_special_apply (-->
<!--            apply_month_time, title,-->
<!--            apply_reason, audit_status, audit_flow_status,-->
<!--            from_auditor, to_auditor, is_send, sort,-->
<!--            created_by, created_name, created_phone, filing_cycle,-->
<!--            remark, store_relation_quantity,workflow_instance_id,-->
<!--            created_at, updated_at-->
<!--        ) VALUES (-->
<!--                     #{ew.applyMonthTime}, #{ew.title},-->
<!--                     #{ew.applyReason}, #{ew.auditStatus}, #{ew.auditFlowStatus},-->
<!--                     #{ew.fromAuditor}, #{ew.toAuditor}, #{ew.isSend}, #{ew.sort},-->
<!--                     #{ew.createdBy}, #{ew.createdName}, #{ew.createdPhone}, #{ew.filingCycle},-->
<!--                     #{ew.remark}, #{ew.storeRelationQuantity},#{ew.workflowInstanceId},-->
<!--                     #{ew.createdAt}, #{ew.updatedAt}-->
<!--                 )-->
<!--    </insert>-->

    <select id="pageMdSpecialApply" resultType="io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO">
        SELECT
        mdsa.id,
        mdsa.apply_month_time,
        mdsa.title,
        mdsa.apply_reason,
        mdsa.audit_status,
        mdsa.audit_flow_status,
        mdsa.workflow_instance_id,
        mdsa.sort,
        mdsa.created_by,
        mdsa.created_name,
        mdsa.created_phone,
        mdsa.filing_cycle,
        mdsa.remark,
        mdsa.store_relation_quantity,
        mdsa.created_at
        FROM md_special_apply mdsa WHERE mdsa.is_deleted = 0
        <if test="ew.specialApplyId != null and ew.specialApplyId.size() > 0">
            AND  mdsa.id IN
            <foreach item="item" collection="ew.specialApplyId" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="ew.auditStatusList != null and !ew.auditStatusList.isEmpty()">
            AND mdsa.audit_status IN
            <foreach collection="ew.auditStatusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="ew.auditFlowStatusList != null and !ew.auditFlowStatusList.isEmpty()">
            AND mdsa.audit_flow_status IN
            <foreach collection="ew.auditFlowStatusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="ew.startTime != null and  ew.startTime != '' and ew.endTime != null and ew.endTime != ''">
            AND mdsa.created_at BETWEEN #{ew.startTime} AND #{ew.endTime}
        </if>
        <if test="ew.applyMonthTime != null and ew.applyMonthTime != ''">
            AND DATE_FORMAT(mdsa.apply_month_time, '%Y-%m') = #{ew.applyMonthTime}
        </if>
        <if test="ew.title != null  and ew.title != ''">
            AND  mdsa.title LIKE concat('%',#{ew.title},'%')
        </if>
        <if test="ew.applyReason != null and ew.applyReason != ''">
            AND  mdsa.apply_reason LIKE concat('%',#{ew.applyReason},'%')
        </if>
        <if test="ew.createdName != null and ew.createdName != ''">
            AND  mdsa.created_name LIKE concat('%',#{ew.createdName},'%')
        </if>
    </select>
    <select id="getMdSpecialApply" resultType="io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO">
        SELECT
            mdsa.id,
            mdsa.apply_month_time,
            mdsa.title,
            mdsa.apply_reason,
            mdsa.audit_status,
            mdsa.audit_flow_status,
            mdsa.workflow_instance_id,
            mdsa.brand_id,
            mdsa.sort,
            mdsa.created_name,
            mdsa.created_phone,
            mdsa.created_at,
            mdsa.filing_cycle,
            mdsa.remark,
            mdsa.store_relation_quantity,
            mdsa.updated_name,
            mdsa.updated_at
        FROM md_special_apply mdsa WHERE mdsa.is_deleted = 0 AND mdsa.id = #{ew.id}
    </select>
    <select id="dataCalculation" resultType="java.lang.Long">
        select MAX(stv.sort) from md_special_apply  stv where  stv.is_deleted = 0
    </select>
</mapper>
