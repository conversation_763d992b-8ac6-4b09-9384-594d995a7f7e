package io.terminus.lshm.product.server.domain.recordFiling.service.read.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.terminus.lshm.ncc.response.TreeNccGoodsTypeResponse;
import io.terminus.lshm.ncc.response.TreeNccNewDisplayResponse;
import io.terminus.lshm.product.common.enums.AuditStatusEnum;
import io.terminus.lshm.product.common.enums.BusinessTypeEnum;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskExportRequest;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskPageRequest;
import io.terminus.lshm.product.common.recordfiling.response.*;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskDTO;
import io.terminus.lshm.product.server.converter.RecordFilingItemTaskConverter;
import io.terminus.lshm.product.server.converter.RecordFilingItemTaskSpecificationPriceConverter;
import io.terminus.lshm.product.server.external.ncc.NccService;
import io.terminus.lshm.product.server.manager.BusinessAuditRecordManager;
import io.terminus.lshm.product.server.domain.recordFiling.dao.RecordFilingItemTaskDao;
import io.terminus.lshm.product.server.domain.recordFiling.model.RecordFilingItemTaskPO;
import io.terminus.lshm.product.server.domain.recordFiling.model.RecordFilingItemTaskSpecificationPricePO;
import io.terminus.lshm.product.server.domain.recordFiling.service.read.RecordFilingItemTaskReadService;
import io.terminus.lshm.product.server.domain.recordFiling.service.read.RecordFilingItemTaskSpecificationPriceReadService;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RecordFilingItemTaskReadServiceImpl extends ServiceImpl<RecordFilingItemTaskDao, RecordFilingItemTaskPO> implements RecordFilingItemTaskReadService {
    @Resource
    private RecordFilingItemTaskSpecificationPriceReadService recordFilingItemTaskSpecificationPriceService;
    @Resource
    private RecordFilingItemTaskConverter recordFilingItemTaskConverter;
    @Resource
    private RecordFilingItemTaskSpecificationPriceConverter recordFilingItemTaskSpecificationPriceConverter;
    @Resource
    private BusinessAuditRecordManager businessAuditRecordManager;
    @Resource
    private  NccService nccService;


    @Override
    public Paging<RecordFilingItemTaskPageResponse> page(RecordFilingItemTaskPageRequest request) {

        if (ObjectUtil.isNotEmpty(request.getApproveStatus())) {
            List<Long> myApprovals =baseMapper.myApprovalIds(ServerContext.getUserId().toString(),request.getApproveStatus());
            if (CollUtil.isEmpty(myApprovals)) {
                return Paging.empty();
            }
            request.setFilterIds(myApprovals);
        }
        Page<RecordFilingItemTaskPO> result = this.queryPageList(request);
        // 未查询到数据
        if (CollUtil.isEmpty(result.getRecords())) {
            return Paging.empty();
        }
        log.info("分页查询返回数据result.getRecords {} ,总数量 {}", result.getRecords(), result.getTotal());
        List<RecordFilingItemTaskPageResponse> list = recordFilingItemTaskConverter.listP2PageRsp(result.getRecords());
        //组织按钮权限数据
        if (ObjectUtil.isNotEmpty(ServerContext.getUserId())) {
            Set<String> idset = list.stream().map(RecordFilingItemTaskPageResponse::getId).map(String::valueOf).collect(Collectors.toSet());
            //组织按钮权限数据
            Map<String, List<String>> keyMap = businessAuditRecordManager.selectBatchApprovalPending(
                    BusinessTypeEnum.PRODUCT_ARCHIVING.getCode(), idset, ServerContext.getUserId().toString());
            list.forEach(x -> x.setKeyList(keyMap.get(x.getId().toString())));
        }
        //转移到converter
//        list.forEach(x -> {
//            if (ObjectUtil.isNotEmpty(x.getAuditFlowStatus())) {
//                x.setAuditFlowStatusList(JSONUtil.toList(x.getAuditFlowStatus(), FlowNodeDTO.class));
//            }
//        });
        log.info("数据组装完成后 list {} ", list);
        return new Paging<>(result.getTotal(), list);
    }

    @Override
    public Response<RecordFilingItemTaskResponse> detail(Long id) {

        RecordFilingItemTaskPO result = this.getById(id);
        if (ObjectUtil.isEmpty(result)) {
            return Response.ok(new RecordFilingItemTaskResponse());
        }
        RecordFilingItemTaskResponse recordFilingItemTaskResponse = recordFilingItemTaskConverter.p2Rsp(result);
        //查询商品规格构成信息和价格信息
        LambdaQueryWrapper<RecordFilingItemTaskSpecificationPricePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RecordFilingItemTaskSpecificationPricePO::getRecordFilingId, id);
        lambdaQueryWrapper.eq(RecordFilingItemTaskSpecificationPricePO::getIsDeleted, false);
        List<RecordFilingItemTaskSpecificationPricePO> specificationPriceList = recordFilingItemTaskSpecificationPriceService.list(lambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(specificationPriceList)) {
            List<RecordFilingItemTaskSpecificationPriceResponse> itemSpecificationList = recordFilingItemTaskSpecificationPriceConverter.listP2ListRsp(specificationPriceList);
            recordFilingItemTaskResponse.setItemSpecificationList(itemSpecificationList);
            recordFilingItemTaskResponse.setItemPriceList(itemSpecificationList);
        }
        //组织按钮权限数据
        if (ObjectUtil.isNotEmpty(ServerContext.getUserId())) {
            List<String> keyList = businessAuditRecordManager.selectApprovalPending(
                    BusinessTypeEnum.PRODUCT_ARCHIVING.getCode(), id.toString(), ServerContext.getUserId().toString());
            recordFilingItemTaskResponse.setKeyList(keyList);
        }

//        if (ObjectUtil.isNotEmpty(recordFilingItemTaskResponse.getAuditFlowStatus())) {
//            recordFilingItemTaskResponse.setAuditFlowStatusList(JSONUtil.toList(recordFilingItemTaskResponse.getAuditFlowStatus(), FlowNodeDTO.class));
//        }

        return Response.ok(recordFilingItemTaskResponse);
    }

    public Page<RecordFilingItemTaskPO> queryPageList(RecordFilingItemTaskPageRequest request) {
        return this.baseMapper.queryPageList(new Page<>(request.getPageNo(), request.getPageSize()), request);

    }

    public Response<List<RecordFilingItemTaskListReaponse>> newArrivalGetList(RecordFilingItemTaskPageRequest request) {
        LambdaQueryWrapper<RecordFilingItemTaskPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> {
            if (StringUtils.isNotBlank(request.getItemCode())) {
                wrapper.like(RecordFilingItemTaskPO::getItemCode, request.getItemCode());
            }
            if (StringUtils.isNotBlank(request.getItemName())) {
                if (StringUtils.isNotBlank(request.getItemCode())) {
                    wrapper.or();
                }
                wrapper.like(RecordFilingItemTaskPO::getItemFullName, request.getItemName());
            }
        });
        queryWrapper.eq(RecordFilingItemTaskPO::getAuditStatus, AuditStatusEnum.COMPLETED.getCode());
        List<RecordFilingItemTaskPO> recordFilingItemTaskPOList = this.list(queryWrapper);

        // 未查询到数据
        if (CollUtil.isEmpty(recordFilingItemTaskPOList)) {
            return Response.ok(new ArrayList<>());
        }
        List<RecordFilingItemTaskListReaponse> list = Convert.toList(RecordFilingItemTaskListReaponse.class, recordFilingItemTaskPOList);
        return Response.ok(list);
    }

    @Override
    public Page<RecordFilingItemTaskDTO> queryRecordFilingItemTaskList(RecordFilingItemTaskExportRequest request) {
        // 我的审批过滤
        if (ObjectUtil.isNotEmpty(request.getApproveStatus()) && ObjectUtil.isNotEmpty(request.getLoginUserId())) {
            List<Long> myApprovals =baseMapper.myApprovalIds(request.getLoginUserId(),request.getApproveStatus());
            if (CollUtil.isEmpty(myApprovals)) {
                return new Page<>(request.getPageNo(), request.getPageSize(), 0);
            }
            request.setFilterIds(myApprovals);
        }
        return this.baseMapper.queryRecordFilingItemTaskList(new Page<>(request.getPageNo(), request.getPageSize()), request);
    }

    /**
     * 查询品牌的数量*
     *
     * @param brandIds
     * @return
     */
    @Override
    public Map<Long, Long> countByBrandIds(List<Long> brandIds) {
        List<RecordFilingItemTaskPO> recordFilingItemTaskPOS = this.baseMapper.countByBrandIds(brandIds);
        return recordFilingItemTaskPOS.stream().collect(Collectors.toMap(RecordFilingItemTaskPO::getBrandId, RecordFilingItemTaskPO::getApplicantId));
    }

    @Override
    public Map<Long, Long> countByManufacturers(List<Long> ids) {
        List<RecordFilingItemTaskPO> recordFilingItemTaskPOS = this.baseMapper.countByManufacturers(ids);
        return recordFilingItemTaskPOS.stream().collect(Collectors.toMap(RecordFilingItemTaskPO::getManufacturerId, RecordFilingItemTaskPO::getApplicantId));

    }

    /**
     * 新陈列分类
     * @return
     */
    public List<TreeNccNewDisplayResponse> nccNewDisplayCategory() {
        return nccService.nccNewDisplayCategory();
    }

    /**
     * 获取商品分类
     * @return
     */
    public List<TreeNccGoodsTypeResponse> goodsTypeList() {
        return nccService.goodsTypeList();
    }

    @Override
    public MyApprovalResponse myApprovals() {
        String userId = ServerContext.getUserId().toString();
        return this.baseMapper.myApprovals(userId);
    }
}
