package io.terminus.lshm.product.server.baseflow;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import io.terminus.common.model.Response;
import io.terminus.gaia.organization.api.facade.InnerEmployeeReadFacade;
import io.terminus.gaia.organization.api.request.FindEmployeeUserInfoByEmployeeCodeListRequest;
import io.terminus.gaia.organization.api.response.FindEmployeeUserInfoByEmployeeCodeListResponse;
import io.terminus.lshm.flow.exception.FlowBpmExceptionCodeEnum;
import io.terminus.lshm.flow.exception.FlowException;
import io.terminus.lshm.flow.request.*;
import io.terminus.lshm.flow.response.FlowItemApprovalStatusEnum;
import io.terminus.lshm.flow.response.FlowItemExtApiResponse;
import io.terminus.lshm.flow.response.FlowStartWithBizResponse;
import io.terminus.lshm.product.common.enums.WorkFlowCodeEnum;
import io.terminus.lshm.product.facade.flow.FlowBpmReadFacade;
import io.terminus.lshm.product.facade.flow.FlowBpmWriteFacade;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 编写公共的审核类
 * @param <T>
 */
@Slf4j
public abstract class BaseFlowAbstract<T> {

    @Resource
    protected FlowBpmWriteFacade flowBpmWriteFacade;
    @Resource
    protected FlowBpmReadFacade flowBpmReadFacade;
    @Resource
    private InnerEmployeeReadFacade innerEmployeeReadFacade;

    /**
     * 提交申请（模板方法）
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitFlowApply(FlowApplyBizRequest<?> request) {
        //获取实体参数
        T entity = saveEntity(request);
        //todo 调用BPM流程
        FlowStartWithBizRequest flowStartWithBizRequest = new FlowStartWithBizRequest();
        flowStartWithBizRequest.setWorkflowCode(request.getFlowCode());
        flowStartWithBizRequest.setData(request.getBizForm());
        FlowStartWithBizResponse response = flowBpmWriteFacade.startWithBiz(flowStartWithBizRequest);
        if (Objects.isNull(response)) {
            throw new FlowException(FlowBpmExceptionCodeEnum.BPM_FLOW_CREATE_ERROR);
        }
        //设置流程实例id
        request.setWorkflowInstanceId(response.getWorkflowInstanceId());
        updateEntityFlowInfo(entity,request);
        return Boolean.TRUE;
    }


    /**
     * 再次提交
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean reSubmitFlowApply(FlowApplyBizRequest<?> request){
        T entity = getEntityByFlowInstanceId(request.getWorkflowInstanceId());
        reSaveEntity(request);
        //todo 调用BPM流程
        FlowSubmitItemRequest flowSubmitItemRequest = new FlowSubmitItemRequest();
        flowSubmitItemRequest.setWorkItemId(request.getWorkItemId());
        flowSubmitItemRequest.setComment(request.getComment());
        Boolean submitItem = flowBpmWriteFacade.submitItem(flowSubmitItemRequest);
        if (!submitItem) {
            throw new FlowException(FlowBpmExceptionCodeEnum.BPM_FLOW_RESUBMIT_ERROR);
        }
        //修改状态
        updateEntityStatus(entity, request);
        return true;
    }


    /**
     * 审批（通用逻辑）
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditFlowApply(FlowApplyBizRequest<?> request) {
        T entity = getEntityByFlowInstanceId(request.getWorkflowInstanceId());
        FlowBpmRequest flowBpmRequest = new FlowBpmRequest();
        flowBpmRequest.setComment(request.getComment());
        flowBpmRequest.setWorkItemId(request.getWorkItemId());
        flowBpmRequest.setSubmitToReject(request.getSubmitToReject());
        flowBpmRequest.setRejectToActivityCode(request.getRejectToActivityCode());
        flowBpmRequest.setWorkflowInstanceId(request.getWorkflowInstanceId());
        Boolean result = flowBpmWriteFacade.audit(flowBpmRequest);
        updateEntityStatus(entity, request);
        //发送企业微信通知
        CompletableFuture.runAsync(() -> {
            sendNotification(entity,true);
        });
        return result;
    }


    /**
     * 作废
     * @param flowApplyBizRequest
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancel(FlowApplyBizRequest<?> flowApplyBizRequest){
        FlowCancelItemRequest flowCancelItemRequest = new FlowCancelItemRequest();
        BeanUtil.copyProperties(flowApplyBizRequest,flowCancelItemRequest);
        T entity = getEntityByFlowInstanceId(flowApplyBizRequest.getWorkflowInstanceId());
        if (Objects.isNull(entity)){
            throw new BusinessException("数据不存在");
        }
        Boolean cancel = flowBpmWriteFacade.cancelItem(flowCancelItemRequest);
        if (!cancel){
            throw new FlowException(FlowBpmExceptionCodeEnum.BPM_FLOW_CANCEL_ERROR);
        }
        //修改作废状态
        updateEntityStatus(entity,flowApplyBizRequest);
        return cancel;
    }


    /**
     * 批量审批
     * @param requests
     * @return
     */
    public Boolean batchApproveFlowApply(List<FlowApplyBizRequest> requests) {
        //获取bpm里面本人的相对应的instanceId,code,的任务id
        FlowItemExtRequest flowItemExtRequest = new FlowItemExtRequest();
        flowItemExtRequest.setWorkflowcode(requests.get(0).getFlowCode());
        FlowItemExtApiResponse flowItemExtApiResponse = flowBpmReadFacade.workItemExtList(flowItemExtRequest);
        if (flowItemExtApiResponse.getContent().isEmpty()) {
            throw new FlowException(FlowBpmExceptionCodeEnum.BPM_USER_LIST_INSTANCE_NOT_EXIST);
        }

        Map<String, FlowApplyBizRequest> requestMap = requests.stream()
                .collect(Collectors.toMap(
                        FlowApplyBizRequest::getWorkflowInstanceId,
                        Function.identity()
                ));

        //根据instanceId分组，获取所有待办
        Map<String, List<FlowItemExtApiResponse.WorkItem>> workItemMap = flowItemExtApiResponse.getContent()
                .stream()
                .collect(Collectors.groupingBy(FlowItemExtApiResponse.WorkItem::getInstanceId));

        //获取所有的instanceId
        List<String> instanceIds = requests.stream()
                .map(item -> item.getWorkflowInstanceId())
                .collect(Collectors.toList());

        List<T> byWorkflowInstanceIds = getEntityByWorkflowInstanceIds(instanceIds);
        if (byWorkflowInstanceIds.isEmpty()) {
            throw new FlowException(FlowBpmExceptionCodeEnum.BPM_LIST_INSTANCE_NOT_EXIST);
        }
        //组装获取所有待办
        toBatchAudit(byWorkflowInstanceIds, workItemMap, requestMap);
        return Boolean.TRUE;
    }


    /**
     * 获取企业微信id
     * @param workflowInstanceId
     * @param flowStatus
     * @return
     */
    public List<String> getEnterpriseWechatCountList(String workflowInstanceId,String flowStatus) {
        //获取该状态的所有人的工号
        FlowItemExtRequest flowItemExtRequest = new FlowItemExtRequest();
        flowItemExtRequest.setWorkflowInstanceId(workflowInstanceId);
        FlowItemExtApiResponse flowItemExtApiResponse = flowBpmReadFacade.workItemExtListNoUser(flowItemExtRequest);
        if (Objects.isNull(flowItemExtApiResponse)) {
            return null;
        }
        List<FlowItemExtApiResponse.WorkItem> contentList = flowItemExtApiResponse.getContent();
        if (CollUtil.isEmpty(contentList)) {
            return null;
        }

        //获取当前状态未审核的工号
        List<FlowItemExtApiResponse.WorkItem> workItemList = contentList.parallelStream()
                .filter(x -> x.getActivityCode().equals(flowStatus)
                        && x.getApproval().equals(FlowItemApprovalStatusEnum.UNDO.getIndex()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(workItemList)) {
            return null;
        }
        //获取所有的工号
        List<String> userCodeList = workItemList.stream()
                .map(FlowItemExtApiResponse.WorkItem::getOriginator)
                .collect(Collectors.toList());
        FindEmployeeUserInfoByEmployeeCodeListRequest findEmployeeUserInfoByEmployeeCodeListRequest = new FindEmployeeUserInfoByEmployeeCodeListRequest();
        findEmployeeUserInfoByEmployeeCodeListRequest.setEmployeeCodeList(userCodeList);

        //获取用户企业微信id
        Response<List<FindEmployeeUserInfoByEmployeeCodeListResponse>> employeeUserInfoByEmployeeCodeList = innerEmployeeReadFacade.findEmployeeUserInfoByEmployeeCodeList(findEmployeeUserInfoByEmployeeCodeListRequest);
        if (!employeeUserInfoByEmployeeCodeList.isSuccess()) {
            return null;
        }
        List<FindEmployeeUserInfoByEmployeeCodeListResponse> result = employeeUserInfoByEmployeeCodeList.getResult();
        if (CollUtil.isEmpty(result)) {
            return null;
        }

        //获取所有的企业微信账号
        List<String> EnterpriseWechatCountList = result.stream()
                .map(FindEmployeeUserInfoByEmployeeCodeListResponse::getEnterpriseWechatCount)
                .collect(Collectors.toList());
        return EnterpriseWechatCountList;
    }



    /**
     * 保存
     * @param request
     */
    protected abstract T saveEntity(FlowApplyBizRequest<?> request);


    /**
     * 再次提交
     * @param request
     */
    protected abstract Boolean reSaveEntity(FlowApplyBizRequest<?> request);



    /**
     * 修改主表状态
     * @param entity
     * @param request
     */
    protected abstract void updateEntityStatus(T entity, FlowApplyBizRequest<?> request);

    /**
     * 修改流程
     * @param entity
     * @param request
     */
    protected abstract void updateEntityFlowInfo(T entity
            ,FlowApplyBizRequest<?> request);

    /**
     * 根据流程实例ID获取实体
     * @param workflowInstanceId
     * @return
     */
    protected abstract T getEntityByFlowInstanceId(String workflowInstanceId);

    /**
     * 发送企业微信
     * @param entity
     */
    protected abstract Boolean sendNotification(T entity, Boolean isAudit);


    /**
     * 根据流程实例ID批量获取实体
     * @param workflowInstanceIds
     * @return
     */
    protected abstract List<T> getEntityByWorkflowInstanceIds(List<String> workflowInstanceIds);

    /**
     * 调用批量审批
     * @param byWorkflowInstanceIds
     */
    protected abstract void toBatchAudit(List<T> byWorkflowInstanceIds
            , Map<String, List<FlowItemExtApiResponse.WorkItem>> workItemMap
            , Map<String, FlowApplyBizRequest> requestMap);

}