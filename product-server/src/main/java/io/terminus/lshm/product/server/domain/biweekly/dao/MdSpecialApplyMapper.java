package io.terminus.lshm.product.server.domain.biweekly.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyPageRequest;
import io.terminus.lshm.product.server.domain.biweekly.model.MdSpecialApplyPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;

/**
 * 双周MMD特殊申请表 Mapper 接口
 * <AUTHOR>
 */
@Mapper
public interface MdSpecialApplyMapper extends BaseMapper<MdSpecialApplyPO> {

    /**
     * 分页查询md特殊申请列表
     * @param request 查询请求
     * @return 分页响应
     * **/
    Page<MdSpecialApplyTO> pageMdSpecialApply(@Param("page") Page page,@Param("ew") MdSpecialApplyPageRequest request);

    /**
     * 查看md特殊申请详情
     * @param request 查询请求
     * @return 分页响应
     * **/
    MdSpecialApplyTO getMdSpecialApply(@Param("ew") MdSpecialApplyByRequest request);


    /**
     * 新增MD特殊申请（提交审批）
     * @param request 查询请求
     * @return 布尔
     * */
//    int addMdSpecialApply(@Param("ew") MdSpecialApplyByRequest request);


    /**
     * 查询排序
     * **/
    Long  dataCalculation(@Param("ew") MdSpecialApplyByRequest request);



    /**
     * 重新提交时更改MD特殊申请信息
     * */
    Boolean updateMdSpecialApply(@Param("ew") MdSpecialApplyPO request);



}