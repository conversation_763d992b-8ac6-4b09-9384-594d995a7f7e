package io.terminus.lshm.product.server.factory.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import io.terminus.lshm.product.common.enums.AdjustPriceFlowNodeEnum;
import io.terminus.lshm.product.common.enums.AuditStatusEnum;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustOrderRelationPO;
import io.terminus.lshm.product.server.domain.adjust.service.read.AdjustOrderRelationReadService;
import io.terminus.lshm.product.server.domain.adjust.service.write.AdjustOrderRelationWriteService;
import io.terminus.lshm.product.server.enums.AdjustTypeEnum;
import io.terminus.lshm.product.server.external.promotion.PromotionCenterService;
import io.terminus.lshm.product.server.external.promotion.dto.AdjustPriceByPromotionDTO;
import io.terminus.lshm.product.server.factory.handler.AbstractAdjustPriceAfterHandler;
import io.terminus.lshm.product.server.factory.handler.dto.AdjustPriceHandlerDTO;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 后置处理器-
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PromotionStatusChangeAdjustPriceAfterHandler extends AbstractAdjustPriceAfterHandler {


    private final PromotionCenterService promotionCenterService;

    private final AdjustOrderRelationReadService adjustOrderRelationReadService;
    private final AdjustOrderRelationWriteService adjustOrderRelationWriteService;

    /**
     * 创建后置处理方法
     *
     * @param request          创建的请求

     */
    @Override
    protected void after(AdjustPriceHandlerDTO request) {
        log.info("operatorId:{}",ServerContext.getUserInfo().getUserId());
        AdjustPriceByPromotionDTO dto = new AdjustPriceByPromotionDTO();

        dto.setReason(request.getReason());
        dto.setBrandId(request.getAdjustPricePO().getBrandId());
        dto.setOperatorId(ServerContext.getUserInfo().getUserId());
        dto.setOperator(ServerContext.getUserInfo().getNickName());

        List<AdjustOrderRelationPO> relationPOS = adjustOrderRelationReadService.lambdaQuery().select(AdjustOrderRelationPO::getId, AdjustOrderRelationPO::getResJson)
                .eq(AdjustOrderRelationPO::getAdjustId, request.getAdjustPricePO().getId())
                .eq(AdjustOrderRelationPO::getAdjustType, AdjustTypeEnum.EXPIRE_PROMOTION_ORDER.getKey()).list();

        if(CollectionUtils.isEmpty(relationPOS)){
            throw new BusinessException("no promotion Id");
        }
        List<Long> promotionIds = relationPOS.stream().map(AdjustOrderRelationPO::getResJson).map(Long::valueOf).collect(Collectors.toList());
        dto.setPromotionIds(promotionIds);
        //通过
        if(AdjustPriceFlowNodeEnum.COMPLETED.getCode().equals(request.getAuditStatus())){
            dto.setAuditStatus("begin");
       }else {
           //驳回
            dto.setAuditStatus("end");
       }
        //去审批
        promotionCenterService.auditPromotion(dto);
        //没有通过的删除记录
        if(!AdjustPriceFlowNodeEnum.COMPLETED.getCode().equals(request.getAuditStatus())){
            List<Long> ids = relationPOS.stream().map(AdjustOrderRelationPO::getId).collect(Collectors.toList());
            adjustOrderRelationWriteService.removeBatchByIds(ids);
        }
        return;
    }

    /**
     * 创建后置支持性校验方法，仅当校验通过
     * 才会执行后置处理方法
     *
     * @param request 创建请求
     * @return 扩展点执行必要性检查结果
     */
    @Override
    protected Boolean isSupport(AdjustPriceHandlerDTO request) {
        //来源内部自建且是非促销调价
        return Objects.nonNull(request.getAdjustPricePO()) && Objects.nonNull(request.getAuditStatus()) && request.getAdjustPricePO().getFormType() == 3 ;
    }
}