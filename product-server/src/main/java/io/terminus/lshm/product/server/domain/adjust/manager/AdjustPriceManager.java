package io.terminus.lshm.product.server.domain.adjust.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.model.Response;
import io.terminus.gaia.organization.api.facade.InnerEmployeeReadFacade;
import io.terminus.gaia.organization.api.request.FindEmployeeByUserIdsRequest;
import io.terminus.gaia.organization.api.request.FindEmployeeUserInfoByEmployeeCodeListRequest;
import io.terminus.gaia.organization.api.response.FindEmployeeUserInfoByEmployeeCodeListResponse;
import io.terminus.gaia.organization.model.EmployeeBO;
import io.terminus.lshm.flow.request.*;
import io.terminus.lshm.flow.response.FlowItemApprovalStatusEnum;
import io.terminus.lshm.flow.response.FlowItemExtApiResponse;
import io.terminus.lshm.flow.response.FlowListByInstanceResponse;
import io.terminus.lshm.flow.response.FlowStartWithBizResponse;
import io.terminus.lshm.item.common.bean.response.itemMeasure.ItemMeasurePageResponse;
import io.terminus.lshm.item.common.model.item.ItemMeasureUnitTO;
import io.terminus.lshm.item.facade.item.read.ItemMeasureUnitReadFacade;
import io.terminus.lshm.product.common.bean.request.adjust.*;
import io.terminus.lshm.product.common.bean.request.bpm.FlowCancelRequest;
import io.terminus.lshm.product.common.bean.response.adjust.AdjustAuditRecordResponse;
import io.terminus.lshm.product.common.enums.*;
import io.terminus.lshm.product.common.flow.dto.MsgInfo;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.server.domain.adjust.model.*;
import io.terminus.lshm.product.server.domain.adjust.service.read.AdjustAuditRecordReadService;
import io.terminus.lshm.product.server.domain.adjust.service.read.AdjustCategoryPriceReadService;
import io.terminus.lshm.product.server.domain.adjust.service.read.AdjustItemReadService;
import io.terminus.lshm.product.server.domain.adjust.service.read.AdjustStoreReadService;
import io.terminus.lshm.product.server.domain.adjust.service.read.impl.AdjustPriceReadServiceImpl;
import io.terminus.lshm.product.server.domain.adjust.service.write.impl.*;
import io.terminus.lshm.product.server.enums.AdjustTimeStrategyEnum;
import io.terminus.lshm.product.server.external.notice.WxSendService;
import io.terminus.lshm.product.server.facade.flow.FlowBpmReadFacadeImpl;
import io.terminus.lshm.product.server.facade.flow.FlowBpmWriteFacadeImpl;
import io.terminus.lshm.product.server.factory.handler.AbstractAdjustPriceAfterHandler;
import io.terminus.lshm.product.server.factory.handler.dto.AdjustPriceHandlerDTO;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.lshm.server.common.ServerUserInfo;
import io.terminus.lshm.store.common.bean.request.store.read.InnerFindStoreByCodeRequest;
import io.terminus.lshm.store.common.bean.request.store.read.InnerFindStoreByCodesRequest;
import io.terminus.lshm.store.common.model.store.InnerStoreTO;
import io.terminus.lshm.store.facade.store.read.InnerStoreReadFacade;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description
 * @Date 2025/5/21
 */
@Slf4j
@Service
public class AdjustPriceManager {

    @Autowired
    private AdjustPriceWriteServiceImpl adjustPriceWriteService;
    @Autowired
    private AdjustPriceReadServiceImpl adjustPriceReadService;

    @Autowired
    private AdjustStoreWriteServiceImpl adjustStoreWriteService;

    @Autowired
    private AdjustCategoryPriceWriteServiceImpl adjustCategoryPriceWriteService;

    @Autowired
    private AdjustItemWriteServiceImpl adjustItemWriteService;
    @Autowired
    private FlowBpmWriteFacadeImpl flowBpmWriteFacade;

    @Autowired
    private FlowBpmReadFacadeImpl flowBpmReadFacade;

    @Autowired
    private AdjustAuditRecordWriteServiceImpl auditRecordWriteService;

    @Autowired
    private AdjustAuditRecordReadService auditRecordReadService;

    @Autowired
    private AdjustCategoryPriceReadService adjustCategoryPriceReadService;
    @Autowired
    private AdjustItemReadService adjustItemReadService;
    @Autowired
    private AdjustStoreReadService adjustStoreReadService;

    @Autowired
    private WxSendService wxSendService;

    @Autowired
    private InnerEmployeeReadFacade innerEmployeeReadFacade;

    @Autowired
    private ItemMeasureUnitReadFacade itemMeasureUnitReadFacade;

    @Autowired
    private InnerStoreReadFacade innerStoreReadFacade;


    @Transactional(rollbackFor = Exception.class)
    public String adjustPriceApply(FlowApplyBizRequest<AdjustPriceAddRequest> flowApplyBizRequest) {
        AdjustPriceAddRequest request = flowApplyBizRequest.getBizForm();
        AdjustPricePO adjustPricePO = BeanUtil.copyProperties(request, AdjustPricePO.class);
        adjustPriceWriteService.add(adjustPricePO);

        List<AdjustItemPO> adjustItemPOList = BeanUtil.copyToList(request.getItems(), AdjustItemPO.class);
        adjustItemWriteService.add(adjustItemPOList, adjustPricePO.getId());

        List<AdjustStorePO> storePOList = null;
        if (CollUtil.isNotEmpty(request.getStores())) {
            storePOList = BeanUtil.copyToList(request.getStores(), AdjustStorePO.class);
            adjustStoreWriteService.add(storePOList, adjustPricePO.getId());
        }
        List<AdjustCategoryPricePO> adjustCategoryPricePOList = null;
        if (CollUtil.isNotEmpty(request.getCategories())) {
            adjustCategoryPricePOList = BeanUtil.copyToList(request.getCategories(), AdjustCategoryPricePO.class);
            adjustCategoryPriceWriteService.add(adjustCategoryPricePOList, adjustPricePO.getId());
        }
        handlerPromotion(adjustPricePO, adjustItemPOList, storePOList, adjustCategoryPricePOList);

        //todo 调用BPM流程
        FlowStartWithBizRequest flowStartWithBizRequest = new FlowStartWithBizRequest();
        flowStartWithBizRequest.setWorkflowCode(WorkFlowCodeEnum.ADJUST_PRICE.getCode());
        //不是延期则设置adjustType类型
        if (!request.getIsDelay()) {
            String bizCode = AdjustFormTypeEnum.getKeyByValue(request.getFormType());
            request.setAjustType(bizCode);
        }
        flowStartWithBizRequest.setData(flowApplyBizRequest.getBizForm());

        FlowStartWithBizResponse flowStartWithBizResponse = flowBpmWriteFacade.startWithBiz(flowStartWithBizRequest);
        log.info("flowStartWithBizResponse：{}", flowStartWithBizResponse);
        if (Objects.isNull(flowStartWithBizResponse)) {
            throw new BusinessException("调用bpm失败！");
        }
        //编辑状态
        AdjustPricePO adjustPriceUpdate = new AdjustPricePO();
        adjustPriceUpdate.setId(adjustPricePO.getId());
        adjustPriceUpdate.setWorkflowInstanceId(flowStartWithBizResponse.getWorkflowInstanceId());
        adjustPriceWriteService.updateWorkFlow(adjustPriceUpdate);
        //修改原来单据
        if (request.getIsDelay()) {
            AdjustPricePO updateOldAdjust = new AdjustPricePO();
            updateOldAdjust.setId(request.getOldAdjustId());
            updateOldAdjust.setDelayId(adjustPricePO.getId());
            adjustPriceWriteService.updateWorkFlow(updateOldAdjust);
        }
        return adjustPricePO.getAdjustCode();
    }


    /**
     * 验证单位是否存在
     *
     * @param request
     */
    public AdjustPriceAddRequest validImportItem(AdjustPriceAddRequest request) {
        List<AdjustItemAddRequest> items = request.getItems();
        List<AdjustItemAddRequest> isEmptyItemList = items.parallelStream()
                .filter(item -> StrUtil.isBlank(item.getItemId()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(isEmptyItemList)) {
            return request;
        }

        //根据商品编码和商品单位id查询商品
        List<ItemMeasureUnitTO> ItemMeasureUnitTO = isEmptyItemList.stream()
                .map(item -> {
                    ItemMeasureUnitTO itemMeasureUnitTO = new ItemMeasureUnitTO();
                    itemMeasureUnitTO.setItemCode(item.getItemCode());
                    itemMeasureUnitTO.setUnitName(item.getItemUnitName());
                    return itemMeasureUnitTO;
                }).collect(Collectors.toList());

        io.terminus.trantorframework.Response<List<ItemMeasurePageResponse>> findItemByItemCodeAndUnitList = itemMeasureUnitReadFacade.FindItemByItemCodeAndUnit(ItemMeasureUnitTO);
        if (!findItemByItemCodeAndUnitList.getSuccess()) {
            throw new BusinessException("商品中心商品调用失败");
        }
        List<ItemMeasurePageResponse> res = findItemByItemCodeAndUnitList.getRes();
        if (CollUtil.isEmpty(res)) {
            throw new BusinessException("商品中心商品为空");
        }

        // 构建 requestMap：key = itemCode + itemUnitName
        Map<String, ItemMeasurePageResponse> resMap = res.stream()
                .collect(Collectors.toMap(
                        item -> item.getItemCode() + ":" + item.getUnitName(),
                        Function.identity()
                ));

        List<AdjustItemAddRequest> errorItems = new ArrayList<>();
        List<AdjustItemAddRequest> resultItems = request.getItems()
                .stream()
                .filter(item -> StrUtil.isNotBlank(item.getItemId()))
                .collect(Collectors.toList());

        //用于处理集合
        request.getItems()
                .stream()
                .filter(item -> StrUtil.isBlank(item.getItemId()))
                .forEach(item -> {
                    boolean result = resMap.containsKey(item.getItemCode() + ":" + item.getItemUnitName());
                    if (!result) {
                        errorItems.add(item);
                    } else {
                        ItemMeasurePageResponse itemMeasurePageResponse = resMap.get(item.getItemCode() + ":" + item.getItemUnitName());
                        item.setItemId(String.valueOf(itemMeasurePageResponse.getItemId()));
                        item.setItemUnitId(String.valueOf(itemMeasurePageResponse.getUnitId()));
                        item.setMeasureUnitId(
                                itemMeasurePageResponse.getMeasureUnitId() != null
                                        ? String.valueOf(itemMeasurePageResponse.getMeasureUnitId())
                                        : IdUtil.fastSimpleUUID()
                        );
                        resultItems.add(item);
                    }
                });
        request.setItems(resultItems);
        request.setErrorItems(errorItems);
        return request;
    }


    /**
     * 验证门店是否存在
     *
     * @param request
     * @return
     */
    public AdjustPriceAddRequest validImportStore(AdjustPriceAddRequest request) {
        List<AdjustStoreAddRequest> stores = request.getStores();
        List<String> storeCodes = stores.stream().map(store -> store.getStoreCode())
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(storeCodes)) {
            throw new BusinessException("门店为空！");
        }
        InnerFindStoreByCodesRequest innerFindStoreByCodesRequest = new InnerFindStoreByCodesRequest();
        innerFindStoreByCodesRequest.setCodes(storeCodes);
        io.terminus.trantorframework.Response<List<InnerStoreTO>> storeByCodesResponse = innerStoreReadFacade.findStoreByCodes(innerFindStoreByCodesRequest);
        if (!storeByCodesResponse.getSuccess()) {
            throw new BusinessException("门店中心门店调用失败！");
        }
        List<InnerStoreTO> res = storeByCodesResponse.getRes();
        if (CollUtil.isEmpty(res)) {
            throw new BusinessException("调用门店中心门店数据为空");
        }

        Map<String, InnerStoreTO> resMap = res.stream()
                .collect(Collectors.toMap(
                        item -> item.getStoreCode(),
                        Function.identity()
                ));

        List<AdjustStoreAddRequest> errorStores = new ArrayList<>();
        List<AdjustStoreAddRequest> storesItems = new ArrayList<>();

        request.getStores().stream()
                .forEach(item -> {
                    boolean result = resMap.containsKey(item.getStoreCode());
                    if (!result) {
                        errorStores.add(item);
                    } else {
                        InnerStoreTO innerStoreTO = resMap.get(item.getStoreCode());
                        item.setStoreId(innerStoreTO.getId());
                        item.setStoreCode(innerStoreTO.getStoreCode());
                        item.setStoreType(innerStoreTO.getStoreType());
                        item.setStoreName(innerStoreTO.getStoreName());
                        if (StrUtil.isNotBlank(innerStoreTO.getProvince())) {
                            item.setProvinceName(innerStoreTO.getProvince());
                        }
                        if (StrUtil.isNotBlank(innerStoreTO.getCity())) {
                            item.setCityName(innerStoreTO.getCity());
                        }
                        if (StrUtil.isNotBlank(innerStoreTO.getArea())) {
                            item.setAreaName(innerStoreTO.getArea());
                        }
                        if (StrUtil.isNotBlank(innerStoreTO.getProvinceCode())) {
                            item.setProvinceId(Long.valueOf(innerStoreTO.getProvinceCode()));
                        }
                        if (StrUtil.isNotBlank(innerStoreTO.getCityCode())) {
                            item.setCityId(Long.valueOf(innerStoreTO.getCityCode()));
                        }
                        if (StrUtil.isNotBlank(innerStoreTO.getDistrictCode())) {
                            item.setAreaId(Long.valueOf(innerStoreTO.getDistrictCode()));
                        }
                        item.setOrgCode(innerStoreTO.getOrgCode());
                        storesItems.add(item);
                    }
                });
        request.setErrorStores(errorStores);
        request.setStores(storesItems);
        return request;
    }


    public void handlerPromotion(AdjustPricePO adjustPricePO, List<AdjustItemPO> adjustItemPOList, List<AdjustStorePO> storePOList, List<AdjustCategoryPricePO> adjustCategoryPricePOList) {
        //只有活动使用
        AdjustPriceHandlerDTO adjustPriceHandlerDTO = new AdjustPriceHandlerDTO();
        adjustPriceHandlerDTO.setAdjustPricePO(adjustPricePO);
        adjustPriceHandlerDTO.setAdjustItemPOList(adjustItemPOList);
        adjustPriceHandlerDTO.setStorePOList(storePOList);
        adjustPriceHandlerDTO.setCategoryPricePOList(adjustCategoryPricePOList);
        AbstractAdjustPriceAfterHandler.handle(adjustPriceHandlerDTO);
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean reApply(FlowApplyBizRequest<AdjustPriceReRequest> request) {
        AdjustPricePO adjustPricePO = adjustPriceReadService.getByWorkflowInstanceId(request.getWorkflowInstanceId());
        if (Objects.isNull(adjustPricePO)) {
            throw new BusinessException("调价不存在");
        }
        List<AdjustItemPO> adjustItemPOList = null;
        if (CollUtil.isNotEmpty(request.getBizForm().getItems())) {
            adjustItemPOList = BeanUtil.copyToList(request.getBizForm().getItems(), AdjustItemPO.class);
            adjustItemWriteService.reAdd(adjustItemPOList, adjustPricePO.getId());
        }
        List<AdjustStorePO> adjustStorePOList = null;
        if (CollUtil.isNotEmpty(request.getBizForm().getStores())) {
            adjustStorePOList = BeanUtil.copyToList(request.getBizForm().getStores(), AdjustStorePO.class);
            adjustStoreWriteService.reAdd(adjustStorePOList, adjustPricePO.getId());
        }
        List<AdjustCategoryPricePO> adjustCategoryPricePOList = null;
        if (CollUtil.isNotEmpty(request.getBizForm().getCategories())) {
            adjustCategoryPricePOList = BeanUtil.copyToList(request.getBizForm().getCategories(), AdjustCategoryPricePO.class);
            adjustCategoryPriceWriteService.reAdd(adjustCategoryPricePOList, adjustPricePO.getId());
        }
        //再次提交处理促销问题
        handlerPromotion(adjustPricePO, adjustItemPOList, adjustStorePOList,adjustCategoryPricePOList);

        //todo 调用BPM流程
        FlowSubmitItemRequest flowSubmitItemRequest = new FlowSubmitItemRequest();
        flowSubmitItemRequest.setWorkItemId(request.getWorkItemId());
        flowSubmitItemRequest.setComment(request.getComment());
        Boolean submitItem = flowBpmWriteFacade.submitItem(flowSubmitItemRequest);
        if (!submitItem) {
            throw new BusinessException("再次提交失败！");
        }
        AdjustPricePO adjustPriceUpdate = new AdjustPricePO();
        adjustPriceUpdate.setId(adjustPricePO.getId());
        request.setSubmitToReject(true);
        setAdjustPriceStatus(adjustPricePO, request, adjustPriceUpdate);
        adjustPriceUpdate.setReason(request.getBizForm().getReason());
        adjustPriceUpdate.setRemark(request.getBizForm().getRemark());
        adjustPriceUpdate.setBrandId(Long.valueOf(request.getBizForm().getBrandId()));
        if (null!=request.getBizForm().getAdjustNotice()) {
            adjustPriceUpdate.setAdjustNotice(request.getBizForm().getAdjustNotice());
        }
        if (null!=request.getBizForm().getPromotionType()){
            adjustPriceUpdate.setPromotionType(request.getBizForm().getPromotionType());
        }
        if (StrUtil.isNotBlank(request.getBizForm().getFileUrl())){
            adjustPriceUpdate.setFileUrl(request.getBizForm().getFileUrl());
        }
        adjustPriceWriteService.updateWorkFlow(adjustPriceUpdate);
        return Boolean.TRUE;
    }


    public Boolean validReApply(FlowApplyBizRequest<AdjustPriceReRequest> request) {
        AdjustPricePO adjustPricePO = adjustPriceReadService.getByWorkflowInstanceId(request.getWorkflowInstanceId());
        if (Objects.isNull(adjustPricePO)) {
            throw new BusinessException("调价不存在");
        }
        List<AdjustItemPO> adjustItemPOList = BeanUtil.copyToList(request.getBizForm().getItems(), AdjustItemPO.class);
        //查询商品
        LambdaQueryWrapper<AdjustItemPO> adjustItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        adjustItemLambdaQueryWrapper.eq(AdjustItemPO::getAdjustId, adjustPricePO.getId());
        List<AdjustItemPO> adjustItemPOS = adjustItemReadService.list(adjustItemLambdaQueryWrapper);
        if (adjustItemPOS.size() != adjustItemPOList.size()) {
            return true;
        }
        boolean isSameItem = !CollUtil.containsAll(
                adjustItemPOList.stream().map(AdjustItemPO::getItemId).collect(Collectors.toSet()),
                adjustItemPOS.stream().map(AdjustItemPO::getItemId).collect(Collectors.toList())
        );
        if (isSameItem) {
            return true;
        }

        List<AdjustStorePO> adjustStorePOList = BeanUtil.copyToList(request.getBizForm().getStores(), AdjustStorePO.class);
        //比对门店是否一致
        LambdaQueryWrapper<AdjustStorePO> storeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        storeLambdaQueryWrapper.eq(AdjustStorePO::getAdjustId, adjustPricePO.getId());
        List<AdjustStorePO> storeList = adjustStoreReadService.list(storeLambdaQueryWrapper);
        if (storeList.size() != adjustStorePOList.size()) {
            return true;
        }
        boolean isSameStore = !CollUtil.containsAll(
                storeList.stream().map(AdjustStorePO::getStoreId).collect(Collectors.toSet()),
                adjustStorePOList.stream().map(AdjustStorePO::getStoreId).collect(Collectors.toList())
        );
        if (isSameStore) {
            return true;
        }
        return false;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean audit(FlowApplyBizRequest request) {
        AdjustPricePO adjustPricePO = adjustPriceReadService.getByWorkflowInstanceId(request.getWorkflowInstanceId());
        if (Objects.isNull(adjustPricePO)) {
            throw new BusinessException("调价不存在");
        }
        FlowBpmRequest flowBpmRequest = new FlowBpmRequest();
        flowBpmRequest.setComment(request.getComment());
        flowBpmRequest.setWorkItemId(request.getWorkItemId());
        flowBpmRequest.setSubmitToReject(request.getSubmitToReject());
        flowBpmRequest.setRejectToActivityCode(request.getRejectToActivityCode());
        flowBpmRequest.setWorkflowInstanceId(request.getWorkflowInstanceId());
        //设置跳级
        /*if (StrUtil.isNotBlank(request.getRejectToActivityCode())&&request.getSubmitToReject()) {
            AdjustPriceFlowNodeOrderByEnum byFormTypeOrOriginCode = AdjustPriceFlowNodeOrderByEnum.getByFormTypeOrOriginCode(adjustPricePO.getFormType(), adjustPricePO.getAdjustStatus());
            int calculateSkipLevelsTo = byFormTypeOrOriginCode.calculateSkipLevelsTo(request.getRejectToActivityCode());
            flowBpmRequest.setSkipLevels(calculateSkipLevelsTo);
        }*/
        //获取跳级级别
        Boolean audit = flowBpmWriteFacade.audit(flowBpmRequest);
        if (!audit) {
            throw new BusinessException("审核失败！");
        }
        AdjustPricePO adjustPriceUpdate = new AdjustPricePO();
        adjustPriceUpdate.setId(adjustPricePO.getId());
        setAdjustPriceStatus(adjustPricePO, request, adjustPriceUpdate);
        adjustPriceWriteService.updateWorkFlow(adjustPriceUpdate);
        //添加操作记录
        if (!request.getSubmitToReject()) {
            auditRecordWriteService.add(adjustPriceUpdate.getId(), adjustPricePO.getAdjustStatus(),adjustPriceUpdate.getAdjustStatus(), request.getComment(), request.getWorkItemId());
        }
        //活动变更审批状态
        if (AdjustPriceFlowNodeEnum.COMPLETED.getCode().equals(adjustPriceUpdate.getAdjustStatus()) || AdjustPriceFlowNodeEnum.ADJUST_PRICE_START.getCode().equals(adjustPriceUpdate.getAdjustStatus())) {
            handlerAdjustOrder(adjustPricePO, request.getComment(), adjustPriceUpdate.getAdjustStatus());
        }
        //发送企业微信通知
        CompletableFuture.runAsync(() -> {
            flowUp(adjustPriceUpdate.getId(), true);
        });
        return Boolean.TRUE;
    }

    public void handlerAdjustOrder(AdjustPricePO adjustPricePO, String comment, String AdjustStatus) {
        AdjustPriceHandlerDTO adjustPriceHandlerDTO = new AdjustPriceHandlerDTO();
        adjustPriceHandlerDTO.setAdjustPricePO(adjustPricePO);
        adjustPriceHandlerDTO.setAuditStatus(AdjustStatus);
        adjustPriceHandlerDTO.setReason(comment);
        AbstractAdjustPriceAfterHandler.handle(adjustPriceHandlerDTO);
    }

    /**
     * * 测试
     *
     * @param request
     * @return
     */
    public Boolean testRetryPriceCenter(FlowApplyBizRequest request) {
        setTestUserInfo();
        AdjustPricePO adjustPricePO = adjustPriceReadService.getById(request.getWorkflowInstanceId());
        if (Objects.isNull(adjustPricePO)) {
            throw new BusinessException("调价不存在");
        }
        handlerAdjustOrder(adjustPricePO, request.getComment(), request.getSubmitToReject() ? AdjustPriceFlowNodeEnum.COMPLETED.getCode() : AdjustPriceFlowNodeEnum.ADJUST_PRICE_START.getCode());
        return Boolean.TRUE;
    }

    private void setTestUserInfo() {
        ServerUserInfo userInfo = new ServerUserInfo();
        userInfo.setUserId(1L);
        userInfo.setNickName("admin");
        ServerContext.setUserInfo(userInfo);
    }

    /**
     * 测试补偿等*
     *
     * @param request
     * @return
     */
    public Boolean testRetryPromotion(FlowApplyBizRequest request) {
        setTestUserInfo();
        AdjustPricePO adjustPricePO = adjustPriceReadService.getById(request.getWorkflowInstanceId());
        if (Objects.isNull(adjustPricePO)) {
            throw new BusinessException("调价不存在");
        }
        List<AdjustStorePO> storeList = adjustStoreWriteService.lambdaQuery().eq(AdjustStorePO::getAdjustId, adjustPricePO.getId()).list();
        List<AdjustItemPO> adjustItemPOS = adjustItemWriteService.lambdaQuery().eq(AdjustItemPO::getAdjustId, adjustPricePO.getId()).list();
        List<AdjustCategoryPricePO> adjustCategoryPricePOList = adjustCategoryPriceWriteService.lambdaQuery().eq(AdjustCategoryPricePO::getAdjustId, adjustPricePO.getId()).list();
        handlerPromotion(adjustPricePO, adjustItemPOS, storeList, adjustCategoryPricePOList);
        return Boolean.TRUE;
    }


    /**
     * 获取节点超时时间
     *
     * @param adjustPricePO
     * @return
     */
    private AdjustBpmExpireTime setAdjustBpmExpireTime(AdjustPricePO adjustPricePO, FlowApplyBizRequest request) {
        if (StrUtil.isBlank(request.getRejectToActivityCode())) {
            if (adjustPricePO.getAdjustStatus().equals(AdjustPriceFlowNodeEnum.PRODUCT_CENTER_LEADER_APPROVAL.getCode())
                    && adjustPricePO.getFormType().equals(AdjustFormTypeEnum.NATIONAL_AREA.getCode())) {
                return AdjustBpmExpireTime.builder()
                        .vicePersidentDate(AdjustTimeStrategyEnum.addHoursToDate(4))
                        .build();
            }
            if (adjustPricePO.getAdjustStatus().equals(AdjustPriceFlowNodeEnum.PRODUCT_DEPUTY_DIRECTOR_APPROVAL.getCode())
                    && adjustPricePO.getFormType().equals(AdjustFormTypeEnum.NATIONAL_AREA.getCode())) {
                return AdjustBpmExpireTime.builder()
                        .vicePresidentTimeOut(AdjustTimeStrategyEnum.addHoursToDate(4))
                        .build();
            }
            if (adjustPricePO.getAdjustStatus().equals(AdjustPriceFlowNodeEnum.STORE_SUPERVISOR.getCode())
                    && adjustPricePO.getFormType().equals(AdjustFormTypeEnum.COMPETITIVE_PROMOTION.getCode())) {
                return AdjustBpmExpireTime.builder()
                        .vicePresidentZoTimeOut(AdjustTimeStrategyEnum.addHoursToDate(4))
                        .build();
            }

        }
        // 调用策略枚举获取结果
        AdjustBpmExpireTime expireTime = AdjustTimeStrategyEnum.apply(adjustPricePO, request);
        return expireTime;
    }


    /**
     * 批量审核
     *
     * @param requests
     * @return
     */
    public Boolean batchAudit(List<FlowApplyBizRequest> requests) {
        //获取bpm里面本人的相对应的instanceId,code,的任务id
        FlowItemExtRequest flowItemExtRequest = new FlowItemExtRequest();
        flowItemExtRequest.setWorkflowcode(WorkFlowCodeEnum.ADJUST_PRICE.getCode());
        FlowItemExtApiResponse flowItemExtApiResponse = flowBpmReadFacade.workItemExtList(flowItemExtRequest);
        if (flowItemExtApiResponse.getContent().isEmpty()) {
            throw new BusinessException("该账号未存在待办");
        }

        Map<String, FlowApplyBizRequest> requestMap = requests.stream()
                .collect(Collectors.toMap(
                        FlowApplyBizRequest::getWorkflowInstanceId,
                        Function.identity()
                ));

        //根据instanceId分组，获取所有待办
        Map<String, List<FlowItemExtApiResponse.WorkItem>> workItemMap = flowItemExtApiResponse.getContent()
                .stream()
                .collect(Collectors.groupingBy(FlowItemExtApiResponse.WorkItem::getInstanceId));

        //获取所有的instanceId
        List<String> instanceIds = requests.stream()
                .map(item -> item.getWorkflowInstanceId())
                .collect(Collectors.toList());


        List<AdjustPricePO> byWorkflowInstanceIds = adjustPriceReadService.getByWorkflowInstanceIds(instanceIds);
        if (byWorkflowInstanceIds.isEmpty()) {
            throw new BusinessException("待办信息不存在");
        }

        //组装获取所有待办
        byWorkflowInstanceIds.stream().forEach(
                po -> {
                    //获取bpm映射节点
                    AdjustPriceFlowNodeOrderByEnum adjustPriceFlowNodeOrderByEnum = AdjustPriceFlowNodeOrderByEnum.getByFormTypeOrOriginCode(po.getFormType(), po.getAdjustStatus());

                    List<FlowItemExtApiResponse.WorkItem> workItem = workItemMap.get(po.getWorkflowInstanceId())
                            .parallelStream()
                            .filter(item -> item.getActivityCode().equals(adjustPriceFlowNodeOrderByEnum.getCode()))
                            .collect(Collectors.toList());

                    FlowApplyBizRequest flowApplyBizRequest = requestMap.get(po.getWorkflowInstanceId());
                    flowApplyBizRequest.setWorkItemId(workItem.get(0).getId());
                    //调用审批
                    this.audit(flowApplyBizRequest);
                }
        );
        return Boolean.TRUE;
    }


    /**
     * 获取流程状态
     *
     * @param request
     * @param adjustPricePO
     */
    private void setAdjustPriceStatus(AdjustPricePO adjustPricePO, FlowApplyBizRequest request, AdjustPricePO updatePricePO) {
        //驳回
        if (!request.getSubmitToReject()){
            AdjustPriceFlowNodeOrderByEnum formTypeOrCode = AdjustPriceFlowNodeOrderByEnum.getByFormTypeOrCode(adjustPricePO.getFormType(), request.getRejectToActivityCode());
            updatePricePO.setAdjustStatus(formTypeOrCode.getOriginalCode());
            return;
        }
        //审核通过，查询当前状态之前是否有驳回状态，如果有则取上一个状态,没有则按顺序执行
        AdjustAuditRecordPO lastAuditRecord = auditRecordReadService.getLastAuditRecord(adjustPricePO.getId(), adjustPricePO.getAdjustStatus());
        if (!Objects.isNull(lastAuditRecord)){
            AdjustPriceFlowNodeOrderByEnum formTypeOrCode = AdjustPriceFlowNodeOrderByEnum.getByFormTypeOrOriginCode(adjustPricePO.getFormType(), lastAuditRecord.getAuditFlowNode());
            updatePricePO.setAdjustStatus(formTypeOrCode.getOriginalCode());
            return;
        }
        AdjustPriceFlowNodeOrderByEnum formTypeOrCode = AdjustPriceFlowNodeOrderByEnum.getByFormTypeOrOriginCode(adjustPricePO.getFormType(), adjustPricePO.getAdjustStatus());
        AdjustPriceFlowNodeOrderByEnum nextNode = AdjustPriceStateMachineEnum.getNextNode(formTypeOrCode);
        updatePricePO.setAdjustStatus(nextNode.getOriginalCode());
    }


    /**
     * 作废
     *
     * @param request
     * @return
     */
    public Boolean cancel(FlowCancelRequest request) {
        FlowCancelItemRequest flowCancelItemRequest = new FlowCancelItemRequest();
        BeanUtil.copyProperties(request, flowCancelItemRequest);
        AdjustPricePO adjustPricePO = adjustPriceReadService.getByWorkflowInstanceId(request.getWorkflowInstanceId());
        if (Objects.isNull(adjustPricePO)) {
            throw new BusinessException("调价不存在");
        }

        //如果是促销取消调价
        handlerAdjustOrder(adjustPricePO, "作废", AdjustPriceFlowNodeEnum.CANCELLED.getCode());
        //修改作废状态
        AdjustPricePO adjustPriceUpdate = new AdjustPricePO();
        adjustPriceUpdate.setId(adjustPricePO.getId());
        adjustPriceUpdate.setAdjustStatus(AdjustPriceFlowNodeEnum.CANCELLED.getCode());
        adjustPriceWriteService.updateWorkFlow(adjustPriceUpdate);
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean flowUp(Long id, Boolean isAudit) {
        //查询调价数据
        AdjustPricePO adjustPrice = adjustPriceReadService.getById(id);
        if (!isAudit) {
            validFlowUp(adjustPrice);
        }
        //获取企业微信id
        List<String> enterpriseWechatCountList = getEnterpriseWechatCountList(adjustPrice);
        if (CollUtil.isEmpty(enterpriseWechatCountList)) {
            return false;
        }
       /* List<String> enterpriseWechatCountList = new ArrayList<>();
        enterpriseWechatCountList.add("***********");*/

        List<MsgInfo> sendInfoList = new ArrayList<>();
        MsgInfo msgInfo = new MsgInfo();
        String h5DetailUrl = BusinessTypeEnum.getByCode(BusinessTypeEnum.ADJUST_PRICE.getCode()).getH5DetailUrl();
        String h5Url = String.format(h5DetailUrl, AdjustFormTypeEnum.getKeyByValue(adjustPrice.getFormType()), adjustPrice.getId(), adjustPrice.getAdjustStatus());
        msgInfo.setUrl(h5Url);

        //查询调价列表，取活动时间
        LambdaQueryWrapper<AdjustCategoryPricePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdjustCategoryPricePO::getAdjustId, adjustPrice.getId());
        queryWrapper.orderByDesc(AdjustCategoryPricePO::getDeliveryTimeStart);
        List<AdjustCategoryPricePO> categoryPriceList = adjustCategoryPriceReadService.list(queryWrapper);

        //取最新的生效时间
        AdjustCategoryPricePO adjustCategoryPrice = categoryPriceList.get(0);
        msgInfo.setActivityEffectTime(DateUtil.formatDate(adjustCategoryPrice.getDeliveryTimeStart()));
        msgInfo.setCreatedBy(adjustPrice.getCreatedName());
        msgInfo.setBusinessId(adjustPrice.getId().toString());
        msgInfo.setAccountIds(enterpriseWechatCountList);
        //商品调价流程名称-创建人-日期
        String title = "您有流程待审批: " + adjustPrice.getTitile();
        msgInfo.setTitle(title);
        sendInfoList.add(msgInfo);
        wxSendService.handlerSendWechatAppAuditPendingMsgAdjustPricePC(sendInfoList);
        //更新发送时间
        if (!isAudit) {
            AdjustPricePO newAdjustPrice = new AdjustPricePO();
            newAdjustPrice.setId(adjustPrice.getId());
            newAdjustPrice.setLastSendTime(new Date());
            adjustPriceWriteService.updateById(newAdjustPrice);
        }
        return true;
    }

    /**
     *
     * @param id
     * @return
     */
    public Boolean flowUpDelay(Long id) {
        //查询调价数据
        AdjustPricePO adjustPrice = adjustPriceReadService.getById(id);
        List<Long> wechatUserIds = Arrays.asList(adjustPrice.getCreatedBy());
        List<String> enterpriseWechatCountList = getEnterpriseWechatDelayCountList(wechatUserIds);
        if (CollUtil.isEmpty(enterpriseWechatCountList)) {
            return false;
        }
        List<MsgInfo> sendInfoList = new ArrayList<>();
        MsgInfo msgInfo = new MsgInfo();
        String h5DetailUrl = BusinessTypeEnum.getByCode(BusinessTypeEnum.ADJUST_PRICE.getCode()).getH5DetailUrl();
        String h5Url = String.format(h5DetailUrl, AdjustFormTypeEnum.getKeyByValue(adjustPrice.getFormType()), adjustPrice.getId(), adjustPrice.getAdjustStatus());
        msgInfo.setUrl(h5Url);

        //查询调价列表，取活动时间
        LambdaQueryWrapper<AdjustCategoryPricePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdjustCategoryPricePO::getAdjustId, adjustPrice.getId());
        queryWrapper.orderByDesc(AdjustCategoryPricePO::getDeliveryTimeStart);
        List<AdjustCategoryPricePO> categoryPriceList = adjustCategoryPriceReadService.list(queryWrapper);

        //取最新的生效时间
        AdjustCategoryPricePO adjustCategoryPrice = categoryPriceList.get(0);
        msgInfo.setActivityEffectTime(DateUtil.formatDate(adjustCategoryPrice.getDeliveryTimeEnd()));
        msgInfo.setCreatedBy(adjustPrice.getCreatedName());
        msgInfo.setBusinessId(adjustPrice.getId().toString());
        msgInfo.setAccountIds(enterpriseWechatCountList);
        //商品调价流程名称-创建人-日期
        String title = "商品调价流程失效: " + adjustPrice.getTitile();
        msgInfo.setTitle(title);
        sendInfoList.add(msgInfo);
        wxSendService.handlerSendWechatAppAuditPendingMsgAdjustPricePC(sendInfoList);
        return true;
    }


    /**
     * 获取企业微信id
     *
     * @param adjustPrice
     * @return
     */
    private List<String> getEnterpriseWechatCountList(AdjustPricePO adjustPrice) {
        String workflowInstanceId = adjustPrice.getWorkflowInstanceId();
        //获取该状态的所有人的工号
        FlowItemExtRequest flowItemExtRequest = new FlowItemExtRequest();
        flowItemExtRequest.setWorkflowInstanceId(workflowInstanceId);
        FlowItemExtApiResponse flowItemExtApiResponse = flowBpmReadFacade.workItemExtListNoUser(flowItemExtRequest);
        if (Objects.isNull(flowItemExtApiResponse)) {
            return null;
        }
        List<FlowItemExtApiResponse.WorkItem> contentList = flowItemExtApiResponse.getContent();
        if (CollUtil.isEmpty(contentList)) {
            return null;
        }
        AdjustPriceFlowNodeOrderByEnum formTypeOrCode = AdjustPriceFlowNodeOrderByEnum.getByFormTypeOrOriginCode(adjustPrice.getFormType(), adjustPrice.getAdjustStatus());

        //获取当前状态未审核的工号
        List<FlowItemExtApiResponse.WorkItem> workItemList = contentList.parallelStream()
                .filter(x -> x.getActivityCode().equals(formTypeOrCode.getCode())
                        && x.getApproval().equals(FlowItemApprovalStatusEnum.UNDO.getIndex()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(workItemList)) {
            return null;
        }

        //获取所有的工号
        List<String> userCodeList = workItemList.stream()
                .map(FlowItemExtApiResponse.WorkItem::getOriginator)
                .collect(Collectors.toList());
        FindEmployeeUserInfoByEmployeeCodeListRequest findEmployeeUserInfoByEmployeeCodeListRequest = new FindEmployeeUserInfoByEmployeeCodeListRequest();
        findEmployeeUserInfoByEmployeeCodeListRequest.setEmployeeCodeList(userCodeList);

        //获取用户企业微信id
        Response<List<FindEmployeeUserInfoByEmployeeCodeListResponse>> employeeUserInfoByEmployeeCodeList = innerEmployeeReadFacade.findEmployeeUserInfoByEmployeeCodeList(findEmployeeUserInfoByEmployeeCodeListRequest);
        if (!employeeUserInfoByEmployeeCodeList.isSuccess()) {
            return null;
        }
        List<FindEmployeeUserInfoByEmployeeCodeListResponse> result = employeeUserInfoByEmployeeCodeList.getResult();
        if (CollUtil.isEmpty(result)) {
            return null;
        }

        //获取所有的企业微信账号
        List<String> EnterpriseWechatCountList = result.stream()
                .map(FindEmployeeUserInfoByEmployeeCodeListResponse::getEnterpriseWechatCount)
                .collect(Collectors.toList());
        return EnterpriseWechatCountList;
    }


    /**
     * 获取延期企业微信id
     *
     * @param userIds
     * @return
     */
    private List<String> getEnterpriseWechatDelayCountList(List<Long> userIds) {
        FindEmployeeByUserIdsRequest findEmployeeByUserIdsRequest = new FindEmployeeByUserIdsRequest();
        findEmployeeByUserIdsRequest.setUserIds(userIds);
        //获取所有的工号
        Response<List<EmployeeBO>> employeeByUserIdsResponse = innerEmployeeReadFacade.findEmployeeByUserIds(findEmployeeByUserIdsRequest);
        if (!employeeByUserIdsResponse.isSuccess()){
            return null;
        }
        List<EmployeeBO> employeeBOList = employeeByUserIdsResponse.getResult();
        if (CollUtil.isEmpty(employeeBOList)){
            return null;
        }

        List<String> thirdUidList = employeeBOList.stream()
                .filter(x -> StrUtil.isNotBlank(x.getThirdUid()))
                .map(EmployeeBO::getThirdUid)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(thirdUidList)){
            return null;
        }

        return thirdUidList;
    }


    public void validFlowUp(AdjustPricePO adjustPrice) {
        if (Objects.isNull(adjustPrice)) {
            throw new BusinessException("调价单不存在");
        }

        if (
                adjustPrice.getAdjustStatus().equals(AdjustPriceFlowNodeOrderByEnum.COMPLETED.getCode())
                        || adjustPrice.getAdjustStatus().equals(AdjustPriceFlowNodeOrderByEnum.ADJUST_PRICE_COMPLETED.getCode())
                        || adjustPrice.getAdjustStatus().equals(AdjustPriceFlowNodeOrderByEnum.CANCELLED.getCode())
                        || adjustPrice.getAdjustStatus().equals(AdjustPriceFlowNodeOrderByEnum.ADJUST_PRICE_ANOMALY.getCode())) {
            throw new BusinessException("当前调价单状态不允许催办");
        }
        //校验上次发送时间是否超过6小时
        if (ObjectUtil.isNotEmpty(adjustPrice.getLastSendTime())) {
            if (DateUtil.between(new Date(), adjustPrice.getLastSendTime(), DateUnit.HOUR) < 6) {
                throw new BusinessException("6小时内只能进行一次催办");
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean delay(AdjustDelayRequest adjustDelayRequest) {
        AdjustPricePO delayAdjustPrice = adjustPriceReadService.getOne(new LambdaQueryWrapper<AdjustPricePO>().eq(AdjustPricePO::getDelayId, adjustDelayRequest.getAdjustId()));
        if (!Objects.isNull(delayAdjustPrice)) {
            throw new BusinessException("该调价单已存在延期");
        }
        //查询调价数据
        AdjustPricePO oldAdjustPrice = adjustPriceReadService.getById(adjustDelayRequest.getAdjustId());
        LambdaQueryWrapper<AdjustCategoryPricePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdjustCategoryPricePO::getAdjustId, oldAdjustPrice.getId());
        queryWrapper.orderByDesc(AdjustCategoryPricePO::getDeliveryTimeStart);
        List<AdjustCategoryPricePO> oldCategoryPriceList = adjustCategoryPriceReadService.list(queryWrapper);
        validDelay(adjustDelayRequest, oldAdjustPrice, oldCategoryPriceList.get(0));
        AdjustPriceAddRequest adjustPriceAddRequest = BeanUtil.copyProperties(oldAdjustPrice, AdjustPriceAddRequest.class);
        adjustPriceAddRequest.setPostponementRoutine(AdjustFormTypeEnum.getKeyByValue(oldAdjustPrice.getFormType()));
        List<AdjustCategoryPriceAddRequest> categoriesRequest = Convert.toList(AdjustCategoryPriceAddRequest.class, oldCategoryPriceList);
        categoriesRequest.forEach(x -> {
            x.setDeliveryTimeStart(adjustDelayRequest.getActivityTimeStart());
            x.setDeliveryTimeEnd(adjustDelayRequest.getActivityTimeEnd());
        });
        adjustPriceAddRequest.setCategories(categoriesRequest);
        //查询关联的商品
        List<AdjustItemPO> oldAdjustItemList = adjustItemReadService.list(new LambdaQueryWrapper<AdjustItemPO>().eq(AdjustItemPO::getAdjustId, adjustDelayRequest.getAdjustId()));
        oldAdjustItemList.forEach(item -> item.setId(null));
        List<AdjustItemAddRequest> items = Convert.toList(AdjustItemAddRequest.class, oldAdjustItemList);
        adjustPriceAddRequest.setItems(items);
        //查询关联的门店
        List<AdjustStorePO> oldAdjustStoreList = adjustStoreReadService.list(new LambdaQueryWrapper<AdjustStorePO>().eq(AdjustStorePO::getAdjustId, adjustDelayRequest.getAdjustId()));
        oldAdjustStoreList.forEach(item -> item.setId(null));
        List<AdjustStoreAddRequest> stores = Convert.toList(AdjustStoreAddRequest.class, oldAdjustStoreList);
        adjustPriceAddRequest.setStores(stores);
        adjustPriceAddRequest.setIsDelay(true);
        adjustPriceAddRequest.setOldAdjustId(oldAdjustPrice.getId());
        //新发起一个流程
        FlowApplyBizRequest<AdjustPriceAddRequest> flowApplyBizRequest = new FlowApplyBizRequest<>();
        flowApplyBizRequest.setBizForm(adjustPriceAddRequest);
        adjustPriceApply(flowApplyBizRequest);
        return true;
    }


    public void validDelay(AdjustDelayRequest adjustDelayRequest, AdjustPricePO adjustPrice, AdjustCategoryPricePO oldAdjustCategoryPrice) {

        if (Objects.isNull(adjustPrice)) {
            throw new BusinessException("调价单不存在");
        }
        if (adjustPrice.getFormType().equals(AdjustFormTypeEnum.NATIONAL_AREA.getCode())) {
            throw new BusinessException("当前调价类型不支持延期");
        }
        if (!adjustPrice.getAdjustStatus().equals(AdjustPriceFlowNodeOrderByEnum.COMPLETED.getCode())) {
            throw new BusinessException("当前调价单状态不允许延期");
        }
        if (adjustDelayRequest.getActivityTimeStart().compareTo(oldAdjustCategoryPrice.getDeliveryTimeStart()) <= 0) {
            throw new BusinessException("延期生效开始时间只能选择已有的生效时间内");
        }
        if (adjustPrice.getFormType().equals(AdjustFormTypeEnum.COMPETITIVE_PROMOTION.getCode())) {
            //竞争促销调价校验结束时间是否超出一个月
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(oldAdjustCategoryPrice.getDeliveryTimeStart());
            calendar.add(Calendar.MONTH, 1);
            Date endTime = calendar.getTime();
            if (adjustDelayRequest.getActivityTimeEnd().compareTo(endTime) > 0) {
                throw new BusinessException("结束时间最长为一个月");
            }
        }
        if (adjustPrice.getFormType().equals(AdjustFormTypeEnum.EXPIRATION_DATE.getCode())) {
            //临期滞销校验开始和结束时间是否超出15天
            if (DateUtil.between(adjustDelayRequest.getActivityTimeStart(), adjustDelayRequest.getActivityTimeEnd(), DateUnit.DAY) > 15) {
                throw new BusinessException("结束时间不能超过15天");
            }


        }
    }


}
