package io.terminus.lshm.product.server.converter.biweeklyMd;

import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.server.converter.BasicConvert;
import io.terminus.lshm.product.server.domain.biweekly.model.MdAuditRecordPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @Description 月度MD审批记录转换器
 * <AUTHOR>
 * @date 2025/7/9 11:49
 */
@Mapper(componentModel = "spring")
public interface MdAuditRecordConverter extends BasicConvert<MdAuditRecordTO, MdAuditRecordPO> {

    List<MdAuditRecordTO> listP2T(List<MdAuditRecordPO> list);
}
