package io.terminus.lshm.product.server.converter;

import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskSpecificationPriceResponse;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskSpecificationPriceTO;
import io.terminus.lshm.product.server.domain.recordFiling.model.RecordFilingItemTaskSpecificationPriceDraftPO;
import io.terminus.lshm.product.server.domain.recordFiling.model.RecordFilingItemTaskSpecificationPricePO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RecordFilingItemTaskSpecificationPriceConverter extends BasicConvert<RecordFilingItemTaskSpecificationPriceTO, RecordFilingItemTaskSpecificationPricePO> {
    List<RecordFilingItemTaskSpecificationPriceResponse> listP2ListRsp(List<RecordFilingItemTaskSpecificationPricePO> poList);

    List<RecordFilingItemTaskSpecificationPriceTO> draftList2TList(List<RecordFilingItemTaskSpecificationPriceDraftPO> poList);
}
