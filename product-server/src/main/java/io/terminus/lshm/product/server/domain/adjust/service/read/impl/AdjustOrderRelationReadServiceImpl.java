package io.terminus.lshm.product.server.domain.adjust.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import groovy.util.logging.Slf4j;
import io.terminus.lshm.product.server.domain.adjust.dao.AdjustOrderRelationMapper;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustOrderRelationPO;
import io.terminus.lshm.product.server.domain.adjust.service.read.AdjustOrderRelationReadService;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class AdjustOrderRelationReadServiceImpl extends ServiceImpl<AdjustOrderRelationMapper, AdjustOrderRelationPO> implements AdjustOrderRelationReadService {
}
