package io.terminus.lshm.product.server.domain.biweekly.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyPageRequest;
import io.terminus.lshm.product.common.offshelf.dto.EmployeeUsersTO;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.server.domain.biweekly.model.MdSpecialApplyPO;
import io.terminus.lshm.store.common.bean.request.store.read.InnerPageAllRequest;
import io.terminus.lshm.store.common.model.store.InnerStoreTO;
import io.terminus.trantorframework.Paging;
import org.springframework.web.bind.annotation.RequestBody;

import io.terminus.trantorframework.Response;

import java.util.List;

/**
 * @Description 双周MD读取服务接口
 * <AUTHOR>
 * @date 2025/6/19 15:18
 */
public interface MdSpecialApplyReadService extends IService<MdSpecialApplyPO> {


    MdSpecialApplyPO getByWorkflowInstanceId(String workflowInstanceId);

    List<MdSpecialApplyPO> getByWorkflowInstanceIds(List<String> workflowInstanceIds);


    /**
     * 分页查询md特殊申请列表
     * @param request 查询请求
     * @return 分页响应
     * **/
    Paging<MdSpecialApplyTO> pageMdSpecialApply(@RequestBody MdSpecialApplyPageRequest request);



    /**
     * 调用门店中心的列表接口获取门店数据（新建和列表门店查询时都是这个接口，都需要获取所有门店数据）
     * @param request 请求参数（如果需要）
     * @return 门店分页数据
     */
    Response<Paging<InnerStoreTO>> getStoreList(@RequestBody InnerPageAllRequest request);


    /**
     * 查看md特殊申请详情
     * @param request 查询请求
     * @return 分页响应
     * **/
    Response<MdSpecialApplyTO> getMdSpecialApply(@RequestBody MdSpecialApplyByRequest request);



    /**
     * 查看详情时获门店信息，先查询出门店关联表中的门店id，在根据门店id去查询门店表，分页返回门店信息
     * */
    Response<List<MdRelationalStoreTO>> getRelationalStoreInfo(@RequestBody MdSpecialApplyPageRequest request);



    /**
     * 点击新建时，把申请人的昵称，手机号，还有申请日期放回给前端
     * */
    Response<EmployeeUsersTO> echoUser(@RequestBody MdSpecialApplyByRequest request);


    /**
     * 点击审批或者查看时，获取审批记录信息
     * */
    Response<List<MdAuditRecordTO>> getApprovalRecord(@RequestBody MdSpecialApplyByRequest request);



}
