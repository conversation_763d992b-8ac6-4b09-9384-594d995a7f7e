package io.terminus.lshm.product.server.factory.handler.impl;

import com.google.common.collect.Lists;
import io.terminus.lshm.product.server.converter.AdjustOrderRelationConverter;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustItemPO;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustStorePO;
import io.terminus.lshm.product.server.domain.adjust.service.write.AdjustOrderRelationWriteService;
import io.terminus.lshm.product.server.external.promotion.PromotionCenterService;
import io.terminus.lshm.product.server.factory.handler.AbstractAdjustPriceAfterHandler;
import io.terminus.lshm.product.server.factory.handler.dto.AdjustPriceHandlerDTO;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 后置处理器
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PromotionAdjustPriceAfterHandler extends AbstractAdjustPriceAfterHandler {

    private final PromotionCenterService promotionCenterService;

    private final AdjustOrderRelationWriteService adjustOrderRelationWriteService;

    private final AdjustOrderRelationConverter adjustOrderRelationConverter;
    
    /**
     * 创建后置处理方法
     *
     * @param request          创建的请求
     */
    @Override
    protected void after(AdjustPriceHandlerDTO request) {
        if (Objects.isNull(request.getAdjustPricePO().getPromotionId()) || (Objects.nonNull(request.getAdjustPricePO().getSource()) && request.getAdjustPricePO().getSource() == 1)) {
            //在这里做分组处理吧
            // 分组处理
            List<List<AdjustItemPO>> groupedAdjustItemPOList = Lists.partition(request.getAdjustItemPOList(), PARTITION_SIZE);
            List<List<AdjustStorePO>> groupedStorePOList = Lists.partition(request.getStorePOList(), PARTITION_SIZE);
            // 对每个分组调用 after 方法
            for (List<AdjustItemPO> group : groupedAdjustItemPOList) {
                for (List<AdjustStorePO> storeGroup : groupedStorePOList) {
//                    try {
                        AdjustPriceHandlerDTO adjustPriceHandlerDTO = new AdjustPriceHandlerDTO();
                        adjustPriceHandlerDTO.setAdjustPricePO(request.getAdjustPricePO());
                        adjustPriceHandlerDTO.setAdjustItemPOList(group);
                        adjustPriceHandlerDTO.setStorePOList(storeGroup);
                        adjustPriceHandlerDTO.setCategoryPricePOList(request.getCategoryPricePOList());
                        String promotion = promotionCenterService.createPromotion(adjustPriceHandlerDTO);
                        if(Objects.isNull(promotion)){
                            throw new BusinessException("createPromotion error");
                        }
                        adjustOrderRelationWriteService.save(adjustOrderRelationConverter.toPromotion(request,promotion));
//                    } catch (Exception e) {
//                        log.error("createPromotion error",e);
//                    }
                }
            }
           
        }else {
            String promotion =request.getAdjustPricePO().getPromotionId()+"";
            adjustOrderRelationWriteService.save(adjustOrderRelationConverter.toPromotion(request,promotion));
        }
    }

    /**
     * 创建后置支持性校验方法，仅当校验通过
     * 才会执行后置处理方法
     *
     * @param request 创建请求
     * @return 扩展点执行必要性检查结果
     */
    @Override
    protected Boolean isSupport(AdjustPriceHandlerDTO request) {
        //来源内部自建且是促销调价
        return Objects.nonNull(request.getAdjustPricePO()) && request.getAdjustPricePO().getFormType() == 3 && Objects.isNull(request.getAuditStatus());
    }
}