package io.terminus.lshm.product.server.converter;

import io.terminus.api.utils.ParamUtil;
import io.terminus.lshm.product.common.enums.AuditBusinessKeyEnum;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskTO;
import io.terminus.lshm.product.common.recordfiling.request.BatchRecordFilingItemTaskRequest;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskRequest;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingTaskAuditRequest;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskDraftResponse;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskPageResponse;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskResponse;
import io.terminus.lshm.product.common.recordfiling.response.TaxRateResponse;
import io.terminus.lshm.product.server.domain.recordFiling.model.RecordFilingItemTaskDraftPO;
import io.terminus.lshm.product.server.domain.recordFiling.model.RecordFilingItemTaskPO;
import io.terminus.lshm.product.server.domain.recordFiling.model.TaxRateDataPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring", uses = {FlowConfigConverter.class})
public interface RecordFilingItemTaskConverter extends BasicConvert<RecordFilingItemTaskTO, RecordFilingItemTaskPO> {
    RecordFilingItemTaskRequest draft2Req(RecordFilingItemTaskDraftResponse draftPO);


    RecordFilingItemTaskPO req2p(RecordFilingItemTaskRequest request);

    List<RecordFilingItemTaskPO> reqList2pList(List<RecordFilingItemTaskRequest> requestList);

    RecordFilingItemTaskPO req2p(RecordFilingTaskAuditRequest request);

    RecordFilingItemTaskPO req2p(BatchRecordFilingItemTaskRequest request);

    @Mapping(target = "auditFlowStatusList", source = "auditFlowStatus", qualifiedByName = "auditFlowStatusTransfer")
    RecordFilingItemTaskResponse p2Rsp(RecordFilingItemTaskPO po);

    List<RecordFilingItemTaskResponse> listP2Rsp(List<RecordFilingItemTaskPO> list);

    List<RecordFilingItemTaskPageResponse> listP2PageRsp(List<RecordFilingItemTaskPO> list);

    List<TaxRateResponse> listP2RspTax(List<TaxRateDataPO> list);

    @Mapping(target = "auditFlowStatusList", source = "auditFlowStatus", qualifiedByName = "auditFlowStatusTransfer")
    RecordFilingItemTaskPageResponse P2PageRsp(RecordFilingItemTaskPO po);

    default RecordFilingItemTaskPO auditReq2p(RecordFilingTaskAuditRequest request) {
        RecordFilingItemTaskPO recordFilingItemTask = new RecordFilingItemTaskPO();
        recordFilingItemTask.setId(request.getTaskId());
        if (AuditBusinessKeyEnum.FINANCIAL.getCode().equals(request.getBusinessKey())) {
            //财务审核更新字段
            recordFilingItemTask.setTaxCode(request.getTaxCode());
            recordFilingItemTask.setTaxRate(request.getTaxRate());
            recordFilingItemTask.setTaxRateType(request.getTaxRateType());
            recordFilingItemTask.setInputVat(request.getInputVat());
            recordFilingItemTask.setOutputVat(request.getOutputVat());

        }
        if (AuditBusinessKeyEnum.MERCHANDISE_DIRECTOR.getCode().equals(request.getBusinessKey())
                || AuditBusinessKeyEnum.PRODUCT_INSIGHT_DIRECTOR.getCode().equals(request.getBusinessKey())) {
            recordFilingItemTask.setItemCategoryCode(request.getItemCategoryCode());
            recordFilingItemTask.setItemCategoryName(request.getItemCategoryName());
        }
        if (AuditBusinessKeyEnum.PRODUCT_SUPPORT.getCode().equals(request.getBusinessKey())) {
            //产品支持审核更新字段
            recordFilingItemTask.setTaxRateType(request.getTaxRateType());
            recordFilingItemTask.setTaxCode(request.getTaxCode());
            recordFilingItemTask.setTaxRate(request.getTaxRate());
            recordFilingItemTask.setPurchasePrice(request.getPurchasePrice());
            recordFilingItemTask.setPurchaseUnitPrice(request.getPurchaseUnitPrice());
            recordFilingItemTask.setItemNumber(request.getItemNumber());
            recordFilingItemTask.setZtItemType(request.getZtItemType());
            recordFilingItemTask.setZtItemTypeCode(request.getZtItemTypeCode());
            recordFilingItemTask.setJwItemType(request.getJwItemType());
            recordFilingItemTask.setJwItemTypeCode(request.getJwItemTypeCode());
            recordFilingItemTask.setMaterialShortName(request.getMaterialShortName());
            //0607新增字段
            recordFilingItemTask.setItemCategoryCode(request.getItemCategoryCode());
            recordFilingItemTask.setItemCategoryName(request.getItemCategoryName());
            recordFilingItemTask.setTasteName(request.getTasteName());
            recordFilingItemTask.setIsSpecialPrice(request.getIsSpecialPrice());
            recordFilingItemTask.setInputVat(request.getInputVat());
            recordFilingItemTask.setOutputVat(request.getOutputVat());
            recordFilingItemTask.setGoodsTypeId(request.getGoodsTypeId());
            recordFilingItemTask.setGoodsType(request.getGoodsType());
            recordFilingItemTask.setItemType(request.getItemType());
            recordFilingItemTask.setReplaceItemCodeList(request.getReplaceItemCodeList());
            recordFilingItemTask.setReplaceItemNameList(request.getReplaceItemNameList());
            recordFilingItemTask.setNewDisplayCategoryCode(request.getNewDisplayCategoryCode());
            recordFilingItemTask.setNewDisplayCategory(request.getNewDisplayCategory());
            recordFilingItemTask.setPickType(request.getPickType());
            recordFilingItemTask.setItemDepartment(request.getItemDepartment());
            recordFilingItemTask.setProductManagerCode(request.getProductManagerCode());
            recordFilingItemTask.setProductManager(request.getProductManager());
            recordFilingItemTask.setNewComingReason(request.getNewComingReason());
            recordFilingItemTask.setIsThirdProduct(request.getIsThirdProduct());
            recordFilingItemTask.setIsReplaceNewComing(request.getIsReplaceNewComing());
            recordFilingItemTask.setIsExclusiveOperate(request.getIsExclusiveOperate());
            recordFilingItemTask.setIsOnlyImport(request.getIsOnlyImport());
            recordFilingItemTask.setShopFirstDestockQty(request.getShopFirstDestockQty());
            recordFilingItemTask.setIsBigSingleProduct(request.getIsBigSingleProduct());
            recordFilingItemTask.setUnitGramWeight(request.getUnitGramWeight());
            recordFilingItemTask.setIsWeighMark(request.getIsWeighMark());
            recordFilingItemTask.setBarCodeList(request.getBarCodeList());

        }
        return recordFilingItemTask;
    }


}
