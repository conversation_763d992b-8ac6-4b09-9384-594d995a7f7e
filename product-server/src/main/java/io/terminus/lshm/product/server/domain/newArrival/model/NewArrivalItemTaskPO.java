package io.terminus.lshm.product.server.domain.newArrival.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.lshm.common.mybatisplus.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("new_arrival_item_task")
public class NewArrivalItemTaskPO extends BaseModel<Long> {

    /**
     * 商品id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 商品名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     * 商品编码
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 商品类别id
     */
    @TableField("item_type_id")
    private String itemTypeId;

    /**
     * 商品类别名称
     */
    @TableField("item_type_name")
    private String itemTypeName;

    /**
     * 是否为散称
     */
    @TableField("is_scattered_weighing")
    private Integer isScatteredWeighing;

    /**
     * 是否为第三方产品
     */
    @TableField("is_third_party_product")
    private Integer isThirdPartyProduct;


    /**
     * 厂家合作情况说明
     */
    @TableField("cooperation_detail")
    private String cooperationDetail;

    /**
     * 供应商编码
     */
    @TableField("supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @TableField("supplier_name")
    private String supplierName;

    /**
     * 保质期/天
     */
    @TableField("shelf_life_days")
    private Integer shelfLifeDays;

    /**
     * 外条码
     */
    @TableField("bar_code_list")
    private String barCodeList;

    /**
     * 内条码
     */
    @TableField("inner_barcode_list")
    private String innerBarcodeList;

    /**
     * 建议上新仓库id集合
     */
    @TableField("suggestion_warehouse_id_list")
    private String suggestionWarehouseIdList;

    /**
     * 建议上新仓库名称集合
     */
    @TableField("suggestion_warehouse_name_list")
    private String suggestionWarehouseNameList;

    /**
     * 很忙建议上新区域id集合
     */
    @TableField("area_id_list")
    private String areaIdList;

    /**
     * 很忙建议上新区域名称集合
     */
    @TableField("area_name_list")
    private String areaNameList;

    /**
     * 建议上新区域id集合
     */
    @ApiModelProperty(name = "一鸣建议上新区域id集合")
    @TableField("ym_area_id_list")
    private String ymAreaIdList;

    /**
     * 建议上新区域名称集合
     */
    @ApiModelProperty(name = "一鸣建议上新区域名称集合")
    @TableField("ym_area_name_list")
    private String ymAreaNameList;

    /**
     * 门店类型集合
     */
    @TableField("store_type_list")
    private String storeTypeList;

    /**
     * 门店店型分类集合
     */
    @TableField("store_shop_type_list")
    private String storeShopTypeList;

    /**
     * 门店类型集合名称
     */
    @TableField("store_type_list_name")
    private String storeTypeListName;

    /**
     * 门店店型分类集合名称
     */
    @TableField("store_shop_type_list_name")
    private String storeShopTypeListName;

    /**
     * 计划首批到仓上新时间
     */
    @TableField("first_coming_time")
    private Date firstComingTime;

    /**
     * 门店首批去货数量/件（四位小数）
     */
    @TableField("first_arrived_number")
    private BigDecimal firstArrivedNumber;

    /**
     * 规格单位id
     */
    @TableField("specification_unit_id")
    private Long specificationUnitId;

    /**
     * 规格单位
     */
    @TableField("specification_unit")
    private String specificationUnit;

    /**
     * 规格数量
     */
    @TableField("specification_qty")
    private String specificationQty;

    /**
     * 上新商品类型
     */
    @TableField("coming_item_type")
    private String comingItemType;

    /**
     * 替换下架商品编码集合
     */
    @TableField("replace_item_code_list")
    private String replaceItemCodeList;

    /**
     * 替换下架商品名称集合
     */
    @TableField("replace_item_name_list")
    private String replaceItemNameList;

    /**
     * 是否纳入双周MD
     */
    @TableField("is_md")
    private Integer isMd;

    /**
     * 门店毛利率
     */
    @TableField("shop_gross_margin")
    private BigDecimal shopGrossMargin;

    /**
     * 预计新品月度销售额
     */
    @TableField("estimate_sales")
    private BigDecimal estimateSales;

    /**
     * 产能预估 件/月
     */
    @TableField("capacity_estimation")
    private BigDecimal capacityEstimation;

    /**
     * 销售预估量件/月
     */
    @TableField("sales_forecast")
    private BigDecimal salesForecast;

    /**
     * 特批凭证地址
     */
    @TableField("special_voucher")
    private String specialVoucher;

    /**
     * 是否有产品合规书
     */
    @TableField("is_compliance_document")
    private Integer isComplianceDocument;

    /**
     * 产品卖点
     */
    @TableField("product_selling_point")
    private String productSellingPoint;

    /**
     * 是否特价
     */
    @TableField("is_special_price")
    private Integer isSpecialPrice;

    /**
     * 品牌id
     */
    @TableField("brand_id")
    private Integer brandId;

    /**
     * 品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 流程-产品卖点
     */
    @TableField("flow_product_selling_point")
    private String flowProductSellingPoint;

    /**
     * 流程-卖点说明
     */
    @TableField("flow_selling_point_explain")
    private String flowSellingPointExplain;

    /**
     * 流程-检测证明地址
     */
    @TableField("flow_inspection_certificate")
    private String flowInspectionCertificate;

    /**
     * 流程-卖点卡
     */
    @TableField("flow_selling_card")
    private String flowSellingCard;

    /**
     * 流程-确认首批到仓上新时间
     */
    @TableField("flow_first_coming_time")
    private Date flowFirstComingTime;

    /**
     * 流程-包装锋利(割手)
     */
    @TableField("is_sharp")
    private Integer isSharp;

    /**
     * 流程-条码褶皱
     */
    @TableField("is_code_wrinkle")
    private Integer isCodeWrinkle;

    /**
     * 流程-日期隐蔽
     */
    @TableField("is_date_conceal")
    private Integer isDateConceal;

    /**
     * 流程-品控标签
     */
    @TableField("quality_lable")
    private String qualityLable;

    /**
     * 流程-产品合规书地址
     */
    @TableField("compliance_document")
    private String complianceDocument;

    /**
     * 流程-质检结论
     */
    @TableField("quality_testing_result")
    private String qualityTestingResult;

    /**
     * 流程-供应商盖章风险承诺函地址
     */
    @TableField("commitment_letter")
    private String commitmentLetter;

    /**
     * 流程-高风险原因
     */
    @TableField("high_risk_reason")
    private String highRiskReason;

    /**
     * 流程-相关附件
     */
    @TableField("related_accessory")
    private String relatedAccessory;

    /**
     * 审批状态
     */
    @TableField("audit_status")
    private String auditStatus;


    /**
     * 审计流程节点
     */
    @TableField("audit_flow_node")
    private String auditFlowNode;

    /**
     * 审批流程状态
     */
    @TableField("audit_flow_status")
    private String auditFlowStatus;

    /**
     * 审批流程状态名称
     */
    @TableField("audit_flow_status_name")
    private String auditFlowStatusName;

    /**
     * 白底图
     */
    @TableField("white_background_img")
    private String whiteBackgroundImg;

    /**
     * 场景图
     */
    @TableField("scene_img")
    private String sceneImg;

    /**
     * 商品成列图
     */
    @TableField("column_img")
    private String columnImg;

    /**
     * 成列说明
     */
    @TableField("column_explain")
    private String columnExplain;

    /**
     * 陈列单个产品图
     */
    @TableField("product_img")
    private String productImg;

    /**
     * 产品支持品牌所需图
     */
    @TableField("brand_img")
    private String brandImg;

    /**
     * 产品支持文字说明
     */
    @TableField("brand_explain")
    private String brandExplain;

    /**
     * 产品支持备注
     */
    @TableField("brand_remark")
    private String brandRemark;

    /**
     * 品牌总图单个产品图
     */
    @TableField("brand_product_img")
    private String brandProductImg;

    /**
     * 品牌总图
     */
    @TableField("brand_total_img")
    private String brandTotalImg;

    /**
     * 申请状态
     */
    @TableField("apply_status")
    private String applyStatus;

    /**
     * 创建人姓名
     */
    @TableField("created_name")
    private String createdName;

    /**
     * 审批完成时间
     */
    @TableField("audit_completion_time")
    private Date auditCompletionTime;


    @TableField(value="会员价",exist = false)
    private BigDecimal memberPrice;

    @TableField(value="零售价",exist = false)
    private BigDecimal retailPrice;


    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 售卖条件 SINGLE:单个/单件,COMBINE:连包/组合,FCL:整箱/件,SCATTER :散称
     */
    @ApiModelProperty(name = "售卖条件 SINGLE:单个/单件,COMBINE:连包/组合,FCL:整箱/件,SCATTER :散称")
    @TableField("sell_condition")
    private String sellCondition;

    /**
     * 产品卖点：原料产地，产能介绍，产品0添加，优质材料，等级检测，其他
     */
    @TableField("product_sell_point_list")
    private String productSellPointList;
    /**
     * 卖点说明
     */
    @TableField("sell_point_remark")
    private String sellPointRemark;
    /**
     * 检测证明
     */
    @TableField("test_certificate_file")
    private String testCertificateFile;
    /**
     * 迷你店是否去货 1:是,0:否
     */
    @TableField("is_mini_shop_purchase")
    private Integer isMiniShopPurchase;
    /**
     * 外箱图片
     */
    @TableField("out_box_image")
    private String  outBoxImage;

    /**
     * 门店首批去货数量单位
     */
    @TableField("first_arrived_number_unit")
    private String firstArrivedNumberUnit;
}
