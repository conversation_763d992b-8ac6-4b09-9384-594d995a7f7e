package io.terminus.lshm.product.server.domain.biweekly.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.server.domain.biweekly.model.MdAuditRecordPO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 双周MD审核记录表 Mapper 接口
 * <AUTHOR>
 */
@Mapper
public interface MdAuditRecordMapper extends BaseMapper<MdAuditRecordPO> {


    /**
     * 点击审批或者查看时，获取审批记录信息
     * */
    List<MdAuditRecordTO> getApprovalRecord(@Param("ew") MdSpecialApplyByRequest request);


    /**
     * 新建时将将审批信息存入审批记录表
     * */
    Boolean addMdSpecialApply(@Param("ew") MdAuditRecordTO request);



}