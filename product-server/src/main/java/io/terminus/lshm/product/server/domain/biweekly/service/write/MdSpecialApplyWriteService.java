package io.terminus.lshm.product.server.domain.biweekly.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.server.domain.biweekly.model.MdSpecialApplyPO;
import io.terminus.trantorframework.Response;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description 双周MD写服务接口
 * <AUTHOR>
 * @date 2025/6/20 10:15
 */
public interface MdSpecialApplyWriteService extends IService<MdSpecialApplyPO> {



    /**
     * 新增MD特殊申请（提交审批）
     * @param request 查询请求
     * @return 布尔
     * */
    Response<Boolean> addMdSpecialApply(@RequestBody FlowApplyBizRequest<MdSpecialApplyByRequest> request);


    /**
     * 再次提交对数据进行修改（只能更改申请理由，备注，附件信息和门店信息）
     * */
    Response<Boolean> updateMdSpecialApply(@RequestBody FlowApplyBizRequest<MdSpecialApplyByRequest> request);


    void updateWorkFlow(MdSpecialApplyPO mdSpecialApplyPO);
}





