package io.terminus.lshm.product.server.facade.newArrival;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.lshm.product.common.bean.model.NewArrivalItemTaskDTO;
import io.terminus.lshm.product.common.bean.request.newArrival.NewArrivalItemTaskByIdRequest;
import io.terminus.lshm.product.common.bean.request.newArrival.NewArrivalItemTaskExportRequest;
import io.terminus.lshm.product.common.bean.request.newArrival.NewArrivalItemTaskPageRequest;
import io.terminus.lshm.product.common.bean.response.newArrival.ItemBaseDataResponse;
import io.terminus.lshm.product.common.bean.response.newArrival.NewArrivalItemTaskResponse;
import io.terminus.lshm.product.common.enums.BusinessTypeEnum;
import io.terminus.lshm.product.common.recordfiling.response.MyApprovalResponse;
import io.terminus.lshm.product.facade.newArrival.NewArrivalItemTaskReadFacade;
import io.terminus.lshm.product.common.offshelf.dto.NewArrivalItemTaskTO;
import io.terminus.lshm.product.server.converter.ItemBaseDataConverter;
import io.terminus.lshm.product.server.converter.NewArrivalItemTaskConverter;
import io.terminus.lshm.product.server.converter.NewArrivalItemTaskPriceConverter;
import io.terminus.lshm.product.server.manager.BusinessAuditRecordManager;
import io.terminus.lshm.product.server.domain.newArrival.dao.ItemBaseDataDao;
import io.terminus.lshm.product.server.domain.newArrival.model.ItemBaseDataPO;
import io.terminus.lshm.product.server.domain.newArrival.model.NewArrivalItemTaskPO;
import io.terminus.lshm.product.server.domain.newArrival.model.NewArrivalItemTaskPricePO;
import io.terminus.lshm.product.server.domain.newArrival.service.read.NewArrivalItemTaskPriceReadService;
import io.terminus.lshm.product.server.domain.newArrival.service.read.NewArrivalItemTaskReadService;
import io.terminus.lshm.product.server.external.store.StoreCenterService;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@RestController
public class NewArrivalItemTaskReadFacadeImpl implements NewArrivalItemTaskReadFacade {


    private final NewArrivalItemTaskReadService newArrivalItemTaskReadService;


    private final NewArrivalItemTaskPriceReadService newArrivalItemTaskPriceReadService;

    private final NewArrivalItemTaskConverter newArrivalItemTaskConverter;

    private final NewArrivalItemTaskPriceConverter newArrivalItemTaskPriceConverter;

    private final ItemBaseDataConverter itemBaseDataConverter;
    @Resource
    private ItemBaseDataDao itemBaseDataDao;
    @Resource
    private StoreCenterService storeCenterService;

    @Resource
    private BusinessAuditRecordManager businessAuditRecordManager;

    /**
     * 分页查询商品上新任务
     *
     * @param request 查询请求
     * @return 分页响应
     */
    @Override
    public Response<Paging<NewArrivalItemTaskResponse>> pageOffShelfItemTask(NewArrivalItemTaskPageRequest request) {

        Paging<NewArrivalItemTaskResponse>  paging = newArrivalItemTaskReadService.page(request);
        return Response.ok(paging);
    }


    /**
     * 获取详情
     * @param request
     * @return
     */
    @Override
    public Response<NewArrivalItemTaskTO> getTask(NewArrivalItemTaskByIdRequest request) {
        LambdaQueryWrapper<NewArrivalItemTaskPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NewArrivalItemTaskPO::getId, request.getId());
        NewArrivalItemTaskPO newArrivalItemTaskPO = newArrivalItemTaskReadService.getOne(wrapper);
        NewArrivalItemTaskTO newArrivalItemTaskTO = newArrivalItemTaskConverter.p2t(newArrivalItemTaskPO);
        if (Objects.nonNull(newArrivalItemTaskTO)) {
            // 查询审批权限，待办任务
            if (Objects.nonNull(ServerContext.getUserId())) {
                List<String> keyList = businessAuditRecordManager.selectApprovalPending(BusinessTypeEnum.PRODUCT_LAUNCHING.getCode(), newArrivalItemTaskTO.getId().toString(), ServerContext.getUserId().toString());
                newArrivalItemTaskTO.setKeyList(keyList != null ? keyList : Collections.emptyList());
            }
            // 查询规格价格
            LambdaQueryWrapper<NewArrivalItemTaskPricePO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(NewArrivalItemTaskPricePO::getNewComingItemId, request.getId());
            List<NewArrivalItemTaskPricePO> list = newArrivalItemTaskPriceReadService.list(queryWrapper);
            if (!CollectionUtils.isEmpty(list)) {
                newArrivalItemTaskTO.setNewArrivalItemTaskPriceTOList(newArrivalItemTaskPriceConverter.listP2T(list));
            }
//            if (ObjectUtil.isNotEmpty(newArrivalItemTaskTO.getAuditFlowStatus())) {
//                JSONArray objects = JSONUtil.parseArray(newArrivalItemTaskTO.getAuditFlowStatus());
//                newArrivalItemTaskTO.setAuditFlowStatusList(JSONUtil.toList(objects, FlowNodeDTO.class));
//            }

        }

        return Response.ok(newArrivalItemTaskTO);
    }


    @Override
    public Response<List<ItemBaseDataResponse>> getLabelList() {
        LambdaQueryWrapper<ItemBaseDataPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ItemBaseDataPO::getIsDeleted,0).orderByAsc(ItemBaseDataPO::getDataCode);
        List<ItemBaseDataPO> itemBaseDataPOList = itemBaseDataDao.selectList(wrapper);
        return Response.ok(itemBaseDataConverter.listp2Rsp(itemBaseDataPOList));
    }


    /**
     * 导出用
     * @param request
     * @return
     */
    @Override
    public Response<Paging<NewArrivalItemTaskDTO>> queryNewArrivalItemTaskList(NewArrivalItemTaskExportRequest request) {
        return Response.ok(newArrivalItemTaskReadService.queryNewArrivalItemTaskList(request));
    }

    @Override
    public Response<MyApprovalResponse> myApprovals() {
        return Response.ok(newArrivalItemTaskReadService.myApprovals());
    }



}