package io.terminus.lshm.product.server.domain.biweekly.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.terminus.lshm.common.mybatisplus.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 双周MD审核记录表实体
 * <AUTHOR>
 * @date 2025/6/19 9:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("md_audit_record")
public class MdAuditRecordPO extends BaseModel<Long> implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 申请表id
     * */
    @TableField(value = "special_apply_id")
    private Long specialApplyId;

    /**
     * 审批流程节点
     */
    @TableField(value = "special_apply")
    private String specialApply;

    /**
     * 审批状态
     */
    @TableField(value = "audit_status")
    private String auditStatus;

    /**
     * 审批原因
     */
    @TableField(value = "audit_reason")
    private String auditReason;

    /**
     * 审批人
     */
    @TableField(value = "auditor")
    private String auditor;

    /**
     * 审批人姓名
     */
    @TableField(value = "auditor_name")
    private String auditorName;

    /**
     * 审批人工号
     */
    @TableField(value = "auditor_employee_code")
    private String auditorEmployeeCode;

    /**
     * 审批人角色名称
     */
    @TableField(value = "auditor_role_name")
    private String auditorRoleName;

    /**
     * 审批人角色ID
     */
    @TableField(value = "auditor_role_id")
    private String auditorRoleId;

    /**
     * 去审批人
     */
    @TableField(value = "to_auditor")
    private String toAuditor;

    /**
     * 审批时间
     */
    @TableField(value = "audit_at")
    private Date auditAt;

    /**
     * 任务id
     */
    @TableField("work_item_id")
    private String workItemId;

}
