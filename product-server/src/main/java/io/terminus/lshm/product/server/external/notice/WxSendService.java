package io.terminus.lshm.product.server.external.notice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import io.terminus.lshm.product.common.flow.dto.MsgInfo;
import io.terminus.lshm.product.server.config.WxNoticeConfig;
import io.terminus.trantor.notice.api.facade.OutEnterpriseWechatAppSendFacade;
import io.terminus.trantor.notice.dict.EnterpriseWechatAppTemplateCardTypeDict;
import io.terminus.trantor.notice.dict.EnterpriseWechatAppTypeDict;
import io.terminus.trantor.notice.tmodel.wechat.enterprise.EnterpriseWechatAppMsgTO;
import io.terminus.trantor.notice.tmodel.wechat.enterprise.TemplateCard.*;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/31
 * @Version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WxSendService {


    private final OutEnterpriseWechatAppSendFacade wechatAppSendFacade;

    private final WxNoticeConfig wxNoticeConfig;

    public static final String URL = "accountId=%s&businessId=%s&businessType=%s";

    @Value("${audit.flow.frontUrl:}")
    private String flowFrontUrl;
    @Value("${audit.flow.noticeUrl:}")
    private String flowNoticeUrl;

//
//    /**
//     * * //// TODO: 2025/3/31
//     * @param list
//     */
//    public void handlerSendWechatAppMsg(List<OffShelfItemTaskPO> list) {
//        log.info("下架商品流程审批企微消息开始发送:{}",list.size());
//
//        Set<Long> userIds = list.stream().map(OffShelfItemTaskPO::getCreatedBy).collect(Collectors.toSet());
//
//        Map<Long, String> userNameMap = ucUserService.findUserByIds(userIds).stream().collect(Collectors.toMap(UserInfo::getId, UserInfo::getNickname));
//        for (OffShelfItemTaskPO offShelfItemTaskPO : list) {
//            //获取微信账户Id
//            try {
//                String toAuditor = offShelfItemTaskPO.getToAuditor();
//                String[] split = toAuditor.split(",");
//                for (String userId : split) {
//                    String accountId = ucUserService.findUserAccountIdByUserId(Long.valueOf(userId));
//                    log.info("获取到的账户id："+accountId);
//                    if(!StringUtils.hasText(accountId)){
//                        log.error("根据用户id获取用户三方信息接口调用失败:{}",userId);
//                        continue;
//                    }
//                    String url = getUrl(accountId,offShelfItemTaskPO.getId()+"","OFF_SHELF_ITEM_TASK");
//                    EnterpriseWechatAppMsgTO wechatAppMsgTO = new EnterpriseWechatAppMsgTO();
//                    wechatAppMsgTO.setTouser(accountId);
//                    wechatAppMsgTO.setMsgtype(EnterpriseWechatAppTypeDict.TEMPLATE_CARD);
//
//                    TemplateCardTO templateCardTO = new TemplateCardTO();
//                    templateCardTO.setCard_type(EnterpriseWechatAppTemplateCardTypeDict.TEXT_NOTICE);
//                    TemplateCardActionTO card_action = new TemplateCardActionTO();
//                    card_action.setType("1");
//                    card_action.setUrl(url);
//                    templateCardTO.setCard_action(card_action);
//
//                    TemplateSourceTO templateSourceTO = new TemplateSourceTO();
//                    templateSourceTO.setDesc("您有流程待审批，请用电脑打开，手机端暂不支持！");
//                    templateSourceTO.setDesc_color("2");
//                    templateCardTO.setSource(templateSourceTO);
//
//                    TemplateMainTitleTO templateMainTitleTO = new TemplateMainTitleTO();
//                    templateMainTitleTO.setTitle("商品下架流程");
//                    templateCardTO.setMain_title(templateMainTitleTO);
//
//
//                    List<TemplateHorizontalContentListTO> Horizontal_content_list = new ArrayList<>();
//                    TemplateHorizontalContentListTO listTO1 = new TemplateHorizontalContentListTO();
//                    listTO1.setKeyname("商品名称");
//                    listTO1.setValue(offShelfItemTaskPO.getItemName());
//                    Horizontal_content_list.add(listTO1);
//
//                    TemplateHorizontalContentListTO listTO2 = new TemplateHorizontalContentListTO();
//                    listTO2.setKeyname("创建人");
//                    listTO2.setValue(userNameMap.get(offShelfItemTaskPO.getCreatedBy()));
//                    Horizontal_content_list.add(listTO2);
//
//                    TemplateHorizontalContentListTO listTO3 = new TemplateHorizontalContentListTO();
//                    listTO3.setKeyname("创建时间");
//                    listTO3.setValue(DateUtil.formatDateTime(offShelfItemTaskPO.getCreatedAt()));
//                    Horizontal_content_list.add(listTO3);
//                    templateCardTO.setHorizontal_content_list(Horizontal_content_list);
//
//                    List<TemplateJumpListTO> jump_list = new ArrayList<>();
//                    TemplateJumpListTO templateJumpListTO = new TemplateJumpListTO();
//                    templateJumpListTO.setType("1");
//                    templateJumpListTO.setUrl(url);
//                    templateJumpListTO.setTitle("去审核");
//                    jump_list.add(templateJumpListTO);
//
//                    templateCardTO.setJump_list(jump_list);
//
//                    wechatAppMsgTO.setTemplate_card(templateCardTO);
//                    log.info("下架商品流程审批企微消息开始发送开始发送:{}",JSON.toJSONString(wechatAppMsgTO));
//                    String msgId = wechatAppSendFacade.OutEnterpriseWechatAppSend(wechatAppMsgTO).getRes();
//                    log.info("下架商品流程审批企微消息开始发送开始发送,消息id：{}",msgId);
//                }
//
//            } catch (Exception e) {
//                log.error("通知失败！",e);
//            }
//        }
//
//    }


    public String getUrl(String accountId, String id, String type) {
        return wxNoticeConfig.getFrontUrl() + URLUtil.encodeAll(String.format(URL, accountId, id, type));
    }

    public void handlerSendWechatAppMsg2(List<MsgInfo> list) {
        log.info("审批流程企微消息开始发送:{}", list.size());
        for (MsgInfo msgInfo : list) {
            String pathMapUrl = this.pathMapUrl(msgInfo.getUrl());
            EnterpriseWechatAppMsgTO wechatAppMsgTO = new EnterpriseWechatAppMsgTO();
            wechatAppMsgTO.setTouser(String.join("|", msgInfo.getAccountIds()));
            wechatAppMsgTO.setMsgtype(EnterpriseWechatAppTypeDict.TEMPLATE_CARD);

            TemplateCardTO templateCardTO = new TemplateCardTO();
            templateCardTO.setCard_type(EnterpriseWechatAppTemplateCardTypeDict.TEXT_NOTICE);
            TemplateCardActionTO card_action = new TemplateCardActionTO();
            card_action.setType("2");
            card_action.setPagepath(pathMapUrl);
            templateCardTO.setCard_action(card_action);

//                TemplateSourceTO templateSourceTO = new TemplateSourceTO();
//                templateSourceTO.setDesc("您有流程待审批，请用电脑打开，手机端暂不支持！");
//                templateSourceTO.setDesc_color("2");
//                templateCardTO.setSource(templateSourceTO);

            TemplateMainTitleTO templateMainTitleTO = new TemplateMainTitleTO();
            templateMainTitleTO.setTitle(msgInfo.getTitle());
            templateCardTO.setMain_title(templateMainTitleTO);


            List<TemplateHorizontalContentListTO> Horizontal_content_list = new ArrayList<>();
            TemplateHorizontalContentListTO listTO1 = new TemplateHorizontalContentListTO();
            listTO1.setKeyname("商品名称");
            listTO1.setValue(msgInfo.getItemName());
            Horizontal_content_list.add(listTO1);

            TemplateHorizontalContentListTO listTO2 = new TemplateHorizontalContentListTO();
            listTO2.setKeyname("创建人");
            listTO2.setValue(msgInfo.getCreatedBy());
            Horizontal_content_list.add(listTO2);

            TemplateHorizontalContentListTO listTO3 = new TemplateHorizontalContentListTO();
            listTO3.setKeyname("创建时间");
            listTO3.setValue(DateUtil.formatDateTime(msgInfo.getCreatedAt()));
            Horizontal_content_list.add(listTO3);
            templateCardTO.setHorizontal_content_list(Horizontal_content_list);

            List<TemplateJumpListTO> jump_list = new ArrayList<>();
            TemplateJumpListTO templateJumpListTO = new TemplateJumpListTO();
            templateJumpListTO.setType("2");
            templateJumpListTO.setPagepath(pathMapUrl);
            templateJumpListTO.setTitle("去审核");
            jump_list.add(templateJumpListTO);

            templateCardTO.setJump_list(jump_list);

            wechatAppMsgTO.setTemplate_card(templateCardTO);
            log.info("流程审批企微消息开始发送开始发送:{}", JSON.toJSONString(wechatAppMsgTO));
            String msgId = wechatAppSendFacade.OutEnterpriseWechatAppSend(wechatAppMsgTO).getRes();
            log.info("流程审批企微消息开始发送开始发送,消息id：{}", msgId);

        }


    }

    private String pathMapUrl(String url) {
        Map<String, String> map = Maps.newHashMapWithExpectedSize(1);
        map.put("path", URLUtil.encodeAll(flowFrontUrl + url));
        String pathUrl = JSON.toJSONString(map);
        return flowNoticeUrl + URLUtil.encodeAll(pathUrl);
    }


    public void handlerSendWechatAppMsgPC(List<MsgInfo> list) {
        log.info("重新提交流程审批企微消息开始发送:{}", list.size());
        for (MsgInfo msgInfo : list) {
            //获取微信账户Id
            try {
                String url = getUrl(msgInfo.getAccountId(), msgInfo.getBusinessId(), msgInfo.getBusinessType());
                EnterpriseWechatAppMsgTO wechatAppMsgTO = new EnterpriseWechatAppMsgTO();
                wechatAppMsgTO.setTouser(msgInfo.getAccountId());
                wechatAppMsgTO.setMsgtype(EnterpriseWechatAppTypeDict.TEMPLATE_CARD);

                TemplateCardTO templateCardTO = new TemplateCardTO();
                templateCardTO.setCard_type(EnterpriseWechatAppTemplateCardTypeDict.TEXT_NOTICE);
                TemplateCardActionTO card_action = new TemplateCardActionTO();
                card_action.setType("1");
                card_action.setUrl(url);
                templateCardTO.setCard_action(card_action);

                TemplateSourceTO templateSourceTO = new TemplateSourceTO();
                templateSourceTO.setDesc("您有流程被驳回，需重新提交，请用电脑打开，手机端暂不支持！");
                templateSourceTO.setDesc_color("2");
                templateCardTO.setSource(templateSourceTO);

                TemplateMainTitleTO templateMainTitleTO = new TemplateMainTitleTO();
                templateMainTitleTO.setTitle(msgInfo.getTitle());
                templateCardTO.setMain_title(templateMainTitleTO);


                List<TemplateHorizontalContentListTO> Horizontal_content_list = new ArrayList<>();
                TemplateHorizontalContentListTO listTO1 = new TemplateHorizontalContentListTO();
                listTO1.setKeyname("商品名称");
                listTO1.setValue(msgInfo.getItemName());
                Horizontal_content_list.add(listTO1);

                TemplateHorizontalContentListTO listTO2 = new TemplateHorizontalContentListTO();
                listTO2.setKeyname("创建人");
                listTO2.setValue(msgInfo.getCreatedBy());
                Horizontal_content_list.add(listTO2);

                TemplateHorizontalContentListTO listTO3 = new TemplateHorizontalContentListTO();
                listTO3.setKeyname("创建时间");
                listTO3.setValue(DateUtil.formatDateTime(msgInfo.getCreatedAt()));
                Horizontal_content_list.add(listTO3);
                templateCardTO.setHorizontal_content_list(Horizontal_content_list);

                List<TemplateJumpListTO> jump_list = new ArrayList<>();
                TemplateJumpListTO templateJumpListTO = new TemplateJumpListTO();
                templateJumpListTO.setType("1");
                templateJumpListTO.setUrl(url);
                templateJumpListTO.setTitle("去审核");
                jump_list.add(templateJumpListTO);

                templateCardTO.setJump_list(jump_list);

                wechatAppMsgTO.setTemplate_card(templateCardTO);
                log.info("重新提交流程审批企微消息开始发送:{}", JSON.toJSONString(wechatAppMsgTO));
                String msgId = wechatAppSendFacade.OutEnterpriseWechatAppSend(wechatAppMsgTO).getRes();
                log.info("重新提交流程审批企微消息开始发送,消息id：{}", msgId);
            } catch (Exception e) {
                log.error("通知失败！", e);
            }
        }

    }

    /**
     * 发送调价审批待办通知
     *
     * @param list
     */
    public void handlerSendWechatAppAuditPendingMsgAdjustPricePC(List<MsgInfo> list) {
        log.info("发送调价审批催办通知企微消息开始发送:{}", JSON.toJSONString(list));
        for (MsgInfo msgInfo : list) {
            //获取微信账户Id
            try {
                String url = getUrl(msgInfo.getAccountId(), msgInfo.getBusinessId(), msgInfo.getBusinessType());
                EnterpriseWechatAppMsgTO wechatAppMsgTO = new EnterpriseWechatAppMsgTO();
                wechatAppMsgTO.setTouser(String.join("|", msgInfo.getAccountIds()));
                wechatAppMsgTO.setMsgtype(EnterpriseWechatAppTypeDict.TEMPLATE_CARD);

                TemplateCardTO templateCardTO = new TemplateCardTO();
                templateCardTO.setCard_type(EnterpriseWechatAppTemplateCardTypeDict.TEXT_NOTICE);
                TemplateCardActionTO card_action = new TemplateCardActionTO();
                card_action.setType("2");
                card_action.setUrl(url);
                templateCardTO.setCard_action(card_action);

               // TemplateSourceTO templateSourceTO = new TemplateSourceTO();
                //templateSourceTO.setDesc();
               // templateSourceTO.setDesc_color("2");
               // templateCardTO.setSource(templateSourceTO);

                TemplateMainTitleTO templateMainTitleTO = new TemplateMainTitleTO();
                templateMainTitleTO.setTitle(msgInfo.getTitle());
                templateCardTO.setMain_title(templateMainTitleTO);

                List<TemplateHorizontalContentListTO> Horizontal_content_list = new ArrayList<>();

                TemplateHorizontalContentListTO listTO2 = new TemplateHorizontalContentListTO();
                listTO2.setKeyname("创建人");
                listTO2.setValue(msgInfo.getCreatedBy());
                Horizontal_content_list.add(listTO2);

                TemplateHorizontalContentListTO listTO3 = new TemplateHorizontalContentListTO();
                listTO3.setKeyname("活动生效时间");
                listTO3.setValue(msgInfo.getActivityEffectTime());
                Horizontal_content_list.add(listTO3);
                templateCardTO.setHorizontal_content_list(Horizontal_content_list);

                List<TemplateJumpListTO> jump_list = new ArrayList<>();
                TemplateJumpListTO templateJumpListTO = new TemplateJumpListTO();
                templateJumpListTO.setType("1");
                templateJumpListTO.setUrl(url);
                templateJumpListTO.setTitle("去审核");
                jump_list.add(templateJumpListTO);

                templateCardTO.setJump_list(jump_list);

                wechatAppMsgTO.setTemplate_card(templateCardTO);
                log.info("调价审批催办通知开始发送:{}", JSON.toJSONString(wechatAppMsgTO));
                String msgId = wechatAppSendFacade.OutEnterpriseWechatAppSend(wechatAppMsgTO).getRes();
                log.info("调价审批催办企微消息开始发送,消息id：{}", msgId);
            } catch (Exception e) {
                log.error("发送调价审批催办通知失败！", e);
                throw new BusinessException("发送调价审批催办通知失败");
            }
        }

    }

    /**
     * 发送月度MD特殊申请审批待办通知
     *
     * @param list
     */
    public void handlerSendWechatAppAuditPendingMsgBiweeklyMdPC(List<MsgInfo> list) {
        log.info("发送月度MD特殊申请审批催办通知企微消息开始发送:{}", JSON.toJSONString(list));
        for (MsgInfo msgInfo : list) {
            //获取微信账户Id
            try {
                String url = getUrl(msgInfo.getAccountId(), msgInfo.getBusinessId(), msgInfo.getBusinessType());
                EnterpriseWechatAppMsgTO wechatAppMsgTO = new EnterpriseWechatAppMsgTO();
                wechatAppMsgTO.setTouser(String.join("|", msgInfo.getAccountIds()));
                wechatAppMsgTO.setMsgtype(EnterpriseWechatAppTypeDict.TEMPLATE_CARD);

                TemplateCardTO templateCardTO = new TemplateCardTO();
                templateCardTO.setCard_type(EnterpriseWechatAppTemplateCardTypeDict.TEXT_NOTICE);
                TemplateCardActionTO card_action = new TemplateCardActionTO();
                card_action.setType("2");
                card_action.setUrl(url);
                templateCardTO.setCard_action(card_action);

                TemplateMainTitleTO templateMainTitleTO = new TemplateMainTitleTO();
                templateMainTitleTO.setTitle(msgInfo.getTitle());
                templateCardTO.setMain_title(templateMainTitleTO);

                List<TemplateHorizontalContentListTO> Horizontal_content_list = new ArrayList<>();

                TemplateHorizontalContentListTO listTO2 = new TemplateHorizontalContentListTO();
                listTO2.setKeyname("创建人");
                listTO2.setValue(msgInfo.getCreatedBy());
                Horizontal_content_list.add(listTO2);

                TemplateHorizontalContentListTO listTO3 = new TemplateHorizontalContentListTO();
                listTO3.setKeyname("月度MD特殊申请审批");
                listTO3.setValue(msgInfo.getActivityEffectTime());
                Horizontal_content_list.add(listTO3);
                templateCardTO.setHorizontal_content_list(Horizontal_content_list);

                List<TemplateJumpListTO> jump_list = new ArrayList<>();
                TemplateJumpListTO templateJumpListTO = new TemplateJumpListTO();
                templateJumpListTO.setType("1");
                templateJumpListTO.setUrl(url);
                templateJumpListTO.setTitle("去审核");
                jump_list.add(templateJumpListTO);

                templateCardTO.setJump_list(jump_list);

                wechatAppMsgTO.setTemplate_card(templateCardTO);
                log.info("月度MD特殊申请审批催办通知开始发送:{}", JSON.toJSONString(wechatAppMsgTO));
                String msgId = wechatAppSendFacade.OutEnterpriseWechatAppSend(wechatAppMsgTO).getRes();
                log.info("月度MD特殊申请审批催办企微消息发送返回,消息id：{}", msgId);
            } catch (Exception e) {
                log.error("发送月度MD特殊申请审批催办通知失败！", e);
                throw new BusinessException("发送月度MD特殊申请审批催办通知失败");
            }
        }

    }

}
