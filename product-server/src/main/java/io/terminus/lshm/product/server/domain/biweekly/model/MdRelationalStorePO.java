package io.terminus.lshm.product.server.domain.biweekly.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.terminus.lshm.common.mybatisplus.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 双周MD特殊申请关联门店表实体
 * <AUTHOR>
 * @date 2025/6/19 9:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("md_relational_store")
public class MdRelationalStorePO extends BaseModel<Long> implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 门店ID
     */
    @TableField(value = "store_id")
    private Long storeId;



    /**
     * 门店编码
     */
    @TableField(value = "store_code")
    private String storeCode;

    /**
     * 门店名称
     */
    @TableField(value = "store_name")
    private String storeName;


    /**
     * 申请表ID（一对多）
     */
    @TableField(value = "special_apply_id")
    private Long specialApplyId;

}
