package io.terminus.lshm.product.server.converter;

import cn.hutool.core.date.DateUtil;
import io.terminus.lshm.product.common.bean.response.adjust.AdjustItemInfoResponse;
import io.terminus.lshm.product.facade.price.dto.AdjustPriceExcelDTO;
import io.terminus.lshm.product.facade.price.to.AdjustItemTO;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustItemPO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AdjustItemConverter extends BasicConvert<AdjustItemTO, AdjustItemPO> {

    List<AdjustItemInfoResponse> listP2Rsp(List<AdjustItemPO> list);


    default AdjustPriceExcelDTO p2d(AdjustPriceExcelDTO adjustPriceExcelDTO, AdjustItemPO adjustItem) {
        adjustPriceExcelDTO.setItemName(adjustItem.getItemName());
        adjustPriceExcelDTO.setItemCode(adjustItem.getItemCode());
        adjustPriceExcelDTO.setItemArchiveDeliveryPrice(adjustItem.getItemArchiveDeliveryPrice());
        adjustPriceExcelDTO.setItemDeliveryPriceNew(adjustItem.getItemDeliveryPriceNew());
        adjustPriceExcelDTO.setItemArchiveRetailPrice(adjustItem.getItemArchiveRetailPrice());
        adjustPriceExcelDTO.setItemRetailPriceNew(adjustItem.getItemRetailPriceNew());
        adjustPriceExcelDTO.setItemArchiveMemberPrice(adjustItem.getItemArchiveMemberPrice());
        adjustPriceExcelDTO.setItemMemberPriceNew(adjustItem.getItemMemberPriceNew());
        adjustPriceExcelDTO.setItemArchiveDeliveryPriceNew(adjustItem.getItemArchiveDeliveryPriceNew());
        adjustPriceExcelDTO.setItemArchiveRetailPriceNew(adjustItem.getItemArchiveRetailPriceNew());
        adjustPriceExcelDTO.setItemArchiveMemberPriceNew(adjustItem.getItemArchiveMemberPriceNew());
        adjustPriceExcelDTO.setDiscountRate(adjustItem.getDiscountRate());
        adjustPriceExcelDTO.setItemUnitName(adjustItem.getItemUnitName());
        adjustPriceExcelDTO.setProductionDateStart(DateUtil.formatDate(adjustItem.getProductionDateStart()));
        adjustPriceExcelDTO.setSaleItemUnitName(adjustItem.getSaleItemUnitName());
        adjustPriceExcelDTO.setInventoryQuantity(adjustItem.getInventoryQuantity());
        return adjustPriceExcelDTO;
    }
}
