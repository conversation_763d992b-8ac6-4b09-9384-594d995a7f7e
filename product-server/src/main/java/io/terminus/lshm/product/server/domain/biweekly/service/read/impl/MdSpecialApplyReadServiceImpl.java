package io.terminus.lshm.product.server.domain.biweekly.service.read.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.terminus.lshm.flow.response.FlowListByInstanceResponse;
import io.terminus.lshm.product.common.enums.AuditStatusEnum;
import io.terminus.lshm.product.common.enums.biweeklyMd.BiweeklyMdFlowNodeEnum;
import io.terminus.lshm.product.common.offshelf.dto.EmployeeUsersTO;
import io.terminus.lshm.product.facade.flow.FlowBpmReadFacade;
import io.terminus.lshm.product.server.external.user.EmployeeService;
import io.terminus.lshm.store.common.bean.request.store.read.InnerPageAllRequest;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import io.terminus.lshm.product.common.biweekly.dto.MdAttachmentInfoTO;
import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO;
import io.terminus.lshm.product.common.biweekly.request.MdRelationalStoreRequest;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyPageRequest;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdAttachmentInfoMapper;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdAuditRecordMapper;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdRelationalStoreMapper;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdSpecialApplyMapper;
import io.terminus.lshm.product.server.domain.biweekly.model.MdSpecialApplyPO;
import io.terminus.lshm.product.server.domain.biweekly.service.read.MdSpecialApplyReadService;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.lshm.server.common.ServerUserInfo;
import io.terminus.lshm.store.common.bean.request.store.read.InnerFindStoreByIdsRequest;
import io.terminus.lshm.store.common.model.store.InnerStoreTO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import io.terminus.lshm.store.facade.store.read.InnerStoreReadFacade;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/6/19
 */
@Slf4j
@Service
public class MdSpecialApplyReadServiceImpl extends ServiceImpl<MdSpecialApplyMapper, MdSpecialApplyPO> implements MdSpecialApplyReadService {

    @Resource
    private MdSpecialApplyMapper mdSpecialApplyMapper;

    @Resource
    private MdRelationalStoreMapper mdRelationalStoreMapper;

    @Resource
    private MdAttachmentInfoMapper mdAttachmentInfoMapper;

    @Resource
    private MdAuditRecordMapper mdAuditRecordMapper;


    @Autowired
    private EmployeeService employeeService;

    @Resource
    private InnerStoreReadFacade innerStoreReadFacade;
    @Resource
    private FlowBpmReadFacade flowBpmReadFacade;



    @Override
    public MdSpecialApplyPO getByWorkflowInstanceId(String workflowInstanceId) {
        LambdaQueryWrapper<MdSpecialApplyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdSpecialApplyPO::getWorkflowInstanceId, workflowInstanceId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<MdSpecialApplyPO> getByWorkflowInstanceIds(List<String> workflowInstanceIds) {
        LambdaQueryWrapper<MdSpecialApplyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MdSpecialApplyPO::getWorkflowInstanceId, workflowInstanceIds);
        return this.baseMapper.selectList(queryWrapper);
    }



    @Override
    public Paging<MdSpecialApplyTO> pageMdSpecialApply(MdSpecialApplyPageRequest request) {
        // 处理auditFlowStatusList参数，将operationMaintenanceExecute转换为oatrolShopCustomerServiceExecute
        // 因为这两个是同级的，都是同样的数据，而数据库目前存的是oatrolShopCustomerServiceExecute
        if (request.getAuditFlowStatusList() != null && !request.getAuditFlowStatusList().isEmpty()) {
            List<String> convertedStatusList = new ArrayList<>();
            for (String status : request.getAuditFlowStatusList()) {
                if (BiweeklyMdFlowNodeEnum.OPERATION_MAINTENANCE_EXECUTE_APPROVAL.getCode().equals(status)) {
                    convertedStatusList.add(BiweeklyMdFlowNodeEnum.OATROL_SHOP_CUSTOMER_SERVICE_EXECUTE_APPROVAL.getCode());
                } else {
                    convertedStatusList.add(status);
                }
            }
            // 如果列表中同时包含oatrolShopCustomerServiceExecute和operationMaintenanceExecute，去重
            List<String> uniqueStatusList = convertedStatusList.stream().distinct().collect(Collectors.toList());
            request.setAuditFlowStatusList(uniqueStatusList);
        }

        // 如果没有门店条件，直接查询主表
        if (request.getStoreId() == null && (request.getStoreName() == null || request.getStoreName().isEmpty())) {
            // 获取全量数据用于排序
            Page<MdSpecialApplyTO> fullPage = mdSpecialApplyMapper.pageMdSpecialApply(
                    new Page<>(1, Integer.MAX_VALUE), request);
            // 自定义排序
            List<MdSpecialApplyTO> sortedList = sortedByStatusAndTime(fullPage.getRecords());
            // 手动分页
            return manualPaging(sortedList, request);

        }
        // 查询门店关联信息
        MdRelationalStoreRequest rs = new MdRelationalStoreRequest();
        rs.setStoreId(request.getStoreId());
        rs.setStoreName(request.getStoreName());

        List<MdRelationalStoreTO> list = mdRelationalStoreMapper.selectRelationalStoreList(rs);

        // 如果没有关联记录，直接返回空分页
        if (list.isEmpty()) {
            return new Paging<>(0, Collections.emptyList());
        }

        // 提取申请ID集合
        Set<Long> applyIds = list.stream()
                .map(MdRelationalStoreTO::getSpecialApplyId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (applyIds.isEmpty()) {
            return new Paging<>(0, Collections.emptyList());
        }

        // 设置查询条件
        request.setSpecialApplyId(applyIds);

        // 获取全量数据用于排序
        Page<MdSpecialApplyTO> fullPage = mdSpecialApplyMapper.pageMdSpecialApply(
                new Page<>(1, Integer.MAX_VALUE), request);
        // 自定义排序
        List<MdSpecialApplyTO> sortedList = sortedByStatusAndTime(fullPage.getRecords());
        // 手动分页
        return manualPaging(sortedList, request);
    }


    /**
     * 手动分页方法
     */
    private Paging<MdSpecialApplyTO> manualPaging(List<MdSpecialApplyTO> sortedList, MdSpecialApplyPageRequest request) {
        int total = sortedList.size();
        int pageSize = request.getPageSize();
        int pageNo = request.getPageNo();

        int fromIndex = (pageNo - 1) * pageSize;
        if (fromIndex >= total) {
            return new Paging<>(total, Collections.emptyList());
        }

        int toIndex = Math.min(fromIndex + pageSize, total);
        return new Paging<>(total, sortedList.subList(fromIndex, toIndex));
    }

    /**
     * 按照状态优先级 + 时间降序排序
     */
    private List<MdSpecialApplyTO> sortedByStatusAndTime(List<MdSpecialApplyTO> records) {
        return records.stream()
                .sorted((a, b) -> {
                    // 第一排序：状态优先级
                    int statusPriorityA = getStatusPriority(a.getAuditStatus());
                    int statusPriorityB = getStatusPriority(b.getAuditStatus());

                    if (statusPriorityA != statusPriorityB) {
                        return Integer.compare(statusPriorityA, statusPriorityB);
                    }

                    // 第二排序：对于 UNDER_REVIEW 和 REJECTED 内部按 created_at 降序
                    boolean isAUnderReviewOrRejected = AuditStatusEnum.UNDER_REVIEW.getCode().equals(a.getAuditStatus()) ||
                            AuditStatusEnum.REJECTED.getCode().equals(a.getAuditStatus());
                    boolean isBUnderReviewOrRejected = AuditStatusEnum.UNDER_REVIEW.getCode().equals(b.getAuditStatus()) ||
                            AuditStatusEnum.REJECTED.getCode().equals(b.getAuditStatus());

                    if (isAUnderReviewOrRejected && isBUnderReviewOrRejected) {
                        return b.getCreatedAt().compareTo(a.getCreatedAt());
                    }

                    // 第三排序：其他状态统一按 created_at 降序
                    return b.getCreatedAt().compareTo(a.getCreatedAt());
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取状态优先级
     */
    private int getStatusPriority(String status) {
        if (status == null) return 5;

        if (status.equals(AuditStatusEnum.UNDER_REVIEW.getCode()) ||
                status.equals(AuditStatusEnum.REJECTED.getCode())) {
            return 1;
        } else if (status.equals(AuditStatusEnum.COMPLETED.getCode())) {
            return 3;
        } else if (status.equals(AuditStatusEnum.CANCELLED.getCode())) {
            return 4;
        } else {
            return 5;
        }
    }



    @Override
    public Response<Paging<InnerStoreTO>> getStoreList(InnerPageAllRequest request) {
        //调用门店中心接口
        Response<Paging<InnerStoreTO>> page=innerStoreReadFacade.pageAll(request);
        log.info("listSalePrice_request：{}",page);
        return page;
    }

    @Override
    public Response<MdSpecialApplyTO> getMdSpecialApply(MdSpecialApplyByRequest request) {
        //这里先需要查询出基本信息
        MdSpecialApplyTO sa = mdSpecialApplyMapper.getMdSpecialApply( request);
        if (sa == null) {
            return Response.failure("没有申请信息");
        }
        //在查询出关联的附件信息
        List<MdAttachmentInfoTO> info = mdAttachmentInfoMapper.selectMdAttachmentInfoList(sa.getId());
        sa.setAttachments(info);
        //查看还需要展示审批信息（但是查看里面不可做操作，提交审批按钮和获取门店按钮可以置灰）
        return Response.ok(sa);
    }

    @Override
    public Response<List<MdRelationalStoreTO>> getRelationalStoreInfo(MdSpecialApplyPageRequest request) {
        //先分页查询出关联的门店信息(查询关联门店信息表)
        List<MdRelationalStoreTO> relationalStoreList= mdRelationalStoreMapper.selectRelationalStoreListByIds(
                request);
        if (relationalStoreList.isEmpty()) {
            return Response.ok( relationalStoreList);
        }
        //在拿出门店id查询出门店信息
        if (!relationalStoreList.isEmpty()){
            //拿出门店id
            List<Long> storeIds = relationalStoreList.stream()
                    .map(MdRelationalStoreTO::getStoreId)
                    // 过滤掉空值
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            log.info("storeIds:{}",storeIds);
            if (storeIds.isEmpty()){
                return Response.ok(relationalStoreList);
            }
            //这里门店的信息可能发生变更，所以还需要根据门店id查询出门店中心的门店信息findStoreByIds
            InnerFindStoreByIdsRequest storeBy = new InnerFindStoreByIdsRequest();
            storeBy.setIds(storeIds);
            //拿出对应的门店数据
            Response<List<InnerStoreTO>> storeByList = innerStoreReadFacade.findStoreByIds(storeBy);
            // 遍历原始列表，按原顺序更新字段并构建结果
            // 建立门店ID到门店信息的映射
            Map<Long, InnerStoreTO> storeMap = storeByList.getRes().stream()
                    .collect(Collectors.toMap(InnerStoreTO::getId, Function.identity()));
            for (MdRelationalStoreTO relStore : relationalStoreList) {
                InnerStoreTO storeInfo = storeMap.get(relStore.getStoreId());
                if (storeInfo != null) {
                    // 替换字段
                    relStore.setStoreCode(storeInfo.getStoreCode());
                    relStore.setStoreName(storeInfo.getStoreName());
                    relStore.setStoreStatus(storeInfo.getStoreStatus());
                }
            }
        }
        // 如果没有数据或调用失败，返回空分页
        return  Response.ok(relationalStoreList);
    }

    @Override
    public Response<EmployeeUsersTO> echoUser(MdSpecialApplyByRequest request) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)) {
            throw new BusinessException("员工信息不存在");
        }
        //数据返回
        return Response.ok(employeeByUserId);
    }

    @Override
    public Response<List<MdAuditRecordTO>> getApprovalRecord(MdSpecialApplyByRequest request) {
        MdSpecialApplyPO mdSpecialApplyPO = getMdSpecialApplyPO(request);
        if (Objects.isNull(mdSpecialApplyPO)){
            throw new BusinessException("月度MD不存在");
        }
        List<FlowListByInstanceResponse> flowList = flowBpmReadFacade.listByUserIdInstance(request.getWorkflowInstanceId(), mdSpecialApplyPO.getCreatedBy());;
        if (ObjectUtil.isEmpty(flowList)) {
            return Response.ok(Collections.emptyList());
        }

        // 过滤出activityCode为"Activity2"或者approval为"UNDO"的数据
        List<FlowListByInstanceResponse> filteredList = flowList.stream()
                .filter(item -> item.getApproval() != null && !StrUtil.equals("UNDO", item.getApproval().name()))
            .collect(Collectors.toList());

        List<MdAuditRecordTO> auditRecord = mdAuditRecordMapper.getApprovalRecord(request);
        Map<String, MdAuditRecordTO> auditRecordMap = auditRecord.stream().collect(Collectors.toMap(MdAuditRecordTO::getWorkItemId, Function.identity(),(o1,o2)->o2));

        List<MdAuditRecordTO> list = new ArrayList<>();

        filteredList.forEach(item -> {
            MdAuditRecordTO mdAuditRecordTO = new MdAuditRecordTO();
            mdAuditRecordTO.setWorkItemId(item.getId());
            mdAuditRecordTO.setWorkflowInstanceId(item.getInstanceId());
            mdAuditRecordTO.setAuditFlowNode(item.getSourceId());
            mdAuditRecordTO.setAuditFlowNodeName(item.getSourceName());
            if (!StrUtil.equals(BiweeklyMdFlowNodeEnum.BIWEEKLY_MD_START.getBpmCode(), item.getSourceId())) {
                mdAuditRecordTO.setAuditStatus(item.getApproval().getIndex().toString());
            }
            mdAuditRecordTO.setAuditorName(item.getParticipantName());
            mdAuditRecordTO.setAuditAt(item.getFinishTime());
            if (auditRecordMap.containsKey(item.getId())){
                String reason = auditRecordMap.get(item.getId()).getAuditReason();
                if (StrUtil.isNotBlank(reason)){
                    mdAuditRecordTO.setAuditReason( reason);
                }
            }
            list.add(mdAuditRecordTO);
        });

        if (list.isEmpty()){
            return Response.ok(Collections.emptyList());
        }

        list.sort(Comparator.comparing(
                MdAuditRecordTO::getAuditAt,
                Comparator.nullsLast(Comparator.reverseOrder())
        ));
        return Response.ok(list);
    }

    private MdSpecialApplyPO getMdSpecialApplyPO(MdSpecialApplyByRequest request) {
        LambdaQueryWrapper<MdSpecialApplyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdSpecialApplyPO::getWorkflowInstanceId, request.getWorkflowInstanceId());
        return this.baseMapper.selectOne(queryWrapper);
    }

}
