package io.terminus.lshm.product.server.facade.flow;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import io.terminus.lshm.flow.facade.FlowApiFacade;
import io.terminus.lshm.flow.request.*;
import io.terminus.lshm.flow.response.FlowListByInstanceResponse;
import io.terminus.lshm.flow.response.FlowListItemAvailableActionResponse;
import io.terminus.lshm.flow.response.FlowListRejectAvailableItemResponse;
import io.terminus.lshm.flow.response.FlowStartWithBizResponse;
import io.terminus.lshm.product.common.bean.request.adjust.AdjustBpmExpireTime;
import io.terminus.lshm.product.common.offshelf.dto.EmployeeUsersTO;
import io.terminus.lshm.product.facade.flow.FlowBpmReadFacade;
import io.terminus.lshm.product.facade.flow.FlowBpmWriteFacade;
import io.terminus.lshm.product.server.external.user.EmployeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description
 * @Date 2025/5/29
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class FlowBpmWriteFacadeImpl implements FlowBpmWriteFacade {

    private final FlowApiFacade flowApiFacade;

    private final EmployeeService employeeService;


    @Override
    public FlowStartWithBizResponse startWithBiz(FlowStartWithBizRequest request) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)){
            throw new RuntimeException("用户信息获取失败！");
        }
        request.setUserId(employeeByUserId.getEmployeeCode());
        return flowApiFacade.startWithBiz(request);
    }

    @Override
    public Boolean submitItem(FlowSubmitItemRequest request) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)){
            throw new RuntimeException("用户信息获取失败！");
        }
        request.setUserId(employeeByUserId.getEmployeeCode());
        return flowApiFacade.submitItem(request);
    }

    @Override
    public Boolean rejectItem(FlowRejectItemRequest request) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)){
            throw new RuntimeException("用户信息获取失败！");
        }
        request.setUserId(employeeByUserId.getEmployeeCode());
        return flowApiFacade.rejectItem(request);
    }

    @Override
    public Boolean audit(FlowBpmRequest request) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)){
            throw new RuntimeException("用户信息获取失败！");
        }
        if (request.getSubmitToReject()){
            //if (null==request.getSkipLevels()) {
            FlowSubmitItemRequest flowSubmitItemRequest = new FlowSubmitItemRequest();
            BeanUtil.copyProperties(request, flowSubmitItemRequest);
            flowSubmitItemRequest.setUserId(employeeByUserId.getEmployeeCode());
            flowSubmitItemRequest.setData(request.getData());
            return flowApiFacade.submitItem(flowSubmitItemRequest);
            //}

            /*for (int  i = 0; i < request.getSkipLevels(); i++){
                FlowListByInstanceRequest flowListByInstanceRequest = new FlowListByInstanceRequest();
                flowListByInstanceRequest.setUserId("L08677");
                flowListByInstanceRequest.setWorkflowInstanceId(request.getWorkflowInstanceId());
                List<FlowListByInstanceResponse> flowListByInstanceResponses = flowApiFacade.listByInstance(flowListByInstanceRequest);
                if (CollUtil.isEmpty(flowListByInstanceResponses)){
                    throw new RuntimeException("个人待办任务为空！");
                }
                //构造审批请求
                FlowListByInstanceResponse flowListByInstanceResponse = flowListByInstanceResponses.get(0);
                FlowSubmitItemRequest flowSubmitItemRequest = new FlowSubmitItemRequest();
                BeanUtil.copyProperties(request, flowSubmitItemRequest);
                flowSubmitItemRequest.setWorkItemId(flowListByInstanceResponse.getId());
                flowSubmitItemRequest.setUserId("L08677");
                flowSubmitItemRequest.setData(request.getData());
                flowSubmitItemRequest.setComment("跳级通过！");
                Boolean submitItem = flowApiFacade.submitItem(flowSubmitItemRequest);
                if (!submitItem){
                    throw new RuntimeException("自动审批通过失败！");
                }
            }
            return Boolean.TRUE;
            */
        }
        FlowRejectItemRequest flowRejectItemRequest = new FlowRejectItemRequest();
        BeanUtil.copyProperties(request,flowRejectItemRequest);
        flowRejectItemRequest.setUserId(employeeByUserId.getEmployeeCode());
        flowRejectItemRequest.setSubmitToReject(Boolean.TRUE);
        return flowApiFacade.rejectItem(flowRejectItemRequest);
    }

    @Override
    public Boolean cancelItem(FlowCancelItemRequest request) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)){
            throw new RuntimeException("用户信息获取失败！");
        }
        request.setUserId(employeeByUserId.getEmployeeCode());
        return flowApiFacade.cancelItem(request);
    }
}
