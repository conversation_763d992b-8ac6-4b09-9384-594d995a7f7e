package io.terminus.lshm.product.server.external.promotion.convert;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import groovy.lang.Tuple2;
import io.terminus.gaia.domain.promotion.api.model.PromotionCampaignApportion;
import io.terminus.gaia.domain.promotion.api.model.PromotionCampaignItemDO;
import io.terminus.gaia.domain.promotion.api.model.PromotionCampaignItemScopeDO;
import io.terminus.gaia.promotion.campaign.api.model.MemberLimit;
import io.terminus.gaia.promotion.campaign.api.model.PromotionCampaign;
import io.terminus.gaia.promotion.campaign.api.model.PromotionCampaignShop;
import io.terminus.lshm.item.common.bean.model.InnerMeasureUnitItemTO;
import io.terminus.lshm.member.common.model.CeUser;
import io.terminus.lshm.product.common.bean.request.adjust.AdjustItemAddRequest;
import io.terminus.lshm.product.common.bean.request.adjust.AdjustPriceAddRequest;
import io.terminus.lshm.product.common.bean.request.adjust.AdjustStoreAddRequest;
import io.terminus.lshm.product.facade.price.request.ExtPromotionCampaignItemDO;
import io.terminus.lshm.product.facade.price.request.ExtPromotionCampaignShop;
import io.terminus.lshm.product.facade.price.request.PromotionCampaignRequest;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustCategoryPricePO;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustItemPO;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustPricePO;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustStorePO;
import io.terminus.lshm.product.server.external.SystemBrandEnums;
import io.terminus.lshm.product.server.factory.handler.dto.AdjustPriceHandlerDTO;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.exception.BusinessException;
import org.mapstruct.Mapper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATE_FORMAT;

@Mapper(componentModel = "spring")
public interface PromotionBaseDataConverter {


    default  PromotionCampaign initPromotionCampaign(AdjustPriceHandlerDTO request, Map<String, InnerMeasureUnitItemTO> stringTuple2Map) {
        AdjustPricePO adjustPricePO = request.getAdjustPricePO();
        PromotionCampaign promotionCampaign = new PromotionCampaign();
        promotionCampaign.setPromotionCampaignDesc(adjustPricePO.getReason());
        promotionCampaign.setIsCycleCampaign(false);
        promotionCampaign.setOverlayMemberPrice(0);
        promotionCampaign.setEntityId("1");
        promotionCampaign.setRetailFormatId(1L);
        promotionCampaign.setOverlayThirdCoupon(1);
        promotionCampaign.setPromotionTemplateTypeDict("NEAR_EXPIRY");
        promotionCampaign.setSourceFrom(4);//来源给4
        promotionCampaign.setPromotionCampaignChannel("OFFLINE");
        promotionCampaign.setPromotionCampaignName(adjustPricePO.getTitile());
        AdjustCategoryPricePO adjustCategoryPricePO = request.getCategoryPricePOList().get(0);
        promotionCampaign.setPromotionCampaignStartTime(adjustCategoryPricePO.getDeliveryTimeStart());
        promotionCampaign.setPromotionCampaignEndTime(adjustCategoryPricePO.getDeliveryTimeEnd());
        promotionCampaign.setShopScope("SPECIFY_CONTAINS");
        promotionCampaign.setShopList(this.initStore(request.getStorePOList()));
        promotionCampaign.setItemScope(this.initItemScope(request.getAdjustItemPOList(), stringTuple2Map,onlyOneImage(request.getAdjustPricePO().getFileUrl())));
        promotionCampaign.setApportion(this.initStoreApportionPer());
        promotionCampaign.setPromotionCampaignTemplateId(32L);
        promotionCampaign.setSubmitOptType(1);
        MemberLimit memberLimit = new MemberLimit();
        memberLimit.setLimitMember(0);
        memberLimit.setMemberLevel(Collections.emptyList());
        promotionCampaign.setMemberLimit(memberLimit);
        promotionCampaign.setDiscountToolCodeDict("DISCOUNT");
        CeUser ceUser = new CeUser();
        ceUser.setId(adjustPricePO.getCreatedBy());
        ceUser.setNickname(adjustPricePO.getCreatedName());
        promotionCampaign.setCreatedBy(ceUser);
        return promotionCampaign;
    }

    default String onlyOneImage(String fileUrl) {
        String url ="";
        if(StringUtils.hasText(fileUrl)) {
            JSONArray jsonArray = JSON.parseArray(fileUrl);
            if(!CollectionUtils.isEmpty(jsonArray)){
                JSONObject o = (JSONObject) jsonArray.get(0);
                url = o.getString("url");
            }
        }
        return JSON.toJSONString(Lists.newArrayList(url));
    }

    default PromotionCampaignApportion initStoreApportionPer() {
        PromotionCampaignApportion promotionCampaignApportion = new PromotionCampaignApportion();
        promotionCampaignApportion.setStoreApportionPer(new BigDecimal(100));
        promotionCampaignApportion.setFactoryApportionPer(BigDecimal.ZERO);
        promotionCampaignApportion.setSupplierApportionPer(BigDecimal.ZERO);
        promotionCampaignApportion.setPlatformApportionPer(BigDecimal.ZERO);
        return promotionCampaignApportion;

    }

    default PromotionCampaignItemScopeDO initItemScope(List<AdjustItemPO> adjustItemPOList, Map<String, InnerMeasureUnitItemTO> stringTuple2Map, String fileUrl) {
        PromotionCampaignItemScopeDO promotionCampaignItemScopeDO = new PromotionCampaignItemScopeDO();
        promotionCampaignItemScopeDO.setScopeMode("APPOINT_USE");
        promotionCampaignItemScopeDO.setItemList(this.initItemList(adjustItemPOList,stringTuple2Map,fileUrl));
        return promotionCampaignItemScopeDO;

    }

    default List<PromotionCampaignItemDO> initItemList(List<AdjustItemPO> adjustItemPOList, Map<String, InnerMeasureUnitItemTO> stringTuple2Map, String fileUrl) {
        List<PromotionCampaignItemDO> promotionCampaignItemDOlist=Lists.newArrayList();
        for (AdjustItemPO adjustItemPO :adjustItemPOList) {
            String itemUnitName = adjustItemPO.getItemUnitName();
            if (!StringUtils.hasText(adjustItemPO.getItemUnitName())) {
                itemUnitName = adjustItemPO.getSaleItemUnitName();
            }
            InnerMeasureUnitItemTO longStringTuple2 = stringTuple2Map.get(adjustItemPO.getItemCode() + "_" +itemUnitName);
            if(Objects.isNull(longStringTuple2)){
                throw new BusinessException("商品单位不存在: " + adjustItemPO.getItemCode() + ":" + itemUnitName);
            }
            promotionCampaignItemDOlist.add(convertFromAdjustItemPO(adjustItemPO,longStringTuple2,fileUrl));
        }
        return promotionCampaignItemDOlist;

    }

    default PromotionCampaignItemDO convertFromAdjustItemPO(AdjustItemPO adjustItemPO, InnerMeasureUnitItemTO longStringTuple2, String fileUrl) {
        PromotionCampaignItemDO promotionCampaignItemDO = new PromotionCampaignItemDO();
        Long itemId = Long.valueOf(adjustItemPO.getItemId());
        promotionCampaignItemDO.setItemId(itemId);
        promotionCampaignItemDO.setId(itemId);
        promotionCampaignItemDO.setItemCode(adjustItemPO.getItemCode());
        promotionCampaignItemDO.setItemName(adjustItemPO.getItemName());
        promotionCampaignItemDO.setLineTotalQty(new BigDecimal(9999));
        promotionCampaignItemDO.setItemPrice(adjustItemPO.getItemRetailPrice());
        promotionCampaignItemDO.setItemUnit(adjustItemPO.getItemUnitName());
        promotionCampaignItemDO.setMeasureId(longStringTuple2.getId());
        promotionCampaignItemDO.setItemBarCode(longStringTuple2.getBarcode());
        promotionCampaignItemDO.setItemImages(fileUrl);
        BigDecimal rate = NumberUtil.div(adjustItemPO.getDiscountRate(), new BigDecimal(10));
        promotionCampaignItemDO.setPreferentialPrice(rate);
        String itemBatch = DateUtil.format(adjustItemPO.getProductionDateStart(), PURE_DATE_FORMAT);
        promotionCampaignItemDO.setItemBatch(JSON.toJSONString(Lists.newArrayList(itemBatch)));
        return promotionCampaignItemDO;

    }


    default List<PromotionCampaignShop> initStore(List<AdjustStorePO> storePOList) {
        List<PromotionCampaignShop> promotionCampaignShoplist=Lists.newArrayList();
        for (AdjustStorePO adjustStorePO :storePOList) {
            promotionCampaignShoplist.add(convertFromAdjustStorePO(adjustStorePO));
        }
        return promotionCampaignShoplist;

    }

    default PromotionCampaignShop convertFromAdjustStorePO(AdjustStorePO adjustStorePO) {
        PromotionCampaignShop promotionCampaignShop = new PromotionCampaignShop();
        promotionCampaignShop.setShopID(adjustStorePO.getStoreId()+"");
        promotionCampaignShop.setShopName(adjustStorePO.getStoreName());
        promotionCampaignShop.setShopCode(adjustStorePO.getStoreCode());
        return promotionCampaignShop;

    }

    default FlowApplyBizRequest<AdjustPriceAddRequest> initAdjustApply(PromotionCampaignRequest dto, Map<String, InnerMeasureUnitItemTO> stringInnerMeasureUnitItemTOMap){
        FlowApplyBizRequest flowApplyBizRequest = new FlowApplyBizRequest();
        flowApplyBizRequest.setBizForm(this.initAdjustApplyReq(dto,stringInnerMeasureUnitItemTOMap));
        return flowApplyBizRequest;


    }

    default AdjustPriceAddRequest initAdjustApplyReq(PromotionCampaignRequest dto, Map<String, InnerMeasureUnitItemTO> stringInnerMeasureUnitItemTOMap){
        AdjustPriceAddRequest adjustPriceAddRequest = new AdjustPriceAddRequest();
//        adjustPriceAddRequest.setAdjustNotice();
//        adjustPriceAddRequest.setPromotionType();
        adjustPriceAddRequest.setFormType(3);
        adjustPriceAddRequest.setSource(2);
//        adjustPriceAddRequest.setCategories();
        adjustPriceAddRequest.setBrandId(SystemBrandEnums.getByBrandCode(dto.getBrand()).getBrandId().intValue());
        adjustPriceAddRequest.setReason(dto.getPromotionCampaignDesc());
//        adjustPriceAddRequest.setRemark();
        List<Attachment.File> collect = dto.getItemScope().getItemList().stream().map(e -> JSON.parseArray(e.getItemImages(), String.class)).flatMap(List::stream).distinct().map(url -> initUrl(url)).collect(Collectors.toList());

        adjustPriceAddRequest.setFileUrl(JSON.toJSONString(collect));
        adjustPriceAddRequest.setStores(this.initPromotionStoreList(dto.getShopList()));
        adjustPriceAddRequest.setItems(this.initPromotionItemList(dto.getItemScope().getItemList(),stringInnerMeasureUnitItemTOMap));
        adjustPriceAddRequest.setPromotionId(dto.getId());
        return adjustPriceAddRequest;

    }

   default List<AdjustStoreAddRequest> initPromotionStoreList(List<ExtPromotionCampaignShop> shopList){
       List<AdjustStoreAddRequest> adjustStoreAddRequestlist=Lists.newArrayList();
       for (ExtPromotionCampaignShop promotionCampaignShop :shopList) {
       	adjustStoreAddRequestlist.add(convertFromPromotionCampaignShop(promotionCampaignShop));
       }
       return adjustStoreAddRequestlist;
   }

    default AdjustStoreAddRequest convertFromPromotionCampaignShop(ExtPromotionCampaignShop promotionCampaignShop){
        AdjustStoreAddRequest adjustStoreAddRequest = new AdjustStoreAddRequest();
        adjustStoreAddRequest.setStoreName(promotionCampaignShop.getShopName());
        adjustStoreAddRequest.setStoreId(Long.valueOf(promotionCampaignShop.getShopID()));
        adjustStoreAddRequest.setStoreCode(promotionCampaignShop.getShopName());
//        adjustStoreAddRequest.setStoreType();
//        adjustStoreAddRequest.setProvinceName();
//        adjustStoreAddRequest.setProvinceId();
//        adjustStoreAddRequest.setCityName();
//        adjustStoreAddRequest.setCityId();
//        adjustStoreAddRequest.setAreaName();
//        adjustStoreAddRequest.setAreaId();
        return adjustStoreAddRequest;

    }

    default List<AdjustItemAddRequest> initPromotionItemList(List<ExtPromotionCampaignItemDO> itemList, Map<String, InnerMeasureUnitItemTO> stringInnerMeasureUnitItemTOMap){
        List<AdjustItemAddRequest> adjustItemAddRequestlist=Lists.newArrayList();
        for (ExtPromotionCampaignItemDO promotionCampaignItemDO :itemList) {
        	adjustItemAddRequestlist.add(convertFromPromotionCampaignItemDO(promotionCampaignItemDO,stringInnerMeasureUnitItemTOMap.get(promotionCampaignItemDO.getItemCode()+"_"+promotionCampaignItemDO.getItemUnit())));
        }
        return adjustItemAddRequestlist;


    }

   default AdjustItemAddRequest convertFromPromotionCampaignItemDO(ExtPromotionCampaignItemDO promotionCampaignItemDO, InnerMeasureUnitItemTO innerMeasureUnitItemTO){
       AdjustItemAddRequest adjustItemAddRequest = new AdjustItemAddRequest();
       adjustItemAddRequest.setItemId(String.valueOf(promotionCampaignItemDO.getItemId()));
       adjustItemAddRequest.setItemCode(promotionCampaignItemDO.getItemCode());
       adjustItemAddRequest.setItemName(promotionCampaignItemDO.getItemName());
       adjustItemAddRequest.setItemUnitName(promotionCampaignItemDO.getItemUnit());
       adjustItemAddRequest.setItemUnitId(innerMeasureUnitItemTO.getUnitId()+"");
//       adjustItemAddRequest.setItemUnitCode(innerMeasureUnitItemTO.getU);
       adjustItemAddRequest.setItemArchiveDeliveryPrice(innerMeasureUnitItemTO.getDeliveryPrice());
       adjustItemAddRequest.setItemArchiveRetailPrice(innerMeasureUnitItemTO.getRetailPrice());
       adjustItemAddRequest.setItemArchiveMemberPrice(innerMeasureUnitItemTO.getMemberPrice());
       BigDecimal rate = NumberUtil.mul(promotionCampaignItemDO.getPreferentialPrice(), new BigDecimal(10));
       adjustItemAddRequest.setDiscountRate(rate.doubleValue());
//       adjustItemAddRequest.setDeliveryTimeStart();
//       adjustItemAddRequest.setDeliveryTimeEnd();
//       adjustItemAddRequest.setWarehouseCode();
//       adjustItemAddRequest.setWarehouseName();
       adjustItemAddRequest.setConversion(innerMeasureUnitItemTO.getConversionFactor()+"");
//       adjustItemAddRequest.setExpirationType();//?
       Date dateTime = JSON.parseArray(promotionCampaignItemDO.getItemBatch(), String.class).stream().map(str -> DateUtil.parse(str, PURE_DATE_FORMAT)).findFirst().orElseGet(null);
       adjustItemAddRequest.setProductionDateStart(dateTime);

       adjustItemAddRequest.setExpiration(innerMeasureUnitItemTO.getQualityGuaranteePeriod());

       return adjustItemAddRequest;


   }

    default Attachment.File initUrl(String url){
        Attachment.File file = new Attachment.File();
        file.setUrl(url);
        return file;


    }
}
