package io.terminus.lshm.product.server.facade.biweekly;

import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyPageRequest;
import io.terminus.lshm.product.common.offshelf.dto.EmployeeUsersTO;
import io.terminus.lshm.store.common.bean.request.store.read.InnerPageAllRequest;
import io.terminus.lshm.product.facade.biweekly.MdSpecialApplyReadFacade;
import io.terminus.lshm.product.server.domain.biweekly.service.read.MdSpecialApplyReadService;
import io.terminus.lshm.store.common.model.store.InnerStoreTO;
import io.terminus.trantorframework.Paging;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.terminus.trantorframework.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
public class MdSpecialApplyReadFacadeImpl implements MdSpecialApplyReadFacade {

    private final MdSpecialApplyReadService mdSpecialApplyReadService;




    @Override
    public Response<Paging<MdSpecialApplyTO>> pageMdSpecialApply(MdSpecialApplyPageRequest request) {
        Paging<MdSpecialApplyTO>  page = mdSpecialApplyReadService.pageMdSpecialApply(request);
        return Response.ok(page);
    }


    @Override
    public Response<Paging<InnerStoreTO>> getStoreList(InnerPageAllRequest request) {
        return mdSpecialApplyReadService.getStoreList( request);
    }


    @Override
    public Response<MdSpecialApplyTO> getMdSpecialApply(MdSpecialApplyByRequest request) {
        return mdSpecialApplyReadService.getMdSpecialApply( request);
    }

    @Override
    public Response<List<MdRelationalStoreTO>> getRelationalStoreInfo(MdSpecialApplyPageRequest request) {
        return mdSpecialApplyReadService.getRelationalStoreInfo(request);
    }


    @Override
    public Response<EmployeeUsersTO> echoUser(MdSpecialApplyByRequest request) {
        return mdSpecialApplyReadService.echoUser(request);
    }

    @Override
    public Response<List<MdAuditRecordTO>> getApprovalRecord(MdSpecialApplyByRequest request) {
        return mdSpecialApplyReadService.getApprovalRecord(request);
    }
}
