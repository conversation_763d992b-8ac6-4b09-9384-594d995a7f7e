package io.terminus.lshm.product.server.domain.biweekly.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.terminus.lshm.common.mybatisplus.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Description 双周MD附件信息表实体
 * <AUTHOR>
 * @date 2025/6/19 9:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("md_attachment_info")
public class MdAttachmentInfoPO extends BaseModel<Long> implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 图片或PDF文件地址
     */
    @TableField(value = "file_url")
    private String fileUrl;

    /**
     * 申请表ID（一对多）
     */
    @TableField(value = "special_apply_id")
    private Long specialApplyId;

}
