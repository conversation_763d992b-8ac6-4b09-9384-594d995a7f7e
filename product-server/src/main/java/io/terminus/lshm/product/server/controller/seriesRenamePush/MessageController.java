package io.terminus.lshm.product.server.controller.seriesRenamePush;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.lshm.ncc.facade.NccNewApiFacade;
import io.terminus.lshm.ncc.request.NccStopPurchaseOrgRequest;
import io.terminus.lshm.ncc.request.NccStopPurchaseRequest;
import io.terminus.lshm.ncc.response.NccStopPurchaseDetailResponse;
import io.terminus.lshm.ncc.util.ncc.APIUtils;
import io.terminus.lshm.product.common.enums.AuditStatusEnum;
import io.terminus.lshm.product.message.request.EventMessageRequest;
import io.terminus.lshm.product.message.to.PushEventMessageDTO;
import io.terminus.lshm.product.server.domain.message.service.write.PushEventMessageWriteService;
import io.terminus.lshm.product.server.domain.message.service.write.StateEventMessageWriteService;
import io.terminus.lshm.product.server.domain.newArrival.dao.NewArrivalItemTaskDao;
import io.terminus.lshm.product.server.domain.newArrival.model.NewArrivalItemTaskPO;
import io.terminus.lshm.product.server.domain.offshelf.model.OffShelfItemTaskPO;
import io.terminus.lshm.product.server.domain.offshelf.service.read.OffShelfItemTaskReadService;
import io.terminus.lshm.product.server.enums.PushEventBizTypeEnum;
import io.terminus.lshm.product.server.enums.PushEventStateEnum;
import io.terminus.lshm.product.server.enums.SystemConnectEnum;
import io.terminus.lshm.product.server.external.mq.payload.PushEventPayload;
import io.terminus.lshm.product.server.external.mq.payload.StateEventPayload;
import io.terminus.lshm.product.server.external.mq.producer.PushEventProducer;
import io.terminus.lshm.product.server.external.mq.producer.StateEventProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 供方品牌管理
 *
 * <AUTHOR>
 */
@Api(tags = "系列品名称")
@RequestMapping("/api/message")
@Slf4j
@RestController
public class MessageController {

    @Resource
    StateEventProducer stateEventProducer;
    @Resource
    OffShelfItemTaskReadService offShelfItemTaskReadService;
    @Resource
    StateEventMessageWriteService stateEventMessageWriteService;
    @Resource
    PushEventMessageWriteService pushEventMessageWriteService;
    @Value("${product.topic.pushEvent:topic-product-center-pushEvent}")
    String topic;
    @Resource
    PushEventProducer pushEventProducer;
    @Resource
    private NccNewApiFacade nccNewApiFacade;
    @Resource
    private NewArrivalItemTaskDao newArrivalItemTaskDao;


    @Value("item-read-inner-item")
    private java.lang.String itemReadInnerItem;

    @Value("item-server")
    private java.lang.String itemServer;


    @Value("lshm.item.local")
    private java.lang.String itemLocal;

    @Resource
    private Environment environment;


    @GetMapping(value = "/list")
    @ApiOperation("列表")
    public String list(@RequestParam String str) {
        System.out.println("request demo ok" + itemReadInnerItem + itemServer + itemLocal);
        System.out.println(str);
        String result = environment.getProperty(str);
        log.info("result={}", result);
        return result;
    }

    @PostMapping(value = "/saveAndSendMessageById")
    @ApiOperation("列表")
    public String saveAndSendMessageById(@RequestParam String bizId) {
        Date updatedAt = new Date();
        stateEventMessageWriteService.saveAndSendMessage(Long.valueOf(bizId), bizId, updatedAt, PushEventBizTypeEnum.RECORD_FILING.getCode());
        return "true";
    }

    @PostMapping(value = "/saveAndSendMessage")
    @ApiOperation("列表")
    public String saveAndSendMessage(@RequestBody EventMessageRequest payload) {
        Date updatedAt = new Date();
        stateEventMessageWriteService.saveAndSendMessage(payload.getBizId(), payload.getBizCode(), updatedAt, payload.getBizType());
        return "true";
    }

    @PostMapping(value = "/updateAndSendMessage")
    @ApiOperation("列表")
    public String updateAndSendMessage(@RequestBody EventMessageRequest payload) {
        stateEventMessageWriteService.updateAndSendMessage(payload.getBizId()
                , payload.getBizType(), payload.getState(), payload.getMessageStatus());
        return "true";
    }

    @PostMapping(value = "/savePushMessage")
    @ApiOperation("列表")
    public String savePushMessage(@RequestBody EventMessageRequest payload) {
        PushEventMessageDTO pushEventMessageDTO = new PushEventMessageDTO();
        BeanUtils.copyProperties(payload, pushEventMessageDTO);
        pushEventMessageDTO.setToSystem(SystemConnectEnum.MDM.getCode());
        pushEventMessageWriteService.saveEventMessage(pushEventMessageDTO);
        return "true";
    }

    @PostMapping(value = "/updateAndSendPushMessage")
    @ApiOperation("列表")
    public String updateAndSendPushMessage(@RequestBody EventMessageRequest payload) {
        PushEventPayload pushEventPayload = new PushEventPayload();
        BeanUtils.copyProperties(payload, pushEventPayload);
        String msgId = pushEventProducer.sendMdmMessageOrderly(pushEventPayload, pushEventPayload.getBizId(), topic, pushEventPayload.getBizType());
        log.info("msgId={},updateAndSendPushMessage_controller", msgId);
        return "true";
    }

    @GetMapping(value = "/sendMessage")
    @ApiOperation("列表")
    public java.lang.String sendMessage() {
        StateEventPayload pushEventPayload = new StateEventPayload();
        pushEventPayload.setBizId(630L);
        pushEventPayload.setBizCode("POF202505160009");
        pushEventPayload.setBizType(PushEventBizTypeEnum.OFF_SHELF.getCode());
        pushEventPayload.setState(PushEventStateEnum.COMPLETE.getCode());
//        pushEventPayload.setUpdateTime();

        java.lang.String msgId = stateEventProducer.sendMdmMessageOrderly(pushEventPayload, pushEventPayload.getBizCode(), "topic-product-center-stateEvent", pushEventPayload.getBizType());
        System.out.println("request demo ok " + msgId);
        return "demo is ok";
    }

    @GetMapping(value = "/sendNccOffShelf")
    @ApiOperation("列表")
    public java.lang.String sendNccOffShelf(@RequestParam String bizId) {
        OffShelfItemTaskPO offShelfItemTaskPO = offShelfItemTaskReadService.getById(Long.valueOf(bizId));
        sendOffShelfMsg(offShelfItemTaskPO);
        return "demo is ok";
    }

    private void sendOffShelfMsg(OffShelfItemTaskPO offShelfItemTaskPO) {
        StateEventPayload pushEventPayload = new StateEventPayload();
        pushEventPayload.setBizId(offShelfItemTaskPO.getId());
        pushEventPayload.setBizCode(offShelfItemTaskPO.getTaskCode());
        pushEventPayload.setBizType(PushEventBizTypeEnum.OFF_SHELF.getCode());
        pushEventPayload.setState(PushEventStateEnum.COMPLETE.getCode());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
        if (offShelfItemTaskPO.getUpdatedAt() != null) {
            pushEventPayload.setUpdateTime(simpleDateFormat.format(offShelfItemTaskPO.getUpdatedAt()));
        }

        String msgId = stateEventProducer.sendMdmMessageOrderly(pushEventPayload, pushEventPayload.getBizCode(), "topic-product-center-stateEvent", pushEventPayload.getBizType());
        System.out.println("request demo ok " + msgId);
    }

    @PostMapping(value = "/nccApi")
    @ApiOperation("列表")
    public java.lang.String nccApi() {
        List<NccStopPurchaseRequest> nccStopPurchaseRequests = new ArrayList<>();
        NccStopPurchaseRequest nccStopPurchaseRequest = new NccStopPurchaseRequest();
        nccStopPurchaseRequest.setPkMaterial("109120");
        List<NccStopPurchaseOrgRequest> data = new ArrayList<>();
        NccStopPurchaseOrgRequest nccStopPurchaseOrgRequest = new NccStopPurchaseOrgRequest();
        nccStopPurchaseOrgRequest.setPkOrg("10102");
        data.add(nccStopPurchaseOrgRequest);
        nccStopPurchaseRequest.setData(data);
        nccStopPurchaseRequests.add(nccStopPurchaseRequest);
        List<NccStopPurchaseDetailResponse> nccStopPurchaseDetailResponseList = nccNewApiFacade.updateStopPurchaseFlag(nccStopPurchaseRequests);
        System.out.println("request demo ok" + nccStopPurchaseDetailResponseList);
        return "demo is ok";
    }

    @PostMapping(value = "/nccApiNative")
    @ApiOperation("列表")
    public java.lang.String nccApiNative() {
        APIUtils util = new APIUtils();
        java.lang.String pubKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqRmdr891wocPo1SQGsEZ22HGf5DawX56j2smY0fs/8kjxtfvTx+0OLijAP03w4bqnPktqR/kWaIKWVIdpiXJQD/Ga0j1y9HVU80dyW4GIyKIvyGTkrIVPq3vcSkpAxavL/+CXieKmDYDGFG2KaLkCxfRGGaEPtzsJ+k2oveWwsdNuIQlO/POVBHAdTKAoNGnnCnYJGcXW+I6YeAYGfrZBwHwp6gcacD1Pijhh9NY4qtYKbZo2M0QMNnAsE+eylQwT8i/a/FS7TL6dt+wcpUH1algFGKWPNID9gDjqt2WNqzOmVyk6xx2di51yAHB1xSHjO060k8zlNube4Fg05KBAwIDAQAB";
        util.init("121.43.144.59", "8012", "001", "lshmyidea"
                , "4f8882b593d34be6adbe", pubKey, "LSHMUSER", "aaa!123456");

        List<NccStopPurchaseRequest> nccStopPurchaseRequests = new ArrayList<>();
        NccStopPurchaseRequest nccStopPurchaseRequest = new NccStopPurchaseRequest();
        nccStopPurchaseRequest.setPkMaterial("109120");
        List<NccStopPurchaseOrgRequest> data = new ArrayList<>();
        NccStopPurchaseOrgRequest nccStopPurchaseOrgRequest = new NccStopPurchaseOrgRequest();
        nccStopPurchaseOrgRequest.setPkOrg("10102");
        data.add(nccStopPurchaseOrgRequest);
        nccStopPurchaseRequest.setData(data);
        nccStopPurchaseRequests.add(nccStopPurchaseRequest);
        List<NccStopPurchaseDetailResponse> nccStopPurchaseDetailResponseList = nccNewApiFacade.updateStopPurchaseFlag(nccStopPurchaseRequests);
        System.out.println("request demo ok" + nccStopPurchaseDetailResponseList);
        return "demo is ok";
    }


    @PostMapping(value = "/sendMsg")
    @ApiOperation("发送自定义消息")
    public String sendMsg(@RequestBody StateEventPayload payload) {
        return stateEventProducer.sendMdmMessageOrderly(payload, payload.getBizCode(), "topic-product-center-stateEvent", payload.getBizType());
    }

    @GetMapping(value = "/tradeHistoryDataHandle")
    @ApiOperation("交易上新替换历史数据处理")
    public void tradeHistoryDataHandle(@RequestParam Date date) throws InterruptedException {
        List<NewArrivalItemTaskPO> taskPOS = newArrivalItemTaskDao.selectList(new LambdaQueryWrapper<NewArrivalItemTaskPO>()
                .eq(NewArrivalItemTaskPO::getAuditStatus, AuditStatusEnum.COMPLETED.getCode())
                .isNotNull(NewArrivalItemTaskPO::getReplaceItemCodeList)
                .gt(Objects.nonNull(date), NewArrivalItemTaskPO::getAuditCompletionTime, date)
                .orderByAsc(NewArrivalItemTaskPO::getId));
        taskPOS = taskPOS.stream().filter(po -> StrUtil.isNotBlank(po.getReplaceItemCodeList())).collect(Collectors.toList());

        if (CollUtil.isEmpty(taskPOS)) {
            return;
        }
        for (NewArrivalItemTaskPO taskPO : taskPOS) {
            sendReplaceMessage(taskPO);
            Thread.sleep(3000);
        }
    }

    private void sendReplaceMessage(NewArrivalItemTaskPO taskPO) {
        if (Objects.isNull(taskPO)) {
            return;
        }
        Long businessId = taskPO.getId();
        // 判断是否有替换
        String replaceItemCodeStr = taskPO.getReplaceItemCodeList();
        if (StrUtil.isBlank(replaceItemCodeStr)) {
            return;
        }
        List<String> replaceCodeList = StrUtil.split(replaceItemCodeStr, StrUtil.COMMA);
        StateEventPayload payload = new StateEventPayload();
        payload.setBizId(businessId);
        payload.setBizType(PushEventBizTypeEnum.NEW_ARRIVAL_REPLACE.getCode());
        payload.setUpdateTime(DateUtil.formatDateTime(taskPO.getAuditCompletionTime()));
        payload.setState(PushEventStateEnum.COMPLETE.getCode());
        for (String replaceCode : replaceCodeList) {
            payload.setBizCode(replaceCode);
            try {
                stateEventProducer.sendMdmMessageOrderly(payload, businessId, "topic-product-center-stateEvent", payload.getBizType());
            } catch (NumberFormatException e) {
                log.error("businessId={},payload={},msg={},send msg fail", businessId, payload, e.getMessage());
            }
        }
    }

    @GetMapping(value = "/tradeMsgTest")
    @ApiOperation("交易上新测试")
    public void tradeMsgTest(@RequestParam Long id) {
        NewArrivalItemTaskPO taskPO = newArrivalItemTaskDao.selectById(id);
        sendReplaceMessage(taskPO);
    }

    @GetMapping(value = "/tradeOffShelfHistoryDataHandle")
    @ApiOperation("交易下架历史数据处理")
    public void tradeOffShelfHistoryDataHandle(@RequestParam Date date) throws InterruptedException {
        List<OffShelfItemTaskPO> list = offShelfItemTaskReadService.list(new LambdaQueryWrapper<OffShelfItemTaskPO>()
                .eq(OffShelfItemTaskPO::getAuditFlowNode, AuditStatusEnum.COMPLETED.getCode())
                .gt(Objects.nonNull(date), OffShelfItemTaskPO::getUpdatedAt, date)
                .orderByAsc(OffShelfItemTaskPO::getId));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        for (OffShelfItemTaskPO taskPO : list) {
            sendOffShelfMsg(taskPO);
            Thread.sleep(3000);
        }
    }

    @GetMapping(value = "/sendNewArrivalMsg")
    @ApiOperation("发送上新消息")
    public String sendNewArrivalMsg(@RequestParam String bizId) {
        NewArrivalItemTaskPO taskPO = newArrivalItemTaskDao.selectById(bizId);
        sendNewArrivalMsg(taskPO);
        return "demo is ok";
    }

    private void sendNewArrivalMsg(NewArrivalItemTaskPO newArrivalItemTaskPO) {
        StateEventPayload pushEventPayload = new StateEventPayload();
        pushEventPayload.setBizId(newArrivalItemTaskPO.getId());
        pushEventPayload.setBizCode(newArrivalItemTaskPO.getItemCode());
        pushEventPayload.setBizType(PushEventBizTypeEnum.NEW_ARRIVAL.getCode());
        pushEventPayload.setState(PushEventStateEnum.COMPLETE.getCode());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
        if (newArrivalItemTaskPO.getUpdatedAt() != null) {
            pushEventPayload.setUpdateTime(simpleDateFormat.format(newArrivalItemTaskPO.getUpdatedAt()));
        }

        String msgId = stateEventProducer.sendMdmMessageOrderly(pushEventPayload, pushEventPayload.getBizCode(), "topic-product-center-stateEvent", pushEventPayload.getBizType());
        System.out.println("request demo ok " + msgId);
    }
}
