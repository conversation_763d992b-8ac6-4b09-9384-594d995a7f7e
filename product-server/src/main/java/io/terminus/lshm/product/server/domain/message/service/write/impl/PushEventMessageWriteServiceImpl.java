package io.terminus.lshm.product.server.domain.message.service.write.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.terminus.lshm.product.message.to.PushEventMessageDTO;
import io.terminus.lshm.product.message.to.PushEventMessageUpdateStatusDTO;
import io.terminus.lshm.product.server.converter.PushEventMessageConvert;
import io.terminus.lshm.product.server.domain.message.dao.PushEventMessageDao;
import io.terminus.lshm.product.server.domain.message.model.PushEventMessagePO;
import io.terminus.lshm.product.server.domain.message.service.read.PushEventMessageReadService;
import io.terminus.lshm.product.server.domain.message.service.write.PushEventMessageWriteService;
import io.terminus.lshm.server.common.ServerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PushEventMessageWriteServiceImpl extends ServiceImpl<PushEventMessageDao, PushEventMessagePO> implements PushEventMessageWriteService {
    @Resource
    PushEventMessageReadService pushEventMessageReadService;
    @Resource
    PushEventMessageConvert pushEventMessageConvert;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveEventMessage(PushEventMessageDTO pushEventMessageDTO) {
        pushEventMessageDTO.checkParam();
        PushEventMessagePO pushEventMessagePO = pushEventMessageConvert.t2p(pushEventMessageDTO);
        pushEventMessagePO.setUpdatedAt(new Date());
        super.save(pushEventMessagePO);
    }

    private void updateEventMessageStatus(PushEventMessagePO pushEventMessagePO) {
        super.updateById(pushEventMessagePO);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateMessageStatus(PushEventMessageUpdateStatusDTO pushEventMessageUpdateStatusDTO) {
        checkParams(pushEventMessageUpdateStatusDTO);

        LambdaQueryWrapper<PushEventMessagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PushEventMessagePO::getBizId, pushEventMessageUpdateStatusDTO.getBizId())
                .eq(PushEventMessagePO::getBizType, pushEventMessageUpdateStatusDTO.getBizType())
                .eq(PushEventMessagePO::getToSystem, pushEventMessageUpdateStatusDTO.getToSystem())
                .eq(PushEventMessagePO::getState, pushEventMessageUpdateStatusDTO.getState())
                .eq(PushEventMessagePO::getIsDeleted, 0);
        List<PushEventMessagePO> eventMessageList = pushEventMessageReadService.list(wrapper);

        for (PushEventMessagePO pushEventMessagePO : eventMessageList) {
            pushEventMessagePO.setMessageStatus(pushEventMessageUpdateStatusDTO.getMessageStatus());
            pushEventMessagePO.setUpdatedAt(new Date());
            pushEventMessagePO.setUpdatedBy(ServerContext.getUserId());
            pushEventMessagePO.setRetryTimes(pushEventMessagePO.getRetryTimes() + 1);
            pushEventMessagePO.setReturnMessage(pushEventMessageUpdateStatusDTO.getReturnMessage());
            updateEventMessageStatus(pushEventMessagePO);
        }
    }

    private void checkParams(PushEventMessageUpdateStatusDTO pushEventMessageUpdateStatusDTO) {
        if (pushEventMessageUpdateStatusDTO.getBizId() == null
                || StringUtils.isEmpty(pushEventMessageUpdateStatusDTO.getBizType())
                || StringUtils.isEmpty(pushEventMessageUpdateStatusDTO.getState())
                || StringUtils.isEmpty(pushEventMessageUpdateStatusDTO.getToSystem())) {
            log.error("{},bizId,type,state,toSystem is empty",pushEventMessageUpdateStatusDTO);
            throw new IllegalArgumentException("bizId,type,state,toSystem is empty");
        }
    }
}
