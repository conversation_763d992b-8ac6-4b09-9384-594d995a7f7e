package io.terminus.lshm.product.server.facade.biweekly;


import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.facade.biweekly.MdSpecialApplyWriteFacade;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.server.domain.biweekly.service.write.MdSpecialApplyWriteService;
import io.terminus.trantorframework.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
public class MdSpecialApplyWriteFacadeImpl implements MdSpecialApplyWriteFacade {


    private final MdSpecialApplyWriteService mdSpecialApplyWriteService;


    @Override
    public Response<Boolean> addMdSpecialApply(FlowApplyBizRequest<MdSpecialApplyByRequest> request) {
        return mdSpecialApplyWriteService.addMdSpecialApply(request);
    }

    @Override
    public Response<Boolean> updateMdSpecialApply(FlowApplyBizRequest<MdSpecialApplyByRequest> request) {
        return mdSpecialApplyWriteService.updateMdSpecialApply(request);
    }
}
