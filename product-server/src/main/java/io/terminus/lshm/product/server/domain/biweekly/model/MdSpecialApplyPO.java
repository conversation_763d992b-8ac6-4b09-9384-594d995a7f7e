package io.terminus.lshm.product.server.domain.biweekly.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.terminus.lshm.common.mybatisplus.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 双周MMD特殊申请表实体
 * <AUTHOR>
 * @date 2025/6/19 9:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("md_special_apply")
public class MdSpecialApplyPO extends BaseModel<Long> implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申请月份
     */
    @TableField(value = "apply_month_time")
    private Date applyMonthTime;

    /**
     * 流程标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 申请理由
     */
    @TableField(value = "apply_reason")
    private String applyReason;

    /**
     * 审批状态
     */
    @TableField(value = "audit_status")
    private String auditStatus;

    /**
     * 审批流程状态
     */
    @TableField(value = "audit_flow_status")
    private String auditFlowStatus;


    /**
     * 序号
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 手机号
     */
    @TableField(value = "created_phone")
    private String createdPhone;

    /**
     * 报备周期
     */
    @TableField(value = "filing_cycle")
    private String filingCycle;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 门店数
     */
    @TableField(value = "store_relation_quantity")
    private Integer storeRelationQuantity;

    /**
     * 流程实例id
     */
    @TableField("workflow_instance_id")
    private String workflowInstanceId;

    /**
     * 上一次发送催办时间
     */
    @TableField("last_send_time")
    private Date lastSendTime;

    /**
     * 创建名称
     */
    @TableField("created_name")
    private String createdName;

    /**
     * 修改名称
     */
    @TableField("updated_name")
    private String updatedName;


    /**
     * 品牌id 赵一鸣/零食很忙
     */
    @TableField("brand_id")
    private Long brandId;



}
