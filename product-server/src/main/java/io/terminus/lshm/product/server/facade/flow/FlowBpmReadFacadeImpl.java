package io.terminus.lshm.product.server.facade.flow;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import io.terminus.lshm.flow.facade.FlowApiFacade;
import io.terminus.lshm.flow.request.FlowItemExtRequest;
import io.terminus.lshm.flow.request.FlowListByInstanceRequest;
import io.terminus.lshm.flow.request.FlowListItemAvailableActionRequest;
import io.terminus.lshm.flow.request.FlowListRejectAvailableItemRequest;
import io.terminus.lshm.flow.response.*;
import io.terminus.lshm.product.common.bean.response.adjust.AdjustAuditRecordResponse;
import io.terminus.lshm.product.common.enums.WorkFlowCodeEnum;
import io.terminus.lshm.product.common.offshelf.dto.EmployeeUsersTO;
import io.terminus.lshm.product.common.offshelf.dto.OffShelfItemAuditRecordTO;
import io.terminus.lshm.product.facade.flow.FlowBpmReadFacade;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustPricePO;
import io.terminus.lshm.product.server.domain.adjust.service.read.AdjustAuditRecordReadService;
import io.terminus.lshm.product.server.domain.adjust.service.read.AdjustPriceReadService;
import io.terminus.lshm.product.server.external.user.EmployeeService;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description
 * @Date 2025/5/29
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class FlowBpmReadFacadeImpl implements FlowBpmReadFacade {


    private final FlowApiFacade flowApiFacade;

    private final EmployeeService employeeService;

    private final AdjustAuditRecordReadService adjustAuditRecordReadService;

    private final AdjustPriceReadService adjustPriceReadService;



    @Override
    public List<FlowListByInstanceResponse> listByInstance(String workflowInstanceId) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)){
            throw new BusinessException("员工信息不存在");
        }
        FlowListByInstanceRequest flowListByInstanceRequest = new FlowListByInstanceRequest();
        flowListByInstanceRequest.setWorkflowInstanceId(workflowInstanceId);
        flowListByInstanceRequest.setUserId(employeeByUserId.getEmployeeCode());
        return flowApiFacade.listByInstance(flowListByInstanceRequest);
    }

    @Override
    public List<FlowListByInstanceResponse> listByUserIdInstance(String workflowInstanceId, Long userId) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId(userId);
        if (Objects.isNull(employeeByUserId)){
            throw new BusinessException("员工信息不存在");
        }
        FlowListByInstanceRequest flowListByInstanceRequest = new FlowListByInstanceRequest();
        flowListByInstanceRequest.setWorkflowInstanceId(workflowInstanceId);
        flowListByInstanceRequest.setUserId(employeeByUserId.getEmployeeCode());
        return flowApiFacade.listByInstance(flowListByInstanceRequest);
    }

    @Override
    public FlowListRejectAvailableItemResponse listRejectAvailableItem(FlowListRejectAvailableItemRequest request) {
        return flowApiFacade.listRejectAvailableItem(request);
    }

    @Override
    public FlowListItemAvailableActionResponse listAvailableAction(FlowListItemAvailableActionRequest request) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)){
            throw new BusinessException("员工信息不存在");
        }
        request.setUserId(employeeByUserId.getEmployeeCode());
        return flowApiFacade.listAvailableAction(request);
    }

    @Override
    public List<AdjustAuditRecordResponse> listFlowRecord(String workflowInstanceId) {
        AdjustPricePO adjustPricePO = adjustPriceReadService.getByWorkflowInstanceId(workflowInstanceId);
        if (Objects.isNull(adjustPricePO)){
            throw new BusinessException("调价不存在");
        }

        List<FlowListByInstanceResponse> flowList = this.listByInstance(workflowInstanceId);
        if (ObjectUtil.isEmpty(flowList)) {
            return Collections.emptyList();
        }

        Map<String, String> auditRecord = adjustAuditRecordReadService.getAuditRecord(adjustPricePO.getId());

        List<AdjustAuditRecordResponse> list = new ArrayList<>();

        flowList.forEach(item -> {
                AdjustAuditRecordResponse adjustAuditRecordResponse = new AdjustAuditRecordResponse();
                adjustAuditRecordResponse.setAuditFlowNode(item.getSourceId());
                adjustAuditRecordResponse.setAuditFlowNodeName(item.getSourceName());
                adjustAuditRecordResponse.setAuditStatus(item.getApproval().getIndex().toString());
                adjustAuditRecordResponse.setAuditorName(item.getParticipantName());
                adjustAuditRecordResponse.setAuditAt(item.getFinishTime());
                adjustAuditRecordResponse.setWorkItemId(item.getId());
                adjustAuditRecordResponse.setWorkflowInstanceId(item.getInstanceId());
            if (auditRecord.containsKey(item.getId())){
                String reason = auditRecord.get(item.getId());
                if (StrUtil.isNotBlank(reason)){
                    adjustAuditRecordResponse.setAuditReason( reason);
                }
            }
                list.add(adjustAuditRecordResponse);
        });

        return list.stream()
                .sorted(Comparator.comparing(
                        AdjustAuditRecordResponse::getAuditAt,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public List<AdjustAuditRecordResponse> listAdjustFlowRecord(String workflowInstanceId) {
        AdjustPricePO adjustPricePO = adjustPriceReadService.getByWorkflowInstanceId(workflowInstanceId);
        if (Objects.isNull(adjustPricePO)){
            throw new BusinessException("调价不存在");
        }
        Map<String, String> auditRecord = adjustAuditRecordReadService.getAuditRecord(adjustPricePO.getId());
        List<FlowListByInstanceResponse> listByInstanceResponses = listByUserIdInstance(workflowInstanceId, adjustPricePO.getCreatedBy());

        List<AdjustAuditRecordResponse> list = new ArrayList<>();
        listByInstanceResponses.forEach(item -> {
            AdjustAuditRecordResponse adjustAuditRecordResponse = new AdjustAuditRecordResponse();
            adjustAuditRecordResponse.setAuditFlowNode(item.getSourceId());
            adjustAuditRecordResponse.setAuditFlowNodeName(item.getSourceName());
            adjustAuditRecordResponse.setAuditStatus(item.getApproval().getIndex().toString());
            adjustAuditRecordResponse.setAuditorName(item.getParticipantName());
            adjustAuditRecordResponse.setAuditAt(item.getFinishTime());
            adjustAuditRecordResponse.setWorkItemId(item.getId());
            adjustAuditRecordResponse.setWorkflowInstanceId(item.getInstanceId());
            adjustAuditRecordResponse.setAuditor(item.getOriginator());
            if (auditRecord.containsKey(item.getId())){
                String reason = auditRecord.get(item.getId());
                if (StrUtil.isNotBlank(reason)){
                    adjustAuditRecordResponse.setAuditReason( reason);
                }
            }
            list.add(adjustAuditRecordResponse);
        });

        return list.stream()
                .sorted(Comparator.comparing(
                        AdjustAuditRecordResponse::getAuditAt,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ))
                .collect(Collectors.toList());
    }







    @Override
    public String getWorkflowTemplateChart(String workflowInstanceId) {
        return flowApiFacade.getWorkflowChart(WorkFlowCodeEnum.ADJUST_PRICE.getCode(), workflowInstanceId);
    }

    @Override
    public FlowItemExtApiResponse workItemExtList(FlowItemExtRequest request) {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)){
            throw new BusinessException("员工信息不存在");
        }
        request.setUserId(employeeByUserId.getEmployeeCode());
        return flowApiFacade.workItemExtList(request);
    }

    @Override
    public FlowItemExtApiResponse workItemExtListNoUser(FlowItemExtRequest request) {
        return flowApiFacade.workItemExtList(request);
    }

    @Override
    public EmployeeUsersTO getEmployeeUsersTO() {
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)) {
            throw new BusinessException("员工信息不存在");
        }
        return employeeByUserId;
    }


}
