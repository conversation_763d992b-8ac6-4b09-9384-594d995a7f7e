package io.terminus.lshm.product.server.domain.recordFiling.service.read;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import io.terminus.lshm.ncc.response.TreeNccGoodsTypeResponse;
import io.terminus.lshm.ncc.response.TreeNccNewDisplayResponse;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskExportRequest;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskPageRequest;
import io.terminus.lshm.product.common.recordfiling.response.MyApprovalResponse;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskListReaponse;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskPageResponse;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskResponse;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskDTO;
import io.terminus.lshm.product.server.domain.recordFiling.model.RecordFilingItemTaskPO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;

import java.util.List;
import java.util.Map;

public interface RecordFilingItemTaskReadService extends IService<RecordFilingItemTaskPO> {

    /**
     * 查询商品建档列表
     *
     * @param request
     * @return
     */
    Paging<RecordFilingItemTaskPageResponse> page(RecordFilingItemTaskPageRequest request);

    Response<RecordFilingItemTaskResponse> detail(Long id);

    /**
     * 上新获取建档列表
     * @param request
     * @return
     */
    Response<List<RecordFilingItemTaskListReaponse>> newArrivalGetList(RecordFilingItemTaskPageRequest request);


    Page<RecordFilingItemTaskDTO> queryRecordFilingItemTaskList(RecordFilingItemTaskExportRequest request);

    Map<Long,Long> countByBrandIds(List<Long> brandIds);

    Map<Long, Long> countByManufacturers(List<Long> ids);

    List<TreeNccNewDisplayResponse> nccNewDisplayCategory();

    List<TreeNccGoodsTypeResponse> goodsTypeList();

    MyApprovalResponse myApprovals();
}
