package io.terminus.lshm.product.server.domain.message.service.write.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.terminus.lshm.product.message.to.StateEventMessageDTO;
import io.terminus.lshm.product.server.converter.StateEventMessageConvert;
import io.terminus.lshm.product.server.domain.message.dao.StateEventMessageDao;
import io.terminus.lshm.product.server.domain.message.model.StateEventMessagePO;
import io.terminus.lshm.product.server.domain.message.service.read.StateEventMessageReadService;
import io.terminus.lshm.product.server.domain.message.service.write.StateEventMessageWriteService;
import io.terminus.lshm.product.server.enums.EventMessageStatusEnum;
import io.terminus.lshm.product.server.enums.PushEventStateEnum;
import io.terminus.lshm.product.server.external.mq.payload.StateEventPayload;
import io.terminus.lshm.product.server.external.mq.producer.StateEventProducer;
import io.terminus.lshm.server.common.ServerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class StateEventMessageWriteServiceImpl extends ServiceImpl<StateEventMessageDao, StateEventMessagePO> implements StateEventMessageWriteService {
    @Resource
    StateEventProducer stateEventProducer;
    @Resource
    StateEventMessageReadService stateEventMessageReadService;
    @Resource
    StateEventMessageConvert stateEventMessageConvert;

    @Value("${product.topic.stateEvent:topic-product-center-stateEvent}")
    String stateEventTopic;

    private void saveEventMessage(Long bizId, String bizType, String bizCode, Date updatedAt) {
        StateEventMessagePO stateEventMessagePO = new StateEventMessagePO();
        stateEventMessagePO.setBizId(bizId);
        stateEventMessagePO.setBizCode(bizCode);
        stateEventMessagePO.setBizType(bizType);
        stateEventMessagePO.setState(PushEventStateEnum.COMPLETE.getCode());
        if (updatedAt != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            String formattedDate = sdf.format(updatedAt);
            stateEventMessagePO.setBizTime(formattedDate);
        }
        stateEventMessagePO.setCreatedAt(new Date());
        stateEventMessagePO.setCreatedBy(ServerContext.getUserId());
        stateEventMessagePO.setMessageStatus(EventMessageStatusEnum.INIT.getCode());
        boolean result = super.save(stateEventMessagePO);
        if (!result) {
            throw new RuntimeException("saveEventMessage error");
        }
    }

    @Override
    public void saveEventMessage(StateEventMessageDTO stateEventMessageDTO) {
        StateEventMessagePO stateEventMessagePO = stateEventMessageConvert.t2p(stateEventMessageDTO);
        stateEventMessagePO.setUpdatedAt(new Date());
        super.save(stateEventMessagePO);
    }

    private void updateEventMessageStatus(StateEventMessagePO stateEventMessagePO) {
        super.updateById(stateEventMessagePO);
    }

    private String sendStateEventMessage(Long bizId, String bizType, String bizCode, Date updatedAt) {
        StateEventPayload stateEventPayload = new StateEventPayload();
        stateEventPayload.setBizId(bizId);
        stateEventPayload.setBizCode(bizCode);
        stateEventPayload.setBizType(bizType);
        stateEventPayload.setState(PushEventStateEnum.COMPLETE.getCode());
        if (updatedAt != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            String formattedDate = sdf.format(updatedAt);
            stateEventPayload.setUpdateTime(formattedDate);
        }

        String msgId = stateEventProducer.sendMdmMessageOrderly(stateEventPayload, stateEventPayload.getBizCode(), stateEventTopic, stateEventPayload.getBizType());
        log.info("taskId={},taskCode{},msgId={},recordfiling sendMdmMessageOrderly  ok", bizId, bizCode, msgId);
        return msgId;

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveAndSendMessage(Long bizId, String bizCode, Date updatedAt, String bizType) {
        try {
            sendStateEventMessage(bizId, bizType, bizCode, updatedAt);
        } catch (Exception e) {
            log.info("bizId={},bizType={},saveAndSendMessage_error={}", bizId,bizType, e.getMessage());
            //消息发送失败，记录到数据库
            saveEventMessage(bizId, bizType, bizCode, updatedAt);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateAndSendMessage(Long bizId, String bizType, String state, String messageStatus) {

        LambdaQueryWrapper<StateEventMessagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StateEventMessagePO::getBizId, bizId).eq(StateEventMessagePO::getBizType, bizType)
                .eq(StateEventMessagePO::getMessageStatus, messageStatus)
                .eq(StateEventMessagePO::getState, state)
                .eq(StateEventMessagePO::getIsDeleted, 0);
        List<StateEventMessagePO> eventMessageList = stateEventMessageReadService.list(wrapper);

        for (StateEventMessagePO stateEventMessagePO : eventMessageList) {
            String msgId = null;
            try {
                msgId = sendStateEventMessage(stateEventMessagePO.getBizId(), bizType, stateEventMessagePO.getBizCode(), stateEventMessagePO.getUpdatedAt());
            } catch (Exception e) {
                log.error("stateEventMessagePO={},saveAndSendMessage_error={}", stateEventMessagePO, e.getMessage());
                throw new RuntimeException(e);
            } finally {
                if (msgId != null) {
                    stateEventMessagePO.setMessageStatus(EventMessageStatusEnum.FINISHED.getCode());
                } else {
                    stateEventMessagePO.setMessageStatus(EventMessageStatusEnum.FAILED.getCode());
                }
                stateEventMessagePO.setUpdatedAt(new Date());
                stateEventMessagePO.setRetryTimes(stateEventMessagePO.getRetryTimes() + 1);
                stateEventMessagePO.setMsgId(msgId);
                updateEventMessageStatus(stateEventMessagePO);
            }
        }
    }
}
