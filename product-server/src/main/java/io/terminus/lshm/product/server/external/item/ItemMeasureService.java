package io.terminus.lshm.product.server.external.item;

import cn.hutool.core.util.NumberUtil;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import io.terminus.common.utils.NumberUtils;
import io.terminus.lshm.item.common.bean.model.InnerMeasureUnitItemTO;
import io.terminus.lshm.item.common.bean.request.item.InnerItemCodeAndUnitNameListRequest;
import io.terminus.lshm.item.facade.item.read.ItemReadInnerFacade;
import io.terminus.lshm.product.server.converter.ItemBaseDataConverter;
import io.terminus.lshm.product.server.external.item.model.ItemCodeUnitNameDTO;
import io.terminus.lshm.product.util.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/26
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ItemMeasureService {

    private final ItemReadInnerFacade itemReadInnerFacade;
    private final static int PART_SIZE=1000;

    private final ItemBaseDataConverter itemBaseDataConverter;





    public void listMeasureByItemCodeAndUnitName(List<ItemCodeUnitNameDTO> itemTOList){
        if(CollectionUtils.isEmpty(itemTOList)){
            return;
        }
        try {
            for (List<ItemCodeUnitNameDTO> itemCodeUnitNameDTOS : Lists.partition(itemTOList, PART_SIZE)) {
                List<InnerMeasureUnitItemTO> result = listMeasureUnitItem(itemCodeUnitNameDTOS);

                result.forEach(to -> {
                    if (Objects.nonNull(to.getConversionFactor())) {
                        if (to.getIsDeliveryUnit()) {
                            if (Objects.nonNull(to.getDeliveryPrice())) {
                                //配送价
                                BigDecimal deliveryPrice = NumberUtil.mul(to.getDeliveryPrice(), to.getConversionFactor()).setScale(4, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        if (to.getIsSale()) {
                            if (Objects.nonNull(to.getMemberPrice())) {
                                //销售价
                                BigDecimal retailPrice = NumberUtil.mul(to.getRetailPrice(), to.getConversionFactor()).setScale(4, BigDecimal.ROUND_HALF_UP);
                            }
                            if (Objects.nonNull(to.getMemberPrice())) {
                                //会员价
                                BigDecimal memberPrice = NumberUtil.mul(to.getMemberPrice(), to.getConversionFactor()).setScale(4, BigDecimal.ROUND_HALF_UP);
                            }

                        }
                    }

                });

            }
        } catch (Exception e) {
            log.error("listMeasureByItemCodeAndUnitName",e);
        }

    }

    private List<InnerMeasureUnitItemTO> listMeasureUnitItem(List<ItemCodeUnitNameDTO> itemCodeUnitNameDTOS) {
        InnerItemCodeAndUnitNameListRequest request = new InnerItemCodeAndUnitNameListRequest();
        request.setItemCodeAndUnitNameList(itemBaseDataConverter.listMeasure(itemCodeUnitNameDTOS));
        List<InnerMeasureUnitItemTO> result = Assert.getResult(itemReadInnerFacade.listMeasureByItemCodeAndUnitName(request));
        return result;
    }

    public Map<String, InnerMeasureUnitItemTO> mapMeasureUnitItem(List<ItemCodeUnitNameDTO> itemCodeUnitNameDTOS) {
        List<InnerMeasureUnitItemTO> innerMeasureUnitItemTOS = listMeasureUnitItem(itemCodeUnitNameDTOS);
        if(CollectionUtils.isEmpty(innerMeasureUnitItemTOS)){
            return Collections.emptyMap();
        }
        Map<String,InnerMeasureUnitItemTO> map = Maps.newHashMap();
        for (InnerMeasureUnitItemTO innerMeasureUnitItemTO : innerMeasureUnitItemTOS) {
            map.put(innerMeasureUnitItemTO.getItemCode()+"_"+innerMeasureUnitItemTO.getUnitName(),innerMeasureUnitItemTO);
        }
        return map;
    }

    public Map<String, InnerMeasureUnitItemTO> mapPromotionMeasureUnitItem(List<ItemCodeUnitNameDTO> itemCodeUnitNameDTOS) {
        List<InnerMeasureUnitItemTO> innerMeasureUnitItemTOS = listMeasureUnitItem(itemCodeUnitNameDTOS);
        if(CollectionUtils.isEmpty(innerMeasureUnitItemTOS)){
            return Collections.emptyMap();
        }
        Map<String,InnerMeasureUnitItemTO> map = Maps.newHashMap();
        for (InnerMeasureUnitItemTO innerMeasureUnitItemTO : innerMeasureUnitItemTOS) {
            map.put(innerMeasureUnitItemTO.getItemCode()+"_"+innerMeasureUnitItemTO.getUnitName(),innerMeasureUnitItemTO);
        }
        return map;
    }
}
