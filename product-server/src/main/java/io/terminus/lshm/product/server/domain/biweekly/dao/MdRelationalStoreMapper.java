package io.terminus.lshm.product.server.domain.biweekly.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.terminus.lshm.product.common.biweekly.dto.MdAttachmentInfoTO;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import io.terminus.lshm.product.common.biweekly.dto.MdSpecialApplyTO;
import io.terminus.lshm.product.common.biweekly.request.MdRelationalStoreRequest;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyPageRequest;
import io.terminus.lshm.product.server.domain.biweekly.model.MdRelationalStorePO;
import io.terminus.trantorframework.Response;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 双周MD特殊申请关联门店表 Mapper 接口
 */
@Mapper
public interface MdRelationalStoreMapper extends BaseMapper<MdRelationalStorePO> {


    /**
     * 根据门店id和名店名称模糊查询申请与门店关联表信息
     * */
    List<MdRelationalStoreTO> selectRelationalStoreList(@Param("ew") MdRelationalStoreRequest request);


    /**
     * 根据关联id，查询出 申请信息关联的门店信息
     * */
    List<MdRelationalStoreTO> selectRelationalStoreListByIds( @Param("ew") MdSpecialApplyPageRequest request);

    /***
     *将附门店信息存入门店关联表
     */
    Boolean addMdRelationalStore(@Param("storeList") List<MdRelationalStoreTO> storeList);



    /**
     * 根据门店关联主表id，查询出申请信息关联的门店信息
     * */
    List<MdRelationalStoreTO> selectMdRelationalStoreList(@Param("id") Long id);

    /**
     * 逻辑删除附件信息集合update语句
     * */
    Boolean updateMdRelationalStoreInfo(@Param("storeList") List<MdRelationalStoreTO> storeList);


}
