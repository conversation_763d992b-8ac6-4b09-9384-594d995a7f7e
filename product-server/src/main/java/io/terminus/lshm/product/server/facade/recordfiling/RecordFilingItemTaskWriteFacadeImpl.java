package io.terminus.lshm.product.server.facade.recordfiling;

import io.terminus.lshm.product.common.recordfiling.request.*;
import io.terminus.lshm.product.facade.recordfiling.RecordFilingItemTaskWriteFacade;
import io.terminus.lshm.product.server.domain.recordFiling.service.write.RecordFilingItemTaskWriteService;
import io.terminus.lshm.product.server.external.srm.SrmAdapter;
import io.terminus.trantorframework.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@RestController
public class RecordFilingItemTaskWriteFacadeImpl implements RecordFilingItemTaskWriteFacade {
    @Resource
    private RecordFilingItemTaskWriteService recordFilingItemTaskWriteService;
    @Resource
    private SrmAdapter srmAdapter;


    @Override
    public Response<Boolean> createSrmTask(CreateSrmTaskRequest request) {
        Boolean result = srmAdapter.createSrmTask(request.getSupplierCode(), request.getSupplierName());
        return Response.ok(result);
    }


    @Override
    public Response<Long> saveRecordFilingItemTask(RecordFilingItemTaskRequest request) {
        Long id = recordFilingItemTaskWriteService.saveRecordFilingItemTask(request);
        return Response.ok(id);
    }

    @Override
    public Response<Boolean> batchCreateRecordFilingItemTask(List<BatchRecordFilingItemTaskRequest> request) {
        Boolean result = recordFilingItemTaskWriteService.batchCreateRecordFilingItemTask(request);
        return Response.ok(result);
    }

    @Override
    public Response<Long> submit(RecordFilingItemTaskRequest request) {
        Long id = recordFilingItemTaskWriteService.submit(request);
        return Response.ok(id);
    }

    @Override
    public Response<Boolean> audit(RecordFilingTaskAuditRequest request) {
        Boolean result = recordFilingItemTaskWriteService.audit(request);
        return Response.ok(result);
    }

    @Override
    public Response<Boolean> draftBatchSubmit(DraftBatchSubmitRequest request) {
        return Response.ok(recordFilingItemTaskWriteService.draftBatchSubmit(request.getDraftIdList()));
    }

}