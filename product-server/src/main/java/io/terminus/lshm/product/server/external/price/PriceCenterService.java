package io.terminus.lshm.product.server.external.price;

import com.google.common.collect.Lists;
import groovy.lang.Tuple3;
import groovy.util.logging.Slf4j;
import io.terminus.lshm.item.common.bean.model.InnerMeasureUnitItemTO;
import io.terminus.lshm.price.common.dict.AdjustmentSource;
import io.terminus.lshm.price.common.dict.InnerOrderTypeEnum;
import io.terminus.lshm.price.common.dto.InnerPriceAdjustmentOrderItemDTO;
import io.terminus.lshm.price.common.request.InnerPriceAdjustOrderRequest;
import io.terminus.lshm.product.server.converter.ItemBaseDataConverter;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustCategoryPricePO;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustItemPO;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustPricePO;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustStorePO;
import io.terminus.lshm.product.server.enums.AdjustTypeEnum;
import io.terminus.lshm.product.server.external.CommDualSystemAdapter;
import io.terminus.lshm.product.server.external.RouterEnums;
import io.terminus.lshm.product.server.external.SystemBrandEnums;
import io.terminus.lshm.product.server.external.item.ItemMeasureService;
import io.terminus.lshm.product.server.factory.handler.dto.AdjustPriceHandlerDTO;
import io.terminus.lshm.product.util.Assert;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/23
 * @Version 1.0
 */
@lombok.extern.slf4j.Slf4j
@Service
@RequiredArgsConstructor
@Slf4j
public class PriceCenterService {

    private final ItemMeasureService itemMeasureService;
    private final ItemBaseDataConverter itemBaseDataConverter;

//    private final InnerAdjustPriceWriteFacade innerAdjustPriceWriteFacade;

    private final CommDualSystemAdapter commDualSystemAdapter;

    public List<Tuple3<Long, String, String>> createOrder(AdjustPriceHandlerDTO adjustPriceHandlerDTO) {
        Map<String, InnerMeasureUnitItemTO> stringTuple2Map = itemMeasureService.mapMeasureUnitItem(itemBaseDataConverter.reqItemUnitName(adjustPriceHandlerDTO.getAdjustItemPOList()));
        List<AdjustCategoryPricePO> categoryPricePOList = adjustPriceHandlerDTO.getCategoryPricePOList();
        List<Tuple3<Long, String, String>> list = new ArrayList<>();
        for (AdjustCategoryPricePO category : categoryPricePOList) {
            InnerPriceAdjustOrderRequest req = this.initConvertAdjustOrder(adjustPriceHandlerDTO,category,stringTuple2Map);
            if(Objects.isNull(req)){
                continue;
            }
            String orderId = switchCreateOrder(adjustPriceHandlerDTO.getAdjustPricePO().getBrandId(), req);
            String key;
            if (Objects.nonNull(adjustPriceHandlerDTO.getAdjustPricePO().getFormType()) && adjustPriceHandlerDTO.getAdjustPricePO().getFormType() == 2) {
                key = AdjustTypeEnum.RETAIL_ORDER.getKey();
            }else if(Objects.nonNull(category.getCategory())) {
                key = Objects.requireNonNull(AdjustTypeEnum.getByCode(category.getCategory())).getKey();
            }else {
                throw new BusinessException("调价类型不存在: " + adjustPriceHandlerDTO.getAdjustPricePO().getFormType());
            }
            list.add(Tuple3.tuple(category.getId(), orderId, key));
        }
        return list;
    }

    private String switchCreateOrder(Long brandId, InnerPriceAdjustOrderRequest req) {
        String router = SystemBrandEnums.getByBrandId(brandId).getRouter();
        return Assert.getResult(commDualSystemAdapter.doSendOpen(req, RouterEnums.PRICE_CENTER_ADJUST_ORDER, router));
    }

    private InnerOrderTypeEnum switchTypeEnum(Integer category) {
        switch (category) {
            case 1:// 1配送价
                return InnerOrderTypeEnum.DELIVERY;
            case 2: //2零售价
                return InnerOrderTypeEnum.SALE;
            case 3: //3档案价
                return InnerOrderTypeEnum.ARCHIVES;
            default:
                throw new IllegalStateException("Unexpected value: " + category);
        }
    }


    private InnerPriceAdjustOrderRequest initConvertAdjustOrder(AdjustPriceHandlerDTO adjustPriceHandlerDTO, AdjustCategoryPricePO category, Map<String, InnerMeasureUnitItemTO> stringTuple2Map) {
        AdjustPricePO adjustPricePO = adjustPriceHandlerDTO.getAdjustPricePO();
        InnerPriceAdjustOrderRequest innerPriceAdjustOrderRequest = new InnerPriceAdjustOrderRequest();
        innerPriceAdjustOrderRequest.setRequestUUID(UUID.randomUUID().toString());
        innerPriceAdjustOrderRequest.setEffectStartTime(category.getDeliveryTimeStart());
        innerPriceAdjustOrderRequest.setEffectEndTime(category.getDeliveryTimeEnd());
        innerPriceAdjustOrderRequest.setAdjustReason(adjustPricePO.getReason());

        List<Long> storeIds = adjustPriceHandlerDTO.getStorePOList().stream().map(AdjustStorePO::getStoreId).collect(Collectors.toList());

        innerPriceAdjustOrderRequest.setAppStoreIds(storeIds);
        InnerOrderTypeEnum typeEnum;
        if (Objects.nonNull(adjustPricePO.getFormType()) && adjustPricePO.getFormType() == 2) {
            typeEnum = InnerOrderTypeEnum.SALE;
            innerPriceAdjustOrderRequest.setItemDTOS(this.initConvertItem(adjustPriceHandlerDTO.getAdjustItemPOList(),stringTuple2Map, typeEnum, false));
        }else if(Objects.nonNull(category.getCategory())) {
             typeEnum = this.switchTypeEnum(category.getCategory());
             if (InnerOrderTypeEnum.ARCHIVES.equals(typeEnum)){
                innerPriceAdjustOrderRequest.setItemDTOS(this.initConvertItem(adjustPriceHandlerDTO.getAdjustItemPOList(),stringTuple2Map,typeEnum,true));
             }else {
                 innerPriceAdjustOrderRequest.setItemDTOS(this.initConvertItem(adjustPriceHandlerDTO.getAdjustItemPOList(),stringTuple2Map, typeEnum, false));
             }
        }else {
            throw new BusinessException("调价类型不存在: " + adjustPricePO.getFormType());
        }
        if(CollectionUtils.isEmpty(innerPriceAdjustOrderRequest.getItemDTOS())){
            log.info("innerPriceAdjustOrderRequest.getItemDTOS() is null");
            return null;
        }
        innerPriceAdjustOrderRequest.setType(typeEnum);
//        innerPriceAdjustOrderRequest.setAddWay();
        innerPriceAdjustOrderRequest.setIsAutoAudit(Boolean.TRUE);
        InnerPriceAdjustOrderRequest.UserInfo userInfo = new InnerPriceAdjustOrderRequest.UserInfo();
        userInfo.setId(ServerContext.getUserId());
        userInfo.setName(ServerContext.getUserNickName());
        innerPriceAdjustOrderRequest.setAuditor(userInfo);

        InnerPriceAdjustOrderRequest.UserInfo createUser = new InnerPriceAdjustOrderRequest.UserInfo();
        createUser.setId(adjustPricePO.getCreatedBy());
        createUser.setName(adjustPricePO.getCreatedName());
        innerPriceAdjustOrderRequest.setCreator(createUser);

        innerPriceAdjustOrderRequest.setSourceCode(adjustPricePO.getAdjustCode());
        innerPriceAdjustOrderRequest.setSourceType(AdjustmentSource.PRICE_ADJUSTMENT_PROCESS);
        String expiredPrice ="ITEM";
        if (Objects.nonNull(category.getEffectiveType()) && category.getEffectiveType() == 1) {
            expiredPrice = "CREATED";
        }
        innerPriceAdjustOrderRequest.setExpiredPrice(expiredPrice);

        return innerPriceAdjustOrderRequest;

    }


    private List<InnerPriceAdjustmentOrderItemDTO> initConvertItem(List<AdjustItemPO> adjustItemPOList, Map<String, InnerMeasureUnitItemTO> stringTuple2Map, InnerOrderTypeEnum typeEnum, boolean flag) {
        List<InnerPriceAdjustmentOrderItemDTO> innerPriceAdjustmentOrderItemDTOlist=Lists.newArrayList();
        for (AdjustItemPO adjustItemPO :adjustItemPOList) {
            InnerPriceAdjustmentOrderItemDTO innerPriceAdjustmentOrderItemDTO = convertFromAdjustItemPO(typeEnum,flag, adjustItemPO, getInnerMeasureUnitItemTO(stringTuple2Map, adjustItemPO).getId());
            if(Objects.nonNull(innerPriceAdjustmentOrderItemDTO)){
                innerPriceAdjustmentOrderItemDTOlist.add(innerPriceAdjustmentOrderItemDTO);
            }
        }
        return innerPriceAdjustmentOrderItemDTOlist;
    }

    private static InnerMeasureUnitItemTO getInnerMeasureUnitItemTO(Map<String, InnerMeasureUnitItemTO> stringTuple2Map, AdjustItemPO adjustItemPO) {
        String itemUnitName = adjustItemPO.getItemUnitName();
        if (!StringUtils.hasText(adjustItemPO.getItemUnitName())) {
            itemUnitName = adjustItemPO.getSaleItemUnitName();
        }
        InnerMeasureUnitItemTO innerMeasureUnitItemTO = stringTuple2Map.get(adjustItemPO.getItemCode() + "_" +itemUnitName);
        if(Objects.isNull(innerMeasureUnitItemTO)){
            throw new BusinessException("商品单位不存在: " + adjustItemPO.getItemCode() + ":" + itemUnitName);
        }
        return innerMeasureUnitItemTO;
    }

    private InnerPriceAdjustmentOrderItemDTO convertFromAdjustItemPO(InnerOrderTypeEnum typeEnum, boolean flag, AdjustItemPO adjustItemPO, Long measureId) {
        InnerPriceAdjustmentOrderItemDTO innerPriceAdjustmentOrderItemDTO = new InnerPriceAdjustmentOrderItemDTO();
        innerPriceAdjustmentOrderItemDTO.setMeasureId(measureId);
        if(flag){
            if (Objects.isNull(adjustItemPO.getItemArchiveRetailPriceNew()) || BigDecimal.ZERO.compareTo(adjustItemPO.getItemArchiveRetailPriceNew()) == 0) {
                return null;
            }
            innerPriceAdjustmentOrderItemDTO.setPrice(adjustItemPO.getItemArchiveRetailPriceNew());
            innerPriceAdjustmentOrderItemDTO.setMemberPrice(adjustItemPO.getItemArchiveMemberPriceNew());
            innerPriceAdjustmentOrderItemDTO.setDeliveryPrice(adjustItemPO.getItemArchiveDeliveryPriceNew());
        }else {
            if (typeEnum.equals(InnerOrderTypeEnum.SALE) && (Objects.isNull(adjustItemPO.getItemRetailPriceNew()) || BigDecimal.ZERO.compareTo(adjustItemPO.getItemRetailPriceNew()) == 0)) {
                return null;
            }
            if (typeEnum.equals(InnerOrderTypeEnum.DELIVERY) && (Objects.isNull(adjustItemPO.getItemDeliveryPriceNew())) || BigDecimal.ZERO.compareTo(adjustItemPO.getItemDeliveryPriceNew()) == 0) {
                return null;
            }
            innerPriceAdjustmentOrderItemDTO.setPrice(adjustItemPO.getItemRetailPriceNew());
            innerPriceAdjustmentOrderItemDTO.setMemberPrice(adjustItemPO.getItemMemberPriceNew());
            innerPriceAdjustmentOrderItemDTO.setDeliveryPrice(adjustItemPO.getItemDeliveryPriceNew());
        }
        innerPriceAdjustmentOrderItemDTO.setItemPrice(adjustItemPO.getItemArchiveRetailPrice());
        innerPriceAdjustmentOrderItemDTO.setItemMemberPrice(adjustItemPO.getItemArchiveMemberPrice());
        innerPriceAdjustmentOrderItemDTO.setItemDeliveryPrice(adjustItemPO.getItemArchiveDeliveryPrice());
        return innerPriceAdjustmentOrderItemDTO;

    }






}
