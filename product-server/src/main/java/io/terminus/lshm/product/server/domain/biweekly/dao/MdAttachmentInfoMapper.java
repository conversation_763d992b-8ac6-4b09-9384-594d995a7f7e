package io.terminus.lshm.product.server.domain.biweekly.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.terminus.lshm.product.common.biweekly.dto.MdAttachmentInfoTO;
import io.terminus.lshm.product.server.domain.biweekly.model.MdAttachmentInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 双周MD附件信息表 Mapper 接口
 * <AUTHOR>
 */
@Mapper
public interface MdAttachmentInfoMapper extends BaseMapper<MdAttachmentInfoPO> {

        /**
         * 根据申请表id查询出对应的附件信息
         * */
        List<MdAttachmentInfoTO> selectMdAttachmentInfoList(@Param("id") Long id);


        /***
         *将附件信息存入附件信息表
         */
       Boolean addMdAttachmentInfo(@Param("attachmentList") List<MdAttachmentInfoTO> attachmentList);



       /**
        * 逻辑删除附件信息集合update语句
        * */
       Boolean updateMdAttachmentInfo(@Param("attachmentList") List<MdAttachmentInfoTO> attachmentList);


}
