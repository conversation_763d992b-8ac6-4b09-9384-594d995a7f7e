package io.terminus.lshm.product.server.domain.biweekly.service.read.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.server.converter.biweeklyMd.MdAuditRecordConverter;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdAuditRecordMapper;
import io.terminus.lshm.product.server.domain.biweekly.model.MdAuditRecordPO;
import io.terminus.lshm.product.server.domain.biweekly.model.MdSpecialApplyPO;
import io.terminus.lshm.product.server.domain.biweekly.service.read.MdAuditRecordReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 月度MD审批记录查询服务实现类
 * <AUTHOR>
 * @date 2025/7/9 11:25
 */
@Service
public class MdAuditRecordReadServiceImpl extends ServiceImpl<MdAuditRecordMapper, MdAuditRecordPO> implements MdAuditRecordReadService {

    @Autowired
    private MdAuditRecordConverter mdAuditRecordConverter;

    @Override
    public List<MdAuditRecordTO> getMdAuditRecordListBySpecialApplyId(Long specialApplyId) {
        LambdaQueryWrapper<MdAuditRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdAuditRecordPO::getSpecialApplyId, specialApplyId);
        List<MdAuditRecordPO> mdAuditRecordPOList = this.baseMapper.selectList(queryWrapper);
        return mdAuditRecordConverter.listP2T(mdAuditRecordPOList);
    }
}
