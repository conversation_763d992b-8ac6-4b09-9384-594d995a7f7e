package io.terminus.lshm.product.server.domain.biweekly.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.terminus.lshm.flow.request.FlowStartWithBizRequest;
import io.terminus.lshm.flow.response.FlowStartWithBizResponse;
import io.terminus.lshm.product.common.biweekly.dto.MdAttachmentInfoTO;
import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.common.biweekly.dto.MdRelationalStoreTO;
import io.terminus.lshm.product.common.biweekly.request.MdSpecialApplyByRequest;
import io.terminus.lshm.product.common.enums.AuditStatusEnum;
import io.terminus.lshm.product.common.enums.WorkFlowCodeEnum;
import io.terminus.lshm.product.common.enums.biweeklyMd.BiweeklyMdFlowNodeEnum;
import io.terminus.lshm.product.common.offshelf.dto.EmployeeUsersTO;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdAttachmentInfoMapper;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdAuditRecordMapper;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdRelationalStoreMapper;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdSpecialApplyMapper;
import io.terminus.lshm.product.server.domain.biweekly.model.MdSpecialApplyPO;
import io.terminus.lshm.product.server.domain.biweekly.service.write.MdSpecialApplyWriteService;
import io.terminus.lshm.product.server.external.user.EmployeeService;
import io.terminus.lshm.product.server.facade.flow.FlowBpmWriteFacadeImpl;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.lshm.server.common.ServerUserInfo;
import io.terminus.trantorframework.Response;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MdSpecialApplyWriteServiceImpl extends ServiceImpl<MdSpecialApplyMapper, MdSpecialApplyPO> implements MdSpecialApplyWriteService {


    @Resource
    private MdSpecialApplyMapper mdSpecialApplyMapper;

    @Resource
    private MdRelationalStoreMapper mdRelationalStoreMapper;

    @Resource
    private MdAttachmentInfoMapper mdAttachmentInfoMapper;

    @Autowired
    private EmployeeService employeeService;

    @Resource
    private MdAuditRecordMapper  mdAuditRecordMapper;

    @Autowired
    private FlowBpmWriteFacadeImpl flowBpmWriteFacade;


    @Override
    public void updateWorkFlow(MdSpecialApplyPO mdSpecialApplyPO) {
        //创建人
        ServerUserInfo userInfo = ServerContext.getUserInfo();
        //设置创建人名称
        if (!Objects.isNull(userInfo)){
            mdSpecialApplyPO.setUpdatedBy(userInfo.getUserId());
            mdSpecialApplyPO.setUpdatedName(userInfo.getNickName());
        }
        boolean updateById = this.updateById(mdSpecialApplyPO);
        if (!updateById){
            throw new BusinessException("更新流程失败");
        }
    }


    @Override
    public Response<Boolean> addMdSpecialApply(FlowApplyBizRequest<MdSpecialApplyByRequest> flowApplyBizRequest) {
        MdSpecialApplyByRequest request = flowApplyBizRequest.getBizForm();
        //排序计算
        //排序sort的最大值
        Long sort = mdSpecialApplyMapper.dataCalculation(request);
        if (sort == null) {
            //如果为空，说明没有数据，这是第一条，则排序值为0
            sort = 0L;
        }
        //如果不为空，则排序值为最大值+1
        sort = sort + 1L;
        //获取登录人信息
        ServerUserInfo userInfo = ServerContext.getUserInfo();
        MdSpecialApplyPO mdSpecialApplyPO = new MdSpecialApplyPO();
            //todo 调用BPM流程，调用成功后会返回一个流程实例id，这个id存入主表中去，后续审核通过这个id去操作
            FlowStartWithBizRequest flowStartWithBizRequest = new FlowStartWithBizRequest();
            flowStartWithBizRequest.setWorkflowCode(WorkFlowCodeEnum.MD_SPECIAL_APPLY.getCode());
            flowStartWithBizRequest.setData(flowApplyBizRequest.getBizForm());
            FlowStartWithBizResponse flowStartWithBizResponse = flowBpmWriteFacade.startWithBiz(flowStartWithBizRequest);
            log.info("flowStartWithBizResponse：{}", flowStartWithBizResponse);
            if (Objects.isNull(flowStartWithBizResponse)) {
                throw new BusinessException("调用bpm失败！");
            }
            //流程实例id
            mdSpecialApplyPO.setWorkflowInstanceId(flowStartWithBizResponse.getWorkflowInstanceId());
            //计算门店数
            mdSpecialApplyPO.setStoreRelationQuantity(request.getStoreList().size());
            //申请月份
            mdSpecialApplyPO.setApplyMonthTime(new Date());
            //创建时间
            mdSpecialApplyPO.setCreatedAt(new Date());
            //更新时间
            mdSpecialApplyPO.setUpdatedAt(new Date());
            //备注
            mdSpecialApplyPO.setRemark(request.getRemark());
            //申请理由
            mdSpecialApplyPO.setApplyReason(request.getApplyReason());
            //报备周期
            mdSpecialApplyPO.setFilingCycle(request.getFilingCycle());
            //排序
            mdSpecialApplyPO.setSort(Math.toIntExact(sort));
            //获取用户信息
            EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
            if (Objects.isNull(employeeByUserId)) {
                throw new BusinessException("员工信息不存在");
            }
            //标签生成（ 类型+发起人+时间）
            String title = "月度MD特殊申请" + employeeByUserId.getEmployeeName() + LocalDate.now();
            mdSpecialApplyPO.setTitle(title);
            mdSpecialApplyPO.setCreatedBy(employeeByUserId.getUserId());
            mdSpecialApplyPO.setCreatedName(employeeByUserId.getEmployeeName());
            //更新人updated_by
            mdSpecialApplyPO.setUpdatedBy(employeeByUserId.getUserId());
            mdSpecialApplyPO.setUpdatedName(employeeByUserId.getEmployeeName());
            //创建人手机号
            mdSpecialApplyPO.setCreatedPhone(employeeByUserId.getEmployeePhone());
            //品牌id
            mdSpecialApplyPO.setBrandId(request.getBrandId());
            //审批状态
            mdSpecialApplyPO.setAuditStatus(AuditStatusEnum.UNDER_REVIEW.getCode());
            // 审批流程状态
            mdSpecialApplyPO.setAuditFlowStatus(BiweeklyMdFlowNodeEnum.DEPARTMENT_DIRECTOR_APPROVAL.getCode());
            //主表基础信息添加
            this.save(mdSpecialApplyPO);
            log.info("specialApplyId：{}", mdSpecialApplyPO.getId());
            //返回的主表id
            Long specialApplyId = mdSpecialApplyPO.getId();
            // 附件信息中的新建人信息和主表iD
            enrichAttachmentsWithMetadata(request.getAttachmentList(),request.getStoreList(),specialApplyId,employeeByUserId);
            //附件信息集合
            if (!request.getAttachmentList().isEmpty()){
                mdAttachmentInfoMapper.addMdAttachmentInfo(request.getAttachmentList());
            }
            //门店信集合息
            if (!request.getStoreList().isEmpty()){
                mdRelationalStoreMapper.addMdRelationalStore(request.getStoreList());
            }
            //将兴建数据存入审批记录
            MdAuditRecordTO mdAuditRecordTO = new MdAuditRecordTO();
            //流程id
            mdAuditRecordTO.setWorkflowInstanceId(flowStartWithBizResponse.getWorkflowInstanceId());
            //主表id
            mdAuditRecordTO.setSpecialApplyId(specialApplyId);
            //审批状态
            mdAuditRecordTO.setAuditStatus(AuditStatusEnum.UNDER_REVIEW.getCode());
            //审批流程节点
            mdAuditRecordTO.setSpecialApply(BiweeklyMdFlowNodeEnum.BIWEEKLY_MD_START.getCode());
            //审批人
            mdAuditRecordTO.setAuditor(BiweeklyMdFlowNodeEnum.BIWEEKLY_MD_START.getCode());
            //去审批人
            mdAuditRecordTO.setToAuditor(BiweeklyMdFlowNodeEnum.DEPARTMENT_DIRECTOR_APPROVAL.getCode());
            //审批人姓名
            mdAuditRecordTO.setAuditorName(employeeByUserId.getEmployeeName());
            //新建人
            mdAuditRecordTO.setCreatedBys(String.valueOf(employeeByUserId.getUserId()));
            //新建人昵称
            mdAuditRecordTO.setCreatedName(employeeByUserId.getEmployeeName());
            //新建时间
            mdAuditRecordTO.setCreatedAt(new Date());
            //更改人
            mdAuditRecordTO.setUpdatedBys(String.valueOf(employeeByUserId.getUserId()));
            //更改时间
            mdAuditRecordTO.setUpdatedAt(new Date());
            //存入数据
            mdAuditRecordMapper.addMdSpecialApply(mdAuditRecordTO);
            //返回
            return Response.ok();
        }

    @Override
    public Response<Boolean> updateMdSpecialApply(FlowApplyBizRequest<MdSpecialApplyByRequest> flowApplyBizRequest) {
        MdSpecialApplyByRequest request = flowApplyBizRequest.getBizForm();
        //获取用户信息
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId();
        if (Objects.isNull(employeeByUserId)) {
            throw new BusinessException("员工信息不存在");
        }
        MdSpecialApplyPO mdSpecialApplyPO = new MdSpecialApplyPO();
        //更改申请理由
        mdSpecialApplyPO.setApplyReason(request.getApplyReason());
        //更改备注
        mdSpecialApplyPO.setRemark(request.getRemark());
        //更改品牌
        mdSpecialApplyPO.setBrandId(request.getBrandId());
        //主表id
        mdSpecialApplyPO.setId(request.getId());
        //跟新人信息
        mdSpecialApplyPO.setUpdatedBy(employeeByUserId.getUserId());
        mdSpecialApplyPO.setUpdatedName(employeeByUserId.getEmployeeName());
        mdSpecialApplyPO.setUpdatedAt(new Date());
            //获取主表id
        Long specialApplyId = request.getId();
            //根据主表id查询出附件信息
        List<MdAttachmentInfoTO>  attachment = mdAttachmentInfoMapper.selectMdAttachmentInfoList(specialApplyId);
        //对比两者，相同的不用重新存，不同的存一次，逻辑删除新传入的附件中没有的，组成新的附件集合
        // 对比新旧附件信息，准备插入和删除的集合
        List<MdAttachmentInfoTO> attachmentsToInsert = new ArrayList<>();
        List<MdAttachmentInfoTO> attachmentsToDelete = new ArrayList<>();
        // 创建已存在附件的唯一标识集合（special_apply_id, file_url, file_name）
        Set<String> existingAttachmentKeys = attachment.stream()
                .map(att -> String.format("%d_%s_%s", att.getSpecialApplyId(), att.getFileUrl(), att.getFileName()))
                .collect(Collectors.toSet());
        // 遍历新传入的附件信息
        for (MdAttachmentInfoTO newAttachment : request.getAttachmentList()) {
            String newKey = String.format("%d_%s_%s", newAttachment.getSpecialApplyId(), newAttachment.getFileUrl(), newAttachment.getFileName());
            if (existingAttachmentKeys.contains(newKey)) {
                // 如果已存在，则不再处理
                continue;
            } else {
                // 如果不存在，则添加到插入集合
                attachmentsToInsert.add(newAttachment);
            }
        }
        // 准备删除的逻辑：将不在新传入集合中的已存在附件标记为删除
        // 创建新传入附件的唯一标识集合
        Set<String> newAttachmentKeys = request.getAttachmentList().stream()
                .map(newAttachment -> String.format("%d_%s_%s", newAttachment.getSpecialApplyId(), newAttachment.getFileUrl(), newAttachment.getFileName()))
                .collect(Collectors.toSet());
        for (MdAttachmentInfoTO existingAttachment : attachment) {
            String existingKey = String.format("%d_%s_%s", existingAttachment.getSpecialApplyId(), existingAttachment.getFileUrl(), existingAttachment.getFileName());
            if (!newAttachmentKeys.contains(existingKey)) {
                // 如果不存在于新集合中，则添加到删除集合
                attachmentsToDelete.add(existingAttachment);
            }
        }
        //从新存储附件信息
        if (!attachmentsToInsert.isEmpty()){
            mdAttachmentInfoMapper.addMdAttachmentInfo(attachmentsToInsert);
        }
        //逻辑删除之前的附件信息
        if (!attachmentsToDelete.isEmpty()){
            mdAttachmentInfoMapper.updateMdAttachmentInfo(attachmentsToDelete);
        }
        //主表门店关联信息
        List<MdRelationalStoreTO> relationalStore = mdRelationalStoreMapper.selectMdRelationalStoreList(specialApplyId);
        // 对比新旧附件信息，准备插入和删除的集合
        List<MdRelationalStoreTO> relationalStoreInsert = new ArrayList<>();
        List<MdRelationalStoreTO> relationalStoreDelete = new ArrayList<>();
        // 创建已存在附件的唯一标识集合（special_apply_id, store_id）
        Set<String> existingrelationalStoreKeys = relationalStore.stream()
                .map(re -> String.format("%d_%d",
                        re.getSpecialApplyId() != null ? re.getSpecialApplyId() : -1,
                        re.getStoreId() != null ? re.getStoreId() : -1))
                .collect(Collectors.toSet());
        // 遍历新传入的附件信息
        for (MdRelationalStoreTO newrelationalStore : request.getStoreList()) {
            String newKey = String.format("%d_%d", newrelationalStore.getSpecialApplyId(), newrelationalStore.getStoreId());
            if (existingrelationalStoreKeys.contains(newKey)) {
                // 如果已存在，则不再处理
                continue;
            } else {
                // 如果不存在，则添加到插入集合
                relationalStoreInsert.add(newrelationalStore);
            }
        }
        // 准备删除的逻辑：将不在新传入集合中的已存在门店标记为删除
        // 创建新传入附件的唯一标识集合
        Set<String> newrelationalStoreKeys = request.getStoreList().stream()
                .map(newStore -> String.format("%d_%d", newStore.getSpecialApplyId(), newStore.getStoreId()))
                .collect(Collectors.toSet());
        for (MdRelationalStoreTO existingRelationalStore : relationalStore) {
            String existingKey = String.format("%d_%d", existingRelationalStore.getSpecialApplyId(), existingRelationalStore.getStoreId());
            if (!newrelationalStoreKeys.contains(existingKey)) {
                // 如果不存在于新集合中，则添加到删除集合
                relationalStoreDelete.add(existingRelationalStore);
            }
        }
        // 从新存储门店信息
        if (!relationalStoreInsert.isEmpty()){
            mdRelationalStoreMapper.addMdRelationalStore(relationalStoreInsert);
        }
        if (!relationalStoreDelete.isEmpty()){
            // 逻辑删除之前的门店信息
            mdRelationalStoreMapper.updateMdRelationalStoreInfo(relationalStoreDelete);
        }
        //计算门店数
        mdSpecialApplyPO.setStoreRelationQuantity(request.getStoreList().size());
        //更改主表信息
        mdSpecialApplyMapper.updateMdSpecialApply(mdSpecialApplyPO);
        return Response.ok();
    }


    /**
     * 为附件信息赋值 createdBy, createdName, createdAt,
     */
    private void enrichAttachmentsWithMetadata(List<MdAttachmentInfoTO> attachmentList,List<MdRelationalStoreTO> storeList, Long specialApplyId, EmployeeUsersTO employeeByUserId) {
        if (isEmpty(attachmentList) && isEmpty(storeList))
        {
            log.warn("Attachment list is empty or null");
            return;
        }

        Date now = new Date();
        int index = 0;
        //附件
        for (int i = 0; i < attachmentList.size(); i++) {
            MdAttachmentInfoTO attachment = attachmentList.get(i);
            if (attachment == null) {
                log.info("Attachment at index {} is null", i);
                continue;
            }
            attachment.setFileName(attachment.getFileName());
            attachment.setSpecialApplyId(specialApplyId);
            attachment.setCreatedAt(now);
            attachment.setCreatedBys(String.valueOf(employeeByUserId.getUserId()));
            attachment.setCreatedName(employeeByUserId.getEmployeeName());
            attachment.setUpdatedAt(now);
        }
        //门店
        for (int i = 0; i < storeList.size(); i++) {
            MdRelationalStoreTO relationalStore = storeList.get(i);
            if (relationalStore == null) {
                log.info("Attachment at index {} is null", i);
                continue;
            }
            relationalStore.setStoreId(relationalStore.getStoreId());
            relationalStore.setSpecialApplyId(specialApplyId);
            relationalStore.setCreatedAt(now);
            relationalStore.setCreatedBys(String.valueOf(employeeByUserId.getUserId()));
            relationalStore.setCreatedName(employeeByUserId.getEmployeeName());
            relationalStore.setUpdatedAt(now);
        }
    }


    private boolean isEmpty(List<?> list) {
        return list == null || list.isEmpty();
    }
}
