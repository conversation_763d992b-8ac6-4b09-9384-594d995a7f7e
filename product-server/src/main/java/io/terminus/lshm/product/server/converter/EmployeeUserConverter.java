package io.terminus.lshm.product.server.converter;

import io.terminus.gaia.organization.api.response.FindEmployeeListByJobCodeResponse;
import io.terminus.gaia.organization.api.response.FindEmployeePageResponse;
import io.terminus.lshm.product.common.offshelf.response.EmployeeUserResponse;
import io.terminus.lshm.product.common.recordfiling.dto.EmployeeTO;
import io.terminus.lshm.product.server.external.user.model.EmployeeUserDTO;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/2
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface EmployeeUserConverter {
    List<EmployeeUserResponse> listp2res(List<FindEmployeeListByJobCodeResponse> collect);


    List<EmployeeUserResponse> listEmployee(List<EmployeeUserDTO> list);


    default EmployeeUserResponse toEmployee(EmployeeUserDTO dto){
        EmployeeUserResponse employeeUserResponse = new EmployeeUserResponse();
        employeeUserResponse.setUserId(dto.getUserId());
        employeeUserResponse.setEmployeeId(dto.getUserId());
        employeeUserResponse.setEmployeeCode(dto.getEmployeeCode());
        employeeUserResponse.setEmployeeName(dto.getUserName());
        employeeUserResponse.setEmployeePhone(dto.getEmployeeCode());
        return employeeUserResponse;
    }
    default List<EmployeeTO> employeeP2T(List<FindEmployeePageResponse> data) {
        EmployeeTO employeeTO;
        List<EmployeeTO> employeeTOList = new ArrayList<>();
        for (FindEmployeePageResponse employee : data) {
            employeeTO = new EmployeeTO();
            employeeTO.setId(employee.getId());
            employeeTO.setEmployeeName(employee.getEmployeeName());
            employeeTO.setEmployeeCode(employee.getEmployeeCode());
            employeeTO.setEmployeeDisplayName("（" + employee.getEmployeeCode() + "）" + employee.getEmployeeName());
            employeeTO.setEmployeePhone(employee.getEmployeePhone());
            employeeTOList.add(employeeTO);
        }
        return employeeTOList;
    }
}
