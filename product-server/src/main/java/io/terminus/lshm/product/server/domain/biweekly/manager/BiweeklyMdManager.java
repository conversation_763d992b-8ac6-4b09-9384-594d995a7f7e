package io.terminus.lshm.product.server.domain.biweekly.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.terminus.common.model.Response;
import io.terminus.gaia.organization.api.facade.InnerEmployeeReadFacade;
import io.terminus.gaia.organization.api.request.FindEmployeeUserInfoByEmployeeCodeListRequest;
import io.terminus.gaia.organization.api.response.FindEmployeeUserInfoByEmployeeCodeListResponse;
import io.terminus.lshm.flow.request.FlowBpmRequest;
import io.terminus.lshm.flow.request.FlowCancelItemRequest;
import io.terminus.lshm.flow.request.FlowItemExtRequest;
import io.terminus.lshm.flow.response.FlowItemApprovalStatusEnum;
import io.terminus.lshm.flow.response.FlowItemExtApiResponse;
import io.terminus.lshm.flow.response.FlowListByInstanceResponse;
import io.terminus.lshm.product.common.bean.request.biweeklyMd.BiweeklyMdReRequest;
import io.terminus.lshm.product.common.bean.request.bpm.FlowCancelRequest;
import io.terminus.lshm.product.common.biweekly.dto.MdAuditRecordTO;
import io.terminus.lshm.product.common.enums.*;
import io.terminus.lshm.product.common.enums.biweeklyMd.BiweeklyMdFlowNodeEnum;
import io.terminus.lshm.product.common.enums.biweeklyMd.BiweeklyMdStateMachineEnum;
import io.terminus.lshm.product.common.flow.dto.MsgInfo;
import io.terminus.lshm.product.common.offshelf.dto.EmployeeUsersTO;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.server.domain.biweekly.model.MdSpecialApplyPO;
import io.terminus.lshm.product.server.domain.biweekly.service.read.MdAuditRecordReadService;
import io.terminus.lshm.product.server.domain.biweekly.service.read.MdSpecialApplyReadService;
import io.terminus.lshm.product.server.domain.biweekly.service.write.MdAuditRecordWriteService;
import io.terminus.lshm.product.server.domain.biweekly.service.write.MdSpecialApplyWriteService;
import io.terminus.lshm.product.server.external.notice.WxSendService;
import io.terminus.lshm.product.server.external.user.EmployeeService;
import io.terminus.lshm.product.server.facade.flow.FlowBpmReadFacadeImpl;
import io.terminus.lshm.product.server.facade.flow.FlowBpmWriteFacadeImpl;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 月度MD 管理类
 * @date 2025/6/19 15:06
 */
@Slf4j
@Service
public class BiweeklyMdManager {

    @Autowired
    private MdSpecialApplyReadService mdSpecialApplyReadService;
    @Autowired
    private FlowBpmWriteFacadeImpl flowBpmWriteFacade;
    @Autowired
    private MdSpecialApplyWriteService mdSpecialApplyWriteService;
    @Autowired
    private MdAuditRecordWriteService mdAuditRecordWriteService;
    @Autowired
    private WxSendService wxSendService;
    @Autowired
    private FlowBpmReadFacadeImpl flowBpmReadFacade;
    @Autowired
    private InnerEmployeeReadFacade innerEmployeeReadFacade;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private MdAuditRecordReadService mdAuditRecordReadService;

    @Transactional(rollbackFor = Exception.class)
    public Boolean audit(FlowApplyBizRequest request) {
        MdSpecialApplyPO mdSpecialApplyPO = mdSpecialApplyReadService.getByWorkflowInstanceId(request.getWorkflowInstanceId());
        if (Objects.isNull(mdSpecialApplyPO)) {
            throw new BusinessException("月度MD特殊申请不存在");
        }
        FlowBpmRequest flowBpmRequest = new FlowBpmRequest();
        flowBpmRequest.setComment(request.getComment());
        flowBpmRequest.setWorkItemId(request.getWorkItemId());
        flowBpmRequest.setSubmitToReject(request.getSubmitToReject());
        flowBpmRequest.setRejectToActivityCode(BiweeklyMdFlowNodeEnum.getBpmCodeByCode(request.getRejectToActivityCode()));
        flowBpmRequest.setWorkflowInstanceId(request.getWorkflowInstanceId());

        //查询任务id 根据当前登录人
        Long userId = ServerContext.getUserId();
        EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId(userId);
        if (Objects.isNull(employeeByUserId)){
            throw new BusinessException("员工信息不存在");
        }
        List<FlowListByInstanceResponse> flowListByInstanceResponses = flowBpmReadFacade.listByInstance(request.getWorkflowInstanceId());
        if (CollUtil.isEmpty(flowListByInstanceResponses)) {
            throw new BusinessException("个人待办任务不存在");
        }

        // 过滤出participantName为当前登录人且approval为UNDO的数据
        Optional<FlowListByInstanceResponse> firstMatch = flowListByInstanceResponses.stream()
            .filter(item -> StrUtil.equals("UNDO",item.getApproval().name()))
            .filter(item -> employeeByUserId.getEmployeeName().equals(item.getParticipantName()))
            .findFirst();

        // 如果找到匹配项，使用匹配项的ID；否则抛出异常
        if (firstMatch.isPresent()) {
            flowBpmRequest.setWorkItemId(firstMatch.get().getId());
        } else {
            throw new BusinessException("未找到待处理的审批任务");
        }
        FlowListByInstanceResponse flowListByInstanceResponse = firstMatch.get();
        Boolean audit = flowBpmWriteFacade.audit(flowBpmRequest);
        if (!audit) {
            throw new BusinessException("审核失败！");
        }
        // 如果是驳回，重新获取流程节点信息
        if (!request.getSubmitToReject()) {
            BiweeklyMdFlowNodeEnum nextNode = BiweeklyMdStateMachineEnum.getNextNode(BiweeklyMdFlowNodeEnum.getByCode(request.getRejectToActivityCode()));
            mdSpecialApplyPO.setAuditFlowStatus(nextNode.getCode());
        }

        MdSpecialApplyPO mdSpecialApplyUpdate = new MdSpecialApplyPO();
        mdSpecialApplyUpdate.setId(mdSpecialApplyPO.getId());
        // 逐级审核至部门负责人该节点bpm会审两次
        if (request.getSubmitToReject() && StrUtil.equals(flowListByInstanceResponse.getActivityCode(), BiweeklyMdFlowNodeEnum.DEPARTMENT_DIRECTOR_APPROVAL.getBpmCode())) {
            Map<String, List<FlowListByInstanceResponse>> listByInstanceResponseMap = flowListByInstanceResponses.stream().collect(Collectors.groupingBy(FlowListByInstanceResponse::getActivityCode));
            List<FlowListByInstanceResponse> flowListByInstanceResponseList = listByInstanceResponseMap.get(BiweeklyMdFlowNodeEnum.DEPARTMENT_DIRECTOR_APPROVAL.getBpmCode());
            if (CollUtil.isNotEmpty(flowListByInstanceResponseList) && flowListByInstanceResponseList.size() == 1) {
                mdSpecialApplyPO.setAuditFlowStatus(BiweeklyMdFlowNodeEnum.BIWEEKLY_MD_START.getCode());
            }
        }
        setMdSpecialApplyStatus(mdSpecialApplyPO, request, mdSpecialApplyUpdate);
        mdSpecialApplyWriteService.updateWorkFlow(mdSpecialApplyUpdate);
        //添加操作记录
        if (!request.getSubmitToReject()) {
            mdAuditRecordWriteService.add(mdSpecialApplyUpdate.getId(), mdSpecialApplyUpdate.getAuditFlowStatus(), request.getComment(), flowListByInstanceResponse.getId());
        }
        //发送企业微信通知
        CompletableFuture.runAsync(() -> {
            flowUp(mdSpecialApplyUpdate.getId(), true);
        });
        return Boolean.TRUE;
    }

    /**
     * 获取流程状态
     *
     * @param request
     * @param mdSpecialApplyPO
     */
    private void setMdSpecialApplyStatus(MdSpecialApplyPO mdSpecialApplyPO, FlowApplyBizRequest request, MdSpecialApplyPO updateMdSpecialApplyPO) {
        //如果是审核通过
        if (request.getSubmitToReject() && StrUtil.isBlank(request.getRejectToActivityCode())) {
            BiweeklyMdFlowNodeEnum nextNode = BiweeklyMdStateMachineEnum.getNextNode(BiweeklyMdFlowNodeEnum.getByCode(mdSpecialApplyPO.getAuditFlowStatus()));
            updateMdSpecialApplyPO.setAuditFlowStatus(nextNode.getCode());
            if (nextNode.equals(BiweeklyMdFlowNodeEnum.COMPLETED)) {
                updateMdSpecialApplyPO.setAuditStatus(AuditStatusEnum.COMPLETED.getCode());
            }
        }
        //如果是审核驳回
        else {
            updateMdSpecialApplyPO.setAuditFlowStatus(mdSpecialApplyPO.getAuditFlowStatus());
            updateMdSpecialApplyPO.setAuditStatus(AuditStatusEnum.REJECTED.getCode());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean flowUp(Long id, Boolean isAudit) {
        //查询月度MD特殊申请数据
        MdSpecialApplyPO mdSpecialApplyPO = mdSpecialApplyReadService.getById(id);
        if (!isAudit) {
            validFlowUp(mdSpecialApplyPO);
        }
        //获取企业微信id
        List<String> enterpriseWechatCountList = getEnterpriseWechatCountList(mdSpecialApplyPO);
        if (CollUtil.isEmpty(enterpriseWechatCountList)) {
            return false;
        }

        List<MsgInfo> sendInfoList = new ArrayList<>();
        MsgInfo msgInfo = new MsgInfo();
        String h5DetailUrl = BusinessTypeEnum.getByCode(BusinessTypeEnum.BIWEEKLY_MD.getCode()).getH5DetailUrl();
        String h5Url = String.format(h5DetailUrl, mdSpecialApplyPO.getId(), mdSpecialApplyPO.getAuditFlowStatus(), mdSpecialApplyPO.getWorkflowInstanceId(), mdSpecialApplyPO.getAuditStatus());
        msgInfo.setUrl(h5Url);

        //取最新的生效时间
        msgInfo.setCreatedBy(mdSpecialApplyPO.getCreatedName());
        msgInfo.setBusinessId(mdSpecialApplyPO.getId().toString());
        msgInfo.setAccountIds(enterpriseWechatCountList);
        log.info("月度MD特殊申请企微消息推送，获取企微ID信息：{}" + enterpriseWechatCountList);
        //月度MD特殊申请流程名称-创建人-日期
        String title = "您有流程待审批: " + mdSpecialApplyPO.getTitle() + "-" + msgInfo.getCreatedBy() + "-" + DateUtil.formatDate(mdSpecialApplyPO.getCreatedAt());
        msgInfo.setTitle(title);
        sendInfoList.add(msgInfo);
        wxSendService.handlerSendWechatAppAuditPendingMsgBiweeklyMdPC(sendInfoList);
        //更新发送时间
        if (!isAudit) {
            MdSpecialApplyPO newMdSpecialApply = new MdSpecialApplyPO();
            newMdSpecialApply.setId(mdSpecialApplyPO.getId());
            newMdSpecialApply.setLastSendTime(new Date());
            mdSpecialApplyWriteService.updateById(newMdSpecialApply);
        }
        return true;
    }

    public void validFlowUp(MdSpecialApplyPO mdSpecialApplyPO) {
        if (Objects.isNull(mdSpecialApplyPO)) {
            throw new BusinessException("月度MD特殊申请不存在");
        }

        if (mdSpecialApplyPO.getAuditStatus().equals(BiweeklyMdFlowNodeEnum.COMPLETED.getCode())) {
            throw new BusinessException("当前月度MD特殊申请状态不允许催办");
        }
        //校验上次发送时间是否超过6小时
        if (ObjectUtil.isNotEmpty(mdSpecialApplyPO.getLastSendTime())) {
            if (DateUtil.between(new Date(), mdSpecialApplyPO.getLastSendTime(), DateUnit.HOUR) < 6) {
                throw new BusinessException("6小时内只能进行一次催办");
            }
        }

    }

    /**
     * 获取企业微信id
     *
     * @param mdSpecialApplyPO
     * @return
     */
    private List<String> getEnterpriseWechatCountList(MdSpecialApplyPO mdSpecialApplyPO) {
        String workflowInstanceId = mdSpecialApplyPO.getWorkflowInstanceId();
        //获取该状态的所有人的工号
        FlowItemExtRequest flowItemExtRequest = new FlowItemExtRequest();
        flowItemExtRequest.setWorkflowInstanceId(workflowInstanceId);
        FlowItemExtApiResponse flowItemExtApiResponse = flowBpmReadFacade.workItemExtListNoUser(flowItemExtRequest);
        if (Objects.isNull(flowItemExtApiResponse)) {
            return null;
        }
        List<FlowItemExtApiResponse.WorkItem> contentList = flowItemExtApiResponse.getContent();
        if (CollUtil.isEmpty(contentList)) {
            return null;
        }

        //获取当前状态未审核的工号
        List<FlowItemExtApiResponse.WorkItem> workItemList = contentList.parallelStream()
                .filter(x -> x.getActivityCode().equals(BiweeklyMdFlowNodeEnum.getBpmCodeByCode(mdSpecialApplyPO.getAuditFlowStatus()))
                        && x.getApproval().equals(FlowItemApprovalStatusEnum.UNDO.getIndex()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(workItemList)) {
            return null;
        }

        //获取所有的工号
        List<String> userCodeList = workItemList.stream()
                .map(FlowItemExtApiResponse.WorkItem::getOriginator)
                .collect(Collectors.toList());
        FindEmployeeUserInfoByEmployeeCodeListRequest findEmployeeUserInfoByEmployeeCodeListRequest = new FindEmployeeUserInfoByEmployeeCodeListRequest();
        findEmployeeUserInfoByEmployeeCodeListRequest.setEmployeeCodeList(userCodeList);

        //获取用户企业微信id
        Response<List<FindEmployeeUserInfoByEmployeeCodeListResponse>> employeeUserInfoByEmployeeCodeList = innerEmployeeReadFacade.findEmployeeUserInfoByEmployeeCodeList(findEmployeeUserInfoByEmployeeCodeListRequest);
        if (!employeeUserInfoByEmployeeCodeList.isSuccess()) {
            return null;
        }
        List<FindEmployeeUserInfoByEmployeeCodeListResponse> result = employeeUserInfoByEmployeeCodeList.getResult();
        if (CollUtil.isEmpty(result)) {
            return null;
        }

        //获取所有的企业微信账号
        List<String> enterpriseWechatCountList = result.stream()
                .map(FindEmployeeUserInfoByEmployeeCodeListResponse::getEnterpriseWechatCount)
                .collect(Collectors.toList());
        return enterpriseWechatCountList;
    }

    public Boolean batchAudit(List<FlowApplyBizRequest> requests) {
        //获取bpm里面本人的相对应的instanceId,code,的任务id
        FlowItemExtRequest flowItemExtRequest = new FlowItemExtRequest();
        flowItemExtRequest.setWorkflowcode(WorkFlowCodeEnum.MD_SPECIAL_APPLY.getCode());
        FlowItemExtApiResponse flowItemExtApiResponse = flowBpmReadFacade.workItemExtList(flowItemExtRequest);
        if (flowItemExtApiResponse.getContent().isEmpty()) {
            throw new BusinessException("该账号未存在待办");
        }

        Map<String, FlowApplyBizRequest> requestMap = requests.stream()
                .collect(Collectors.toMap(
                        FlowApplyBizRequest::getWorkflowInstanceId,
                        Function.identity()
                ));

        //根据instanceId分组，获取所有待办
        Map<String, List<FlowItemExtApiResponse.WorkItem>> workItemMap = flowItemExtApiResponse.getContent()
                .stream()
                .collect(Collectors.groupingBy(FlowItemExtApiResponse.WorkItem::getInstanceId));

        //获取所有的instanceId
        List<String> instanceIds = requests.stream()
                .map(item -> item.getWorkflowInstanceId())
                .collect(Collectors.toList());


        List<MdSpecialApplyPO> byWorkflowInstanceIds = mdSpecialApplyReadService.getByWorkflowInstanceIds(instanceIds);
        if (byWorkflowInstanceIds.isEmpty()) {
            throw new BusinessException("待办信息不存在");
        }

        //组装获取所有待办
        byWorkflowInstanceIds.stream().forEach(
                po -> {
                    List<FlowItemExtApiResponse.WorkItem> workItem = workItemMap.get(po.getWorkflowInstanceId())
                            .parallelStream()
                            .filter(item -> item.getActivityCode().equals(BiweeklyMdFlowNodeEnum.getBpmCodeByCode(po.getAuditFlowStatus())))
                            .collect(Collectors.toList());

                    FlowApplyBizRequest flowApplyBizRequest = requestMap.get(po.getWorkflowInstanceId());
                    flowApplyBizRequest.setWorkItemId(workItem.get(0).getId());
                    //调用审批
                    this.audit(flowApplyBizRequest);
                }
        );
        return Boolean.TRUE;
    }

    public Boolean cancel(FlowCancelRequest request) {
        FlowCancelItemRequest flowCancelItemRequest = new FlowCancelItemRequest();
        BeanUtil.copyProperties(request, flowCancelItemRequest);
        MdSpecialApplyPO mdSpecialApplyPO = mdSpecialApplyReadService.getByWorkflowInstanceId(request.getWorkflowInstanceId());
        if (Objects.isNull(mdSpecialApplyPO)) {
            throw new BusinessException("月度MD特殊申请不存在");
        }

        // 驳回的状态才可以调用bpm作废
        Boolean cancel = null;
        if (StrUtil.equals(AuditStatusEnum.REJECTED.getCode(), mdSpecialApplyPO.getAuditStatus())) {
            //查询作废任务id
            List<FlowListByInstanceResponse> flowListByInstanceResponses = flowBpmReadFacade.listByInstance(request.getWorkflowInstanceId());
            if (CollUtil.isEmpty(flowListByInstanceResponses)) {
                throw new BusinessException("个人待办任务不存在");
            }

            List<FlowListByInstanceResponse> listByInstanceResponses = flowListByInstanceResponses.parallelStream()
                    .filter(item -> item.getActivityCode().equals(BiweeklyMdFlowNodeEnum.getBpmCodeByCode(mdSpecialApplyPO.getAuditFlowStatus())))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(listByInstanceResponses)) {
                throw new BusinessException("个人待办任务不存在");
            }

            //查询任务id 根据当前登录人
            Long userId = ServerContext.getUserId();
            EmployeeUsersTO employeeByUserId = employeeService.findEmployeeByUserId(userId);
            if (Objects.isNull(employeeByUserId)){
                throw new BusinessException("员工信息不存在");
            }
            // 过滤出participantName为当前登录人的数据
            Optional<FlowListByInstanceResponse> firstMatch = flowListByInstanceResponses.stream()
                    .filter(item -> StrUtil.equals(BiweeklyMdFlowNodeEnum.DEPARTMENT_DIRECTOR_APPROVAL.getCode(),mdSpecialApplyPO.getAuditFlowStatus()))
                    .filter(item -> employeeByUserId.getEmployeeName().equals(item.getParticipantName()))
                    .findFirst();

            // 如果找到匹配项，使用匹配项的ID；否则抛出异常
            if (firstMatch.isPresent()) {
                flowCancelItemRequest.setWorkItemId(firstMatch.get().getId());
            } else {
                throw new BusinessException("未找到待处理的审批任务");
            }
            //FlowListByInstanceResponse flowListByInstanceResponse = firstMatch.get();
            //flowCancelItemRequest.setWorkItemId(flowListByInstanceResponse.getId());
            cancel = flowBpmWriteFacade.cancelItem(flowCancelItemRequest);
            if (!cancel) {
                throw new BusinessException("作废失败！");
            }
        }
        //修改作废状态
        MdSpecialApplyPO mdSpecialApplyPOUpdate = new MdSpecialApplyPO();
        mdSpecialApplyPOUpdate.setId(mdSpecialApplyPO.getId());
        mdSpecialApplyPOUpdate.setAuditStatus(AuditStatusEnum.CANCELLED.getCode());
        mdSpecialApplyWriteService.updateWorkFlow(mdSpecialApplyPOUpdate);
        return cancel;
    }

    public Boolean reApply(FlowApplyBizRequest<BiweeklyMdReRequest> request) {
        // 是否需要从第一步开始审核
        MdSpecialApplyPO mdSpecialApplyPO = mdSpecialApplyReadService.getByWorkflowInstanceId(request.getWorkflowInstanceId());
        if (Objects.isNull(mdSpecialApplyPO)) {
            throw new BusinessException("月度MD特殊申请不存在");
        }
        // 调用BPM流程
        FlowBpmRequest flowBpmRequest = new FlowBpmRequest();
        flowBpmRequest.setComment(request.getComment());
        flowBpmRequest.setWorkItemId(request.getWorkItemId());
        flowBpmRequest.setSubmitToReject(request.getSubmitToReject());
        flowBpmRequest.setRejectToActivityCode(BiweeklyMdFlowNodeEnum.getBpmCodeByCode(request.getRejectToActivityCode()));
        flowBpmRequest.setWorkflowInstanceId(request.getWorkflowInstanceId());

        //查询任务id
        List<FlowListByInstanceResponse> flowListByInstanceResponses = flowBpmReadFacade.listByInstance(request.getWorkflowInstanceId());
        if (CollUtil.isEmpty(flowListByInstanceResponses)) {
            throw new BusinessException("个人待办任务不存在");
        }
        FlowListByInstanceResponse flowListByInstanceResponse = flowListByInstanceResponses.get(0);
        flowBpmRequest.setWorkItemId(flowListByInstanceResponse.getId());

        Boolean audit = flowBpmWriteFacade.audit(flowBpmRequest);
        if (!audit) {
            throw new BusinessException("审核失败！");
        }

        MdSpecialApplyPO mdSpecialApplyPOUpdate = new MdSpecialApplyPO();
        mdSpecialApplyPOUpdate.setId(mdSpecialApplyPO.getId());
        request.setSubmitToReject(true);
        mdSpecialApplyPOUpdate.setAuditStatus(AuditStatusEnum.UNDER_REVIEW.getCode());
        // 根据驳回记录，确定重新提交的审批节点
        List<MdAuditRecordTO> mdAuditRecordListBySpecialApplyId = mdAuditRecordReadService.getMdAuditRecordListBySpecialApplyId(mdSpecialApplyPO.getId());
        if (CollUtil.isEmpty(mdAuditRecordListBySpecialApplyId)) {
            throw new BusinessException("审核驳回记录不存在！");
        }
        List<MdAuditRecordTO> mdAuditRecordTOS = mdAuditRecordListBySpecialApplyId.stream().sorted(Comparator.comparing(MdAuditRecordTO::getCreatedAt).reversed())
                .collect(Collectors.toList());
        Map<String, FlowListByInstanceResponse> listByInstanceResponseMap = flowListByInstanceResponses.stream().collect( Collectors.toMap(FlowListByInstanceResponse::getId, Function.identity()));
        mdSpecialApplyPOUpdate.setAuditFlowStatus(BiweeklyMdFlowNodeEnum.getCodeByBpmCode(listByInstanceResponseMap.get(mdAuditRecordTOS.get(0).getWorkItemId()).getActivityCode()));
        mdSpecialApplyWriteService.updateWorkFlow(mdSpecialApplyPOUpdate);
        return Boolean.TRUE;
    }
}
