package io.terminus.lshm.product.server.domain.adjust.service.read.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.terminus.lshm.product.common.bean.request.adjust.AdjustFlowRequest;
import io.terminus.lshm.product.common.bean.request.adjust.AdjustItemPageQueryRequest;
import io.terminus.lshm.product.common.bean.request.adjust.AdjustPricePageQueryRequest;
import io.terminus.lshm.product.common.bean.request.adjust.AdjustStorePageQueryRequest;
import io.terminus.lshm.product.common.bean.response.adjust.*;
import io.terminus.lshm.product.common.enums.*;
import io.terminus.lshm.product.facade.price.dto.AdjustPriceExcelDTO;
import io.terminus.lshm.product.server.converter.*;
import io.terminus.lshm.product.server.domain.adjust.dao.AdjustPriceMapper;
import io.terminus.lshm.product.server.domain.adjust.model.*;
import io.terminus.lshm.product.server.domain.adjust.service.read.*;
import io.terminus.trantorframework.Paging;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 调价表 Service 实现类
 */
@Service
public class AdjustPriceReadServiceImpl extends ServiceImpl<AdjustPriceMapper, AdjustPricePO> implements AdjustPriceReadService {
    @Resource
    private AdjustCategoryPriceReadService adjustCategoryPriceReadService;
    @Resource
    private AdjustStoreReadService adjustStoreReadService;
    @Resource
    private AdjustPriceConverter adjustPriceConverter;
    @Resource
    private AdjustCategoryPriceConverter adjustCategoryPriceConverter;
    @Resource
    private AdjustStoreConverter adjustStoreConverter;
    @Resource
    private AdjustItemConverter adjustItemConverter;
    @Resource
    private AdjustItemReadService adjustItemReadService;
    @Resource
    private AdjustAuditRecordReadService adjustAuditRecordReadService;
    @Resource
    private AdjustAuditRecordConverter adjustAuditRecordConverter;
    @Resource
    private AdjustErrorReadService adjustErrorReadService;


    @Override
    public Paging<AdjustPricePageResponse> pageAdjustPrice(AdjustPricePageQueryRequest request) {

        LambdaQueryWrapper<AdjustPricePO> queryWrapper = buildLambdaQueryWrapper(request);
        Page<AdjustPricePO> result = this.page(new Page<>(request.getPageNo(), request.getPageSize()), queryWrapper);
        // 未查询到数据
        if (CollUtil.isEmpty(result.getRecords())) {
            return Paging.empty();
        }
        List<AdjustPricePageResponse> adjustPricePageResponses = adjustPriceConverter.listP2Rsp(result.getRecords());
        //查询调价类别
        List<Long> adjustIds = adjustPricePageResponses.stream().map(AdjustPricePageResponse::getId).collect(Collectors.toList());
        LambdaQueryWrapper<AdjustCategoryPricePO> categoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        categoryLambdaQueryWrapper.in(AdjustCategoryPricePO::getAdjustId, adjustIds);
        List<AdjustCategoryPricePO> categoryPriceList = adjustCategoryPriceReadService.list(categoryLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(categoryPriceList)) {
            Map<Long, List<AdjustCategoryPricePO>> categoryPriceMap = categoryPriceList.stream().collect(Collectors.groupingBy(AdjustCategoryPricePO::getAdjustId));
            adjustPricePageResponses.forEach(x -> {
                if (x.getFormType().equals(AdjustFormTypeEnum.NATIONAL_AREA.getCode())) {
                    List<AdjustCategoryPricePO> categoryList = categoryPriceMap.get(x.getId());
                    String category = categoryList.stream().map(AdjustCategoryPricePO::getCategory).map(String::valueOf).collect(Collectors.joining(","));
                    x.setAdjustCategory(category.replace(String.valueOf(AdjustCategoryEnum.DELIVERY_PRICE.getCode()), AdjustCategoryEnum.DELIVERY_PRICE.getDescription()).replace(String.valueOf(AdjustCategoryEnum.RETAIL_PRICE.getCode())
                            , AdjustCategoryEnum.RETAIL_PRICE.getDescription()).replace(String.valueOf(AdjustCategoryEnum.RECORD_PRICE.getCode()), AdjustCategoryEnum.RECORD_PRICE.getDescription()))
                    ;
                }
            });
        }

        return new Paging<>(result.getTotal(), adjustPricePageResponses);
    }

    @Override
    public AdjustPriceInfoResponse getAdjustPriceInfo(Long id) {
        AdjustPricePO adjustPricePO = this.getById(id);
        if (ObjectUtil.isEmpty(adjustPricePO)) {
            return new AdjustPriceInfoResponse();
        }
        AdjustPriceInfoResponse adjustPriceInfoResponse = adjustPriceConverter.p2Rsp(adjustPricePO);
        //查询调价类别
        LambdaQueryWrapper<AdjustCategoryPricePO> categoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        categoryLambdaQueryWrapper.eq(AdjustCategoryPricePO::getAdjustId, id);
        //查询调价类别
        List<AdjustCategoryPricePO> categoryPriceList = adjustCategoryPriceReadService.list(categoryLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(categoryPriceList)) {
            List<AdjustCategoryPriceInfoResponse> categories = adjustCategoryPriceConverter.listP2Rsp(categoryPriceList);
            adjustPriceInfoResponse.setCategories(categories);
        }
        //查询门店
        LambdaQueryWrapper<AdjustStorePO> storeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        storeLambdaQueryWrapper.eq(AdjustStorePO::getAdjustId, id);
        List<AdjustStorePO> storeList = adjustStoreReadService.list(storeLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(storeList)) {
            List<AdjustStoreInfoResponse> stores = adjustStoreConverter.listP2Rsp(storeList);
            adjustPriceInfoResponse.setStores(stores);
        }
        //查询商品
        LambdaQueryWrapper<AdjustItemPO> adjustItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        adjustItemLambdaQueryWrapper.eq(AdjustItemPO::getAdjustId, id);
        List<AdjustItemPO> adjustItemPOS = adjustItemReadService.list(adjustItemLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(adjustItemPOS)) {
            List<AdjustItemInfoResponse> items = adjustItemConverter.listP2Rsp(adjustItemPOS);
            adjustPriceInfoResponse.setItems(items);
        }
        //查询异常数量
        LambdaQueryWrapper<AdjustErrorPO> adjustErrorLambdaQueryWrapper = new LambdaQueryWrapper<>();
        adjustErrorLambdaQueryWrapper.eq(AdjustErrorPO::getAdjustId, id);
        adjustPriceInfoResponse.setErrorNumber(adjustErrorReadService.count(adjustErrorLambdaQueryWrapper));
        return adjustPriceInfoResponse;
    }


    @Override
    public List<AdjustPriceExcelDTO> listAdjustPriceExcel(AdjustPricePageQueryRequest request) {
        //分页查询调价列表
        LambdaQueryWrapper<AdjustPricePO> queryWrapper = buildLambdaQueryWrapper(request);
        Page<AdjustPricePO> pageResult = this.page(new Page<>(request.getPageNo(), request.getPageSize()), queryWrapper);
        if (ObjectUtil.isEmpty(pageResult.getRecords())) {
            return ListUtil.empty();
        }

        Map<Long, AdjustPriceExcelDTO> adjustPriceMap = adjustPriceConverter.listRsp2Map(pageResult.getRecords());
        List<Long> adjustIds = pageResult.getRecords().stream().map(AdjustPricePO::getId).collect(Collectors.toList());

        LambdaQueryWrapper<AdjustCategoryPricePO> categoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        categoryLambdaQueryWrapper.in(AdjustCategoryPricePO::getAdjustId, adjustIds);
        //查询调价类别
        List<AdjustCategoryPricePO> categoryPriceList = adjustCategoryPriceReadService.list(categoryLambdaQueryWrapper);
        Map<Long, List<AdjustCategoryPricePO>> categoryPriceMap = categoryPriceList.stream()
                .collect(Collectors.groupingBy(
                        AdjustCategoryPricePO::getAdjustId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(AdjustCategoryPricePO::getDeliveryTimeStart).reversed())
                                        .collect(Collectors.toList())
                        )
                ));
        //查询门店
        LambdaQueryWrapper<AdjustStorePO> storeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        storeLambdaQueryWrapper.in(AdjustStorePO::getAdjustId, adjustIds);
        List<AdjustStorePO> storeList = adjustStoreReadService.list(storeLambdaQueryWrapper);
        Map<Long, List<AdjustStorePO>> storeMap = storeList.stream().collect(Collectors.groupingBy(AdjustStorePO::getAdjustId));
        //查询商品
        LambdaQueryWrapper<AdjustItemPO> adjustItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        adjustItemLambdaQueryWrapper.in(AdjustItemPO::getAdjustId, adjustIds);
        List<AdjustItemPO> adjustItemList = adjustItemReadService.list(adjustItemLambdaQueryWrapper);
        // Map<Long, List<AdjustItemPO>> adjustItemMap = adjustItemList.stream().collect(Collectors.groupingBy(AdjustItemPO::getAdjustId));
        List<AdjustPriceExcelDTO> adjustPriceExcelList = new ArrayList<>();
        adjustItemList.forEach(adjustItem -> {
            AdjustPriceExcelDTO adjustPriceExcel= adjustPriceMap.get(adjustItem.getAdjustId());
            AdjustPriceExcelDTO adjustPriceExcelDTO = adjustItemConverter.p2d(adjustPriceExcel, adjustItem);
            List<AdjustStorePO> adjustStoreList = storeMap.get(adjustItem.getAdjustId());
            if (ObjectUtil.isNotEmpty(adjustStoreList)) {
                adjustPriceExcelDTO.setStoreList(adjustStoreList.stream().map(AdjustStorePO::getStoreName).collect(Collectors.joining(",")));
            }
            List<AdjustCategoryPricePO> adjustCategoryPriceList = categoryPriceMap.get(adjustItem.getAdjustId());
            if (ObjectUtil.isNotEmpty(adjustCategoryPriceList)) {
                if (adjustPriceExcel.getFormType().equals(AdjustFormTypeEnum.NATIONAL_AREA.getCode())) {
                    adjustCategoryPriceList.forEach(adjustCategoryPrice -> {
                        if (adjustCategoryPrice.getCategory().equals(AdjustCategoryEnum.DELIVERY_PRICE.getCode())) {
                            adjustPriceExcelDTO.setDeliveryTime(DateUtil.formatDate(adjustCategoryPrice.getDeliveryTimeStart()) + "-" + DateUtil.formatDate(adjustCategoryPrice.getDeliveryTimeEnd()));
                        }
                        if (adjustCategoryPrice.getCategory().equals(AdjustCategoryEnum.RETAIL_PRICE.getCode())) {
                            adjustPriceExcelDTO.setRetailTime(DateUtil.formatDate(adjustCategoryPrice.getDeliveryTimeStart()) + "-" + DateUtil.formatDate(adjustCategoryPrice.getDeliveryTimeEnd()));
                        }
                        if (adjustCategoryPrice.getCategory().equals(AdjustCategoryEnum.RECORD_PRICE.getCode())) {
                            adjustPriceExcelDTO.setMainTime(DateUtil.formatDate(adjustCategoryPrice.getDeliveryTimeStart()) + "-" + DateUtil.formatDate(adjustCategoryPrice.getDeliveryTimeEnd()));
                        }
                    });
                    String category = adjustCategoryPriceList.stream().map(AdjustCategoryPricePO::getCategory).map(String::valueOf).collect(Collectors.joining(","));
                    category = category.replace(String.valueOf(AdjustCategoryEnum.DELIVERY_PRICE.getCode()), AdjustCategoryEnum.DELIVERY_PRICE.getDescription()).replace(String.valueOf(AdjustCategoryEnum.RETAIL_PRICE.getCode())
                            , AdjustCategoryEnum.RETAIL_PRICE.getDescription()).replace(String.valueOf(AdjustCategoryEnum.RECORD_PRICE.getCode()), AdjustCategoryEnum.RECORD_PRICE.getDescription());
                    adjustPriceExcelDTO.setCategory(category);
                    String validPrice=adjustCategoryPriceList.stream().map(AdjustCategoryPricePO::getEffectiveType).map(code ->AdjusEffectiveTypeEnum.getDescByCode(code)).collect(Collectors.joining(","));
                    adjustPriceExcelDTO.setEffectiveType(validPrice);
                }else{
                    AdjustCategoryPricePO adjustCategoryPricePO= adjustCategoryPriceList.get(0);
                    adjustPriceExcelDTO.setActivityTime(DateUtil.formatDate(adjustCategoryPricePO.getDeliveryTimeStart())+"-"+DateUtil.formatDate(adjustCategoryPricePO.getDeliveryTimeEnd()));
                    adjustPriceExcelDTO.setEffectiveType(AdjusEffectiveTypeEnum.getDescByCode(adjustCategoryPricePO.getEffectiveType()));
                }

            }
            adjustPriceExcelList.add(adjustPriceExcelDTO);
        });
        return adjustPriceExcelList;
    }

    @Override
    public Paging<AdjustStoreInfoResponse> pageAdjustStore(AdjustStorePageQueryRequest request) {
        //查询门店
        LambdaQueryWrapper<AdjustStorePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdjustStorePO::getAdjustId, request.getAdjustId());
        if (ObjectUtil.isNotEmpty(request.getKeyword())) {
            queryWrapper.and(wrapper ->
                    wrapper.like(AdjustStorePO::getStoreName, request.getKeyword()).or().like(AdjustStorePO::getStoreCode,
                            request.getKeyword()));

        }

        Page<AdjustStorePO> result = adjustStoreReadService.page(new Page<>(request.getPageNo(), request.getPageSize()), queryWrapper);
        // 未查询到数据
        if (CollUtil.isEmpty(result.getRecords())) {
            return Paging.empty();
        }
        List<AdjustStoreInfoResponse> adjustStorePageResponses = adjustStoreConverter.listP2Rsp(result.getRecords());
        return new Paging<>(result.getTotal(), adjustStorePageResponses);
    }

    @Override
    public Paging<AdjustItemInfoResponse> pageAdjustItem(AdjustItemPageQueryRequest request) {
        //查询门店
        LambdaQueryWrapper<AdjustItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdjustItemPO::getAdjustId, request.getAdjustId());
        if (ObjectUtil.isNotEmpty(request.getKeyword())) {
            queryWrapper.and(wrapper ->
                    wrapper.like(AdjustItemPO::getItemName, request.getKeyword()).or().like(AdjustItemPO::getItemCode,
                            request.getKeyword()));

        }
        Page<AdjustItemPO> result = adjustItemReadService.page(new Page<>(request.getPageNo(), request.getPageSize()), queryWrapper);
        // 未查询到数据
        if (CollUtil.isEmpty(result.getRecords())) {
            return Paging.empty();
        }
        List<AdjustItemInfoResponse> adjustItemResponses = adjustItemConverter.listP2Rsp(result.getRecords());
        return new Paging<>(result.getTotal(), adjustItemResponses);
    }

    @Override
    public List<AdjustPricePO> getByWorkflowInstanceIds(List<String> workflowInstanceIds) {
        LambdaQueryWrapper<AdjustPricePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AdjustPricePO::getWorkflowInstanceId, workflowInstanceIds);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public AdjustPricePO getByWorkflowInstanceId(String workflowInstanceId) {
        LambdaQueryWrapper<AdjustPricePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdjustPricePO::getWorkflowInstanceId, workflowInstanceId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    public LambdaQueryWrapper<AdjustPricePO> buildLambdaQueryWrapper(@NotNull AdjustPricePageQueryRequest request) {
        LambdaQueryWrapper<AdjustPricePO> queryWrapper = new LambdaQueryWrapper<>();
        //流程类型
        if (ObjectUtil.isNotEmpty(request.getFormType())) {
            queryWrapper.in(AdjustPricePO::getFormType, request.getFormType());
        }
        //流程标题
        if (ObjectUtil.isNotEmpty(request.getTitile())) {
            queryWrapper.like(AdjustPricePO::getTitile, request.getTitile());
        }
        //申请人
        if (ObjectUtil.isNotEmpty(request.getCreatedName())) {
            queryWrapper.like(AdjustPricePO::getCreatedName, request.getCreatedName());
        }
        //申请日期
        if (ObjectUtil.isNotEmpty(request.getCreateStartTime()) && ObjectUtil.isNotEmpty(request.getCreateEndTime())) {
            queryWrapper.between(AdjustPricePO::getCreatedAt, request.getCreateStartTime(), request.getCreateEndTime());
        }
        //促销类型
        if (ObjectUtil.isNotEmpty(request.getPromotionType())) {
            queryWrapper.in(AdjustPricePO::getPromotionType, request.getPromotionType());
        }
        //调价通知类别
        if (ObjectUtil.isNotEmpty(request.getAdjustNotice())) {
            queryWrapper.in(AdjustPricePO::getAdjustNotice, request.getAdjustNotice());
        }
        //新增品牌
        if (CollUtil.isNotEmpty(request.getBrandId())){
            queryWrapper.in(AdjustPricePO::getBrandId, request.getBrandId());
        }

        //审批流程状态
        if (ObjectUtil.isNotEmpty(request.getFlowStatusList())) {
            queryWrapper.and(w -> {
                for (int i = 0; i < request.getFlowStatusList().size(); i++) {
                    String value = request.getFlowStatusList().get(i);
                    if (i == 0) {
                        w.like(AdjustPricePO::getAdjustStatus, value);
                    } else {
                        w.or().like(AdjustPricePO::getAdjustStatus, value);
                    }
                }
            });
        }
        //审批状态
        if (ObjectUtil.isNotEmpty(request.getAuditStatusList())) {
            queryWrapper.and(w -> {
                for (int i = 0; i < request.getAuditStatusList().size(); i++) {
                    String value = request.getAuditStatusList().get(i);

                    if (i == 0) {
                        w.like(AdjustPricePO::getAdjustStatus, value);
                    } else {
                        w.or().like(AdjustPricePO::getAdjustStatus, value);
                    }

                }
            });
        }
        //调价类别
        if (ObjectUtil.isNotEmpty(request.getCategory())) {
            //查询关联的调价单主键
            LambdaQueryWrapper<AdjustCategoryPricePO> categoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
            categoryLambdaQueryWrapper.in(AdjustCategoryPricePO::getCategory, request.getCategory());
            List<AdjustCategoryPricePO> categoryPriceList = adjustCategoryPriceReadService.list(categoryLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(categoryPriceList)) {
                queryWrapper.in(AdjustPricePO::getId, categoryPriceList.stream().map(AdjustCategoryPricePO::getAdjustId).collect(Collectors.toSet()));
            } else {
                queryWrapper.eq(AdjustPricePO::getId, 0);

            }
        }
        //应用门店
        if (ObjectUtil.isNotEmpty(request.getStoreId())) {
            //查询关联的调价单主键
            LambdaQueryWrapper<AdjustStorePO> storeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            storeLambdaQueryWrapper.eq(AdjustStorePO::getStoreId, request.getStoreId());
            List<AdjustStorePO> storeList = adjustStoreReadService.list(storeLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(storeList)) {
                queryWrapper.in(AdjustPricePO::getId, storeList.stream().map(AdjustStorePO::getAdjustId).collect(Collectors.toSet()));

            } else {
                queryWrapper.eq(AdjustPricePO::getId, 0);

            }
        }

        queryWrapper.last("ORDER BY " +
                "CASE WHEN adjust_status NOT IN ('" + AdjustPriceFlowNodeEnum.COMPLETED.getCode() + "', '" +
                AdjustPriceFlowNodeEnum.CANCELLED.getCode() + "') THEN 0 ELSE 1 END, " +  // 状态分组排序
                "adjust_notice DESC, " +  // 各组内按AdjustNotice降序
                "updated_at DESC, " +     // 各组内按UpdatedAt降序
                "id DESC");               // 各组内按Id降序
        return queryWrapper;
    }
}