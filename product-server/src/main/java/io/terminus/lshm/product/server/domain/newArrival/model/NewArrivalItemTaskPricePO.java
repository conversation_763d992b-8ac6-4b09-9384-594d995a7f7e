package io.terminus.lshm.product.server.domain.newArrival.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.terminus.lshm.common.mybatisplus.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("new_arrival_item_task_price")
public class NewArrivalItemTaskPricePO extends BaseModel<Long> {

    /**
     * 商品建档id
     */
    @TableField("new_coming_item_id")
    private Long newComingItemId;

    /**
     * 单位id
     */
    @TableField("unit_id")
    private Long unitId;

    /**
     * 单位名称
     */
    @TableField("unit_name")
    private String unitName;

    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 换算系数
     */
    @TableField("conversion_factor")
    private BigDecimal conversionFactor;

    /**
     * 单位类型
     */
    @TableField("unit_type")
    private String unitType;

    /**
     * 配送价-基本单位
     */
    @TableField("base_unit_delivery_price")
    private BigDecimal baseUnitDeliveryPrice;

    /**
     * 零售价
     */
    @TableField("retail_price")
    private BigDecimal retailPrice;

    /**
     * 会员零食价
     */
    @TableField("member_price")
    private BigDecimal memberPrice;

    /**
     * 其他规格零售价
     */
    @TableField("other_specification_price")
    private BigDecimal otherSpecificationPrice;

    /**
     * 24%高成本地区价格
     */
    @TableField("twenty_four_percent")
    private BigDecimal twentyFourPercent;

    /**
     * 22%高成本地区价格
     */
    @TableField("twenty_two_percent")
    private BigDecimal twentyTwoPercent;

    /**
     * 21%高成本地区价格
     */
    @TableField("twenty_one_percent")
    private BigDecimal twentyOnePercent;
    /**
     * 配送价目表
     */
    @TableField("delivery_price")
    private BigDecimal deliveryPrice;

}
