package io.terminus.lshm.product.server.domain.biweekly.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.terminus.lshm.product.server.domain.biweekly.dao.MdAuditRecordMapper;
import io.terminus.lshm.product.server.domain.biweekly.model.MdAuditRecordPO;
import io.terminus.lshm.product.server.domain.biweekly.service.write.MdAuditRecordWriteService;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.lshm.server.common.ServerUserInfo;
import io.terminus.trantorframework.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Description 双周MD审核记录实现类
 * <AUTHOR>
 * @date 2025/6/20 10:50
 */
@Service
public class MdAuditRecordWriteServiceImpl extends ServiceImpl<MdAuditRecordMapper, MdAuditRecordPO> implements MdAuditRecordWriteService {
    @Override
    public void add(Long specialApplyId, String status, String comment, String workItemId) {
        MdAuditRecordPO mdAuditRecordPO = new MdAuditRecordPO();
        //创建人
        ServerUserInfo userInfo = ServerContext.getUserInfo();
        if (!Objects.isNull(userInfo)){
            mdAuditRecordPO.setCreatedBy(userInfo.getUserId());
            mdAuditRecordPO.setUpdatedBy(userInfo.getUserId());
            mdAuditRecordPO.setAuditor(userInfo.getUserId().toString());
            mdAuditRecordPO.setAuditorName(userInfo.getNickName());
        }
        mdAuditRecordPO.setSpecialApplyId(specialApplyId);
        mdAuditRecordPO.setAuditReason(comment);
        mdAuditRecordPO.setWorkItemId(workItemId);
        mdAuditRecordPO.setSpecialApply(status);
        boolean save = this.save(mdAuditRecordPO);
        if (!save){
            throw new BusinessException("新增审核记录失败");
        }
    }
}
