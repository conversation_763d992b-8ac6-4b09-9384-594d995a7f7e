package io.terminus.lshm.product.server.converter;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import io.terminus.lshm.product.common.bean.response.adjust.AdjustPriceInfoResponse;
import io.terminus.lshm.product.common.bean.response.adjust.AdjustPricePageResponse;
import io.terminus.lshm.product.common.enums.AdjustNoticeEnum;
import io.terminus.lshm.product.common.enums.AdjustPriceFlowNodeEnum;
import io.terminus.lshm.product.facade.price.dto.AdjustPriceExcelDTO;
import io.terminus.lshm.product.facade.price.to.AdjustPriceTO;
import io.terminus.lshm.product.server.domain.adjust.model.AdjustPricePO;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = "spring")
public interface AdjustPriceConverter extends BasicConvert<AdjustPriceTO, AdjustPricePO> {

    List<AdjustPricePageResponse> listP2Rsp(List<AdjustPricePO> list);


    AdjustPriceInfoResponse p2Rsp(AdjustPricePO adjustPricePO);

    default Map<Long,AdjustPriceExcelDTO> listRsp2Map(List<AdjustPricePO> data) {

        Map<Long,AdjustPriceExcelDTO> map= new HashMap<>();
        data.forEach(response -> {
            AdjustPriceExcelDTO adjustPriceDTO = new AdjustPriceExcelDTO();
            adjustPriceDTO.setId(response.getId());
            adjustPriceDTO.setTitile(response.getTitile());
            adjustPriceDTO.setAdjustStatus(response.getAdjustStatus());
            if(ObjectUtil.isNotEmpty(response.getAdjustNotice())) {
                adjustPriceDTO.setAdjustNotice(AdjustNoticeEnum.getDescByType(response.getAdjustNotice()));
            }
            adjustPriceDTO.setCreatedAt(DateUtil.formatDate(response.getCreatedAt()));
            adjustPriceDTO.setCreatedName(response.getCreatedName());
            adjustPriceDTO.setFormType(response.getFormType());
            adjustPriceDTO.setAdjustStatus("当前待"+ AdjustPriceFlowNodeEnum.getDescByCode(response.getAdjustStatus()));
            adjustPriceDTO.setFileUrl(response.getFileUrl());
            adjustPriceDTO.setReason(response.getReason());
            adjustPriceDTO.setRemark(response.getRemark());
            map.put(adjustPriceDTO.getId(), adjustPriceDTO);
        });
        return map;

    }
}
