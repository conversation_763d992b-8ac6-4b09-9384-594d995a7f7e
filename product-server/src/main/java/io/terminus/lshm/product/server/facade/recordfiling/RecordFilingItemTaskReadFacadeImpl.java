package io.terminus.lshm.product.server.facade.recordfiling;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import io.terminus.gaia.organization.api.facade.InnerEmployeeReadFacade;
import io.terminus.gaia.organization.api.request.FindEmployeePageRequest;
import io.terminus.gaia.organization.api.response.FindEmployeePageResponse;
import io.terminus.lshm.ncc.response.TreeNccGoodsTypeResponse;
import io.terminus.lshm.ncc.response.TreeNccNewDisplayResponse;
import io.terminus.lshm.product.common.enums.AuditRecordFilingFlowEnum;
import io.terminus.lshm.product.common.enums.BusinessTypeEnum;
import io.terminus.lshm.product.common.offshelf.response.EmployeeUserResponse;
import io.terminus.lshm.product.common.recordfiling.response.*;
import io.terminus.lshm.product.facade.recordfiling.RecordFilingItemTaskReadFacade;
import io.terminus.lshm.product.common.recordfiling.dto.EmployeeTO;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskDTO;
import io.terminus.lshm.product.common.recordfiling.request.EmployeePageRequest;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskExportRequest;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskPageRequest;
import io.terminus.lshm.product.common.recordfiling.request.TaxRateRequest;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskDTO;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskListReaponse;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskPageResponse;
import io.terminus.lshm.product.common.recordfiling.response.RecordFilingItemTaskResponse;
import io.terminus.lshm.product.common.recordfiling.response.TaxRateResponse;
import io.terminus.lshm.product.facade.recordfiling.RecordFilingItemTaskReadFacade;
import io.terminus.lshm.product.server.converter.EmployeeUserConverter;
import io.terminus.lshm.product.server.converter.RecordFilingItemTaskConverter;
import io.terminus.lshm.product.server.domain.recordFiling.dao.TaxRateDataDao;
import io.terminus.lshm.product.server.domain.recordFiling.model.TaxRateDataPO;
import io.terminus.lshm.product.server.domain.recordFiling.service.read.RecordFilingItemTaskReadService;
import io.terminus.lshm.product.server.manager.BusinessSendNccManager;
import io.terminus.lshm.product.server.external.user.model.EmployeeUserDTO;
import io.terminus.lshm.product.server.manager.FlowConfigManager;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@RequiredArgsConstructor
@RestController
public class RecordFilingItemTaskReadFacadeImpl implements RecordFilingItemTaskReadFacade {
    @Resource
    private RecordFilingItemTaskReadService recordFilingItemTaskReadService;

    @Resource
    private TaxRateDataDao taxRateDataDao;

    @Resource
    private RecordFilingItemTaskConverter recordFilingItemTaskConverter;

    @Resource
    private BusinessSendNccManager businessSendNccManager;

    @Resource
    private FlowConfigManager flowConfigManager;

    @Resource
    private EmployeeUserConverter employeeUserConverter;
    @Resource
    private InnerEmployeeReadFacade innerEmployeeReadFacade;


    @Override
    public Response<Paging<RecordFilingItemTaskPageResponse>> pageRecordFilingItemTask(RecordFilingItemTaskPageRequest request) {
        Paging<RecordFilingItemTaskPageResponse> paging = recordFilingItemTaskReadService.page(request);
        return Response.ok(paging);
    }


    @Override
    public Response<RecordFilingItemTaskResponse> detail(Long id) {
        return recordFilingItemTaskReadService.detail(id);
    }

    @Override
    public Response<List<RecordFilingItemTaskListReaponse>> newArrivalGetList(RecordFilingItemTaskPageRequest request) {
        return recordFilingItemTaskReadService.newArrivalGetList(request);
    }

    @Override
    public Response<List<TaxRateResponse>> selectTaxRate(TaxRateRequest request) {
        //查询商品规格构成信息和价格信息
        LambdaQueryWrapper<TaxRateDataPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(request.getCode()), TaxRateDataPO::getTaxRateCode, request.getCode());
        List<TaxRateDataPO> list = taxRateDataDao.selectList(lambdaQueryWrapper);
        List<TaxRateResponse> rateResponseList = recordFilingItemTaskConverter.listP2RspTax(list);
        rateResponseList.forEach(taxRateResponse -> {
            if (StringUtils.isNotBlank(taxRateResponse.getTaxRate())) {
                List<String> taxList = Arrays.asList(taxRateResponse.getTaxRate().split("、"));
                taxRateResponse.setTaxRate(taxList.get(0).replace("%", ""));
            }
        });
        return Response.ok(rateResponseList);
    }

    @Override
    public Response<Paging<RecordFilingItemTaskDTO>> queryRecordFilingItemTaskList(RecordFilingItemTaskExportRequest request) {
        Page<RecordFilingItemTaskDTO> result = recordFilingItemTaskReadService.queryRecordFilingItemTaskList(request);
        return Response.ok(new Paging<>(result.getTotal(), result.getRecords()));
    }

    @Override
    public Response<List<TreeNccNewDisplayResponse>> nccNewDisplayCategory() {
        return Response.ok(recordFilingItemTaskReadService.nccNewDisplayCategory());
    }

    @Override
    public Response<List<TreeNccGoodsTypeResponse>> goodsTypeList() {
        return Response.ok(recordFilingItemTaskReadService.goodsTypeList());
    }

    @Override
    public Response<Paging<EmployeeTO>> findEmployeePage(EmployeePageRequest request) {
        FindEmployeePageRequest findEmployeePageRequest = new FindEmployeePageRequest();
        findEmployeePageRequest.setPageNo(request.getPageNo());
        findEmployeePageRequest.setPageSize(request.getPageSize());
        findEmployeePageRequest.setEmployeeName(request.getEmployeeName());
        findEmployeePageRequest.setEmployeeCode(request.getEmployeeCode());
        findEmployeePageRequest.setEmployeePhone(request.getEmployeePhone());
        Paging<FindEmployeePageResponse> employeePageResponse = innerEmployeeReadFacade.findEmployeePage(findEmployeePageRequest).getResult();
        log.info("findEmployeePageResponse:{}", employeePageResponse);
        if (ObjectUtil.isEmpty(employeePageResponse)) {
            return Response.ok(Paging.empty());
        }
        List<EmployeeTO> findEmployeeList = employeeUserConverter.employeeP2T(employeePageResponse.getData());
        return Response.ok(new Paging<>(employeePageResponse.getTotal(), findEmployeeList));
    }

    @Override
    public Response<MyApprovalResponse> myApprovals() {
        return Response.ok(recordFilingItemTaskReadService.myApprovals());
    }

    @Override
    public Response<List<EmployeeUserResponse>> selectProductManager() {
        Set<String> employeeCodes = flowConfigManager.selectProductManager(BusinessTypeEnum.PRODUCT_ARCHIVING.getCode(), AuditRecordFilingFlowEnum.PRODUCT_MANAGER.getKey());
        Map<String, EmployeeUserDTO> maps = flowConfigManager.cacheUserAll(employeeCodes);
        return Response.ok(employeeUserConverter.listEmployee(Lists.newArrayList(maps.values())));
    }

}