package io.terminus.lshm.product.server.facade.biweeklyMd;

import io.terminus.lshm.product.common.bean.request.biweeklyMd.BiweeklyMdReRequest;
import io.terminus.lshm.product.common.bean.request.bpm.FlowCancelRequest;
import io.terminus.lshm.product.facade.biweeklyMd.BiweeklyMdWriteFacade;
import io.terminus.lshm.product.flow.request.FlowApplyBizRequest;
import io.terminus.lshm.product.server.domain.biweekly.manager.BiweeklyMdManager;
import io.terminus.trantorframework.Response;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 双周MD 写接口实现类
 * <AUTHOR>
 * @date 2025/6/19 11:47
 */
@RestController
public class BiweeklyMdWriteFacadeImpl implements BiweeklyMdWriteFacade {

    @Resource
    private BiweeklyMdManager biweeklyMdManager;

    @Override
    public Response<Boolean> audit(FlowApplyBizRequest request) {
        return Response.ok(biweeklyMdManager.audit(request));
    }

    /**
     * 批量审批
     * @param requests
     * @return
     */
    @Override
    public Response<Boolean> batchAudit(List<FlowApplyBizRequest> requests) {
        return Response.ok(biweeklyMdManager.batchAudit(requests));
    }

    @Override
    public Response<Boolean> flowUp(Long id) {
        return Response.ok(biweeklyMdManager.flowUp(id,false));
    }

    @Override
    public Response<Boolean> cancel(FlowCancelRequest request) {
        return Response.ok(biweeklyMdManager.cancel(request));
    }

    @Override
    public Response<Boolean> reApply(FlowApplyBizRequest<BiweeklyMdReRequest> request) {
        return Response.ok(biweeklyMdManager.reApply(request));
    }
}
