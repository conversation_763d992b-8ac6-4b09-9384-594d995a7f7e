package io.terminus.lshm.product.server.domain.newArrival.service.write.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.terminus.lshm.product.common.bean.request.newArrival.NewArrivalItemTaskPriceRequest;
import io.terminus.lshm.product.common.bean.request.newArrival.NewArrivalItemTaskRequest;
import io.terminus.lshm.product.common.bean.request.newArrival.NewArrivalTaskAuditRequest;
import io.terminus.lshm.product.common.enums.AuditStatusEnum;
import io.terminus.lshm.product.common.enums.BusinessTypeEnum;
import io.terminus.lshm.product.common.enums.ComingItemTypeEnum;
import io.terminus.lshm.product.common.enums.NewArrivalFlowNodeEnum;
import io.terminus.lshm.product.common.recordfiling.dto.RecordFilingItemTaskSpecificationPriceTO;
import io.terminus.lshm.product.server.converter.BusinessAuditRecordConverter;
import io.terminus.lshm.product.server.converter.FlowConfigConverter;
import io.terminus.lshm.product.server.converter.NewArrivalItemTaskConverter;
import io.terminus.lshm.product.server.converter.NewArrivalItemTaskPriceConverter;
import io.terminus.lshm.product.server.manager.BusinessTypeService;
import io.terminus.lshm.product.server.domain.newArrival.dao.NewArrivalItemTaskDao;
import io.terminus.lshm.product.server.domain.newArrival.model.NewArrivalItemTaskPO;
import io.terminus.lshm.product.server.domain.newArrival.model.NewArrivalItemTaskPricePO;
import io.terminus.lshm.product.server.domain.newArrival.service.write.NewArrivalItemTaskPriceWriteService;
import io.terminus.lshm.product.server.domain.newArrival.service.write.NewArrivalItemTaskWriteService;
import io.terminus.lshm.product.server.event.dto.BusinessFlowAuditEventDTO;
import io.terminus.lshm.server.common.ServerContext;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NewArrivalItemTaskWriteServiceImpl extends ServiceImpl<NewArrivalItemTaskDao, NewArrivalItemTaskPO> implements NewArrivalItemTaskWriteService {

    @Resource
    private NewArrivalItemTaskConverter newArrivalItemTaskConverter;

    @Resource
    private NewArrivalItemTaskPriceConverter newArrivalItemTaskPriceConverter;

    @Resource
    private NewArrivalItemTaskPriceWriteService newArrivalItemTaskPriceWriteService;

    @Resource
    private BusinessTypeService businessTypeService;

    @Resource
    private BusinessAuditRecordConverter businessAuditRecordConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<Integer, Long> save(List<NewArrivalItemTaskRequest> requestList) {
        Result result = this.insertNewArrivalItemTask(requestList, ComingItemTypeEnum.APPLY_STATUS_SAVE.getCode());
        return result.idMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(List<NewArrivalItemTaskRequest> requestList) {
        Result result = this.insertNewArrivalItemTask(requestList, ComingItemTypeEnum.APPLY_STATUS_SUBMIT.getCode());

        //提交走流程
        for (Long id : result.idList) {
            BusinessFlowAuditEventDTO businessFlowAuditRequest = new BusinessFlowAuditEventDTO();
            businessFlowAuditRequest.setBusinessId(id.toString());
            businessFlowAuditRequest.setBusinessType(BusinessTypeEnum.PRODUCT_LAUNCHING.getCode());

            businessTypeService.businessFlowAudit(businessFlowAuditRequest);
        }


    }


    private Result insertNewArrivalItemTask(List<NewArrivalItemTaskRequest> requestList, String applyStatus) {

        //保存返回
        Map<Integer, Long> idMap = new HashMap<>();
        //提交返回
        List<Long> idList = new ArrayList<>();
        for (NewArrivalItemTaskRequest request : requestList) {
            NewArrivalItemTaskPO newArrivalItemTaskPO = newArrivalItemTaskConverter.t2pReq(request);
            //设置表单状态
            newArrivalItemTaskPO.setApplyStatus(applyStatus);
            //新增
            if (newArrivalItemTaskPO.getId() == null) {
                //设置创建人名称
                newArrivalItemTaskPO.setCreatedName(ServerContext.getUserNickName());

                this.baseMapper.insert(newArrivalItemTaskPO);
                //将前端生成的临时id与正式id对应 返回
                if (request.getTempId() != null) {
                    idMap.put(request.getTempId(), newArrivalItemTaskPO.getId());
                }

                //修改
            } else {
                this.baseMapper.updateById(newArrivalItemTaskPO);
                //将价格信息先全量删除
                LambdaQueryWrapper<NewArrivalItemTaskPricePO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(NewArrivalItemTaskPricePO::getNewComingItemId, request.getId());
                newArrivalItemTaskPriceWriteService.remove(queryWrapper);
            }
            idList.add(newArrivalItemTaskPO.getId());
            //保存价格信息
            if (!CollectionUtils.isEmpty(request.getPriceRequestList())) {
                List<NewArrivalItemTaskPricePO> taskPricePOList = new ArrayList<>();
                for (NewArrivalItemTaskPriceRequest priceRequest : request.getPriceRequestList()) {

                    NewArrivalItemTaskPricePO newArrivalItemTaskPricePO = newArrivalItemTaskPriceConverter.t2pReq(priceRequest);
                    //设置上新id
                    newArrivalItemTaskPricePO.setNewComingItemId(newArrivalItemTaskPO.getId());
                    taskPricePOList.add(newArrivalItemTaskPricePO);
                }
                newArrivalItemTaskPriceWriteService.saveBatch(taskPricePOList);
            }
        }
        return new Result(idMap, idList);
    }

    public void validNewArrivalItem(List<NewArrivalItemTaskRequest> requestList) {
        List<String> filterList = requestList.stream().map(NewArrivalItemTaskRequest::getItemCode).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        if (requestList.size() != filterList.size()) {
            throw new BusinessException("上新商品不能重复");
        }
        List<Long> ids = requestList.stream().map(NewArrivalItemTaskRequest::getId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        //按商品编码查询数据库已存在的上新数据
        LambdaQueryWrapper<NewArrivalItemTaskPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NewArrivalItemTaskPO::getItemCode, filterList);
        queryWrapper.notIn(ObjectUtil.isNotEmpty(ids), NewArrivalItemTaskPO::getId, ids);
        //查询需要比对的数据
        List<NewArrivalItemTaskPO> taskList = this.list(queryWrapper);
        if (ObjectUtil.isEmpty(taskList)) {
            return;
        }

        Set<String> existItemNameList = new HashSet<>();
        requestList.forEach(item -> {
            List<String> itemHMAreaIds;
            List<String> itemYMAreaIds;

            //很忙区域id
            if (ObjectUtil.isNotEmpty(item.getAreaIdList())) {
                itemHMAreaIds = Arrays.stream(item.getAreaIdList().split(",")).map(String::trim).collect(Collectors.toList());
            } else {
                itemHMAreaIds = null;
            }

            //一鸣区域id
            if (ObjectUtil.isNotEmpty(item.getYmAreaIdList())) {
                itemYMAreaIds = Arrays.stream(item.getYmAreaIdList().split(",")).map(String::trim).collect(Collectors.toList());
            } else {
                itemYMAreaIds = null;
            }
            //仓库id
            List<String> warehouseIds = Arrays.stream(item.getSuggestionWarehouseIdList().split(",")).map(String::trim).collect(Collectors.toList());
            //门店店型类型
            List<String> storeShopTypeIds = Arrays.stream(item.getStoreShopTypeList().split(",")).map(String::trim).collect(Collectors.toList());
            //门店类型
            List<String> storeTypeIds = Arrays.stream(item.getStoreTypeList().split(",")).map(String::trim).collect(Collectors.toList());

            taskList.forEach(task -> {
                if (item.getItemCode().equals(task.getItemCode())) {
                    boolean hmAreaIdExists = false;
                    boolean ymAreaIdExists = false;
                    boolean storeShopTypeIdExists = false;
                    boolean storeTypeIdExists = false;
                    boolean warehouseIdExists = false;
                    if (ObjectUtil.isNotEmpty(itemHMAreaIds) && ObjectUtil.isNotEmpty(task.getAreaIdList())) {
                        List<String> taskAreaIds = Arrays.stream(task.getAreaIdList().split(",")).map(String::trim).collect(Collectors.toList());
                        //取交集
                        if (!ListUtils.retainAll(itemHMAreaIds, taskAreaIds).isEmpty()) {
                            hmAreaIdExists = true;

                        }
                    }
                    if (ObjectUtil.isNotEmpty(itemYMAreaIds) && ObjectUtil.isNotEmpty(task.getYmAreaIdList())) {
                        List<String> taskAreaIds = Arrays.stream(task.getYmAreaIdList().split(",")).map(String::trim).collect(Collectors.toList());
                        if (!ListUtils.retainAll(itemYMAreaIds, taskAreaIds).isEmpty()) {
                            ymAreaIdExists = true;

                        }
                    }
                    //仓库id
                    List<String> taskWarehouseIds = Arrays.stream(task.getSuggestionWarehouseIdList().split(",")).map(String::trim).collect(Collectors.toList());
                    if (!ListUtils.retainAll(warehouseIds, taskWarehouseIds).isEmpty()) {
                        warehouseIdExists = true;

                    }
                    //门店店型类型
                    List<String> taskStoreShopTypeIds = Arrays.stream(task.getStoreShopTypeList().split(",")).map(String::trim).collect(Collectors.toList());
                    if (!ListUtils.retainAll(storeShopTypeIds, taskStoreShopTypeIds).isEmpty()) {
                        storeShopTypeIdExists = true;

                    }
                    //门店类型
                    List<String> taskStoreTypeIds = Arrays.stream(task.getStoreTypeList().split(",")).map(String::trim).collect(Collectors.toList());
                    if (!ListUtils.retainAll(storeTypeIds, taskStoreTypeIds).isEmpty()) {
                        storeTypeIdExists = true;

                    }
                    //如果条件同时满足，说明同一商品在同一区域、仓库、门店内上新
                    if (hmAreaIdExists || ymAreaIdExists) {
                        if (storeShopTypeIdExists && storeTypeIdExists && warehouseIdExists) {
                            existItemNameList.add(item.getItemName());
                        }
                    }
                }

            });

        });

        if (ObjectUtil.isNotEmpty(existItemNameList)) {
            throw new BusinessException(String.format("商品【%s】已上新，不能重复上新", String.join(",", existItemNameList)));
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(NewArrivalItemTaskRequest request) {
        NewArrivalItemTaskPO newArrivalItemTaskPO = newArrivalItemTaskConverter.t2pReq(request);
        this.baseMapper.updateById(newArrivalItemTaskPO);
        //将价格信息先全量删除
        LambdaQueryWrapper<NewArrivalItemTaskPricePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NewArrivalItemTaskPricePO::getNewComingItemId, request.getId());
        newArrivalItemTaskPriceWriteService.remove(queryWrapper);
        //价格信息新增
        if (!CollectionUtils.isEmpty(request.getPriceRequestList())) {
            List<NewArrivalItemTaskPricePO> taskPricePOList = new ArrayList<>();
            for (NewArrivalItemTaskPriceRequest priceRequest : request.getPriceRequestList()) {
                //设置上新id
                priceRequest.setNewComingItemId(newArrivalItemTaskPO.getId());
                NewArrivalItemTaskPricePO newArrivalItemTaskPricePO = newArrivalItemTaskPriceConverter.t2pReq(priceRequest);

                taskPricePOList.add(newArrivalItemTaskPricePO);
            }
            newArrivalItemTaskPriceWriteService.saveBatch(taskPricePOList);
        }
        //再次提交走审批
        BusinessFlowAuditEventDTO businessFlowAuditRequest = new BusinessFlowAuditEventDTO();
        businessFlowAuditRequest.setBusinessId(request.getId().toString());
        businessFlowAuditRequest.setBusinessType(BusinessTypeEnum.PRODUCT_LAUNCHING.getCode());
        businessFlowAuditRequest.setBusinessKey(FlowConfigConverter.START);
        businessFlowAuditRequest.setAuditStatus(AuditStatusEnum.APPROVED.getCode());

        businessTypeService.businessFlowAudit(businessFlowAuditRequest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(NewArrivalTaskAuditRequest request) {
        // 插入审批表  流程扭转全部调用振东接口
        BusinessFlowAuditEventDTO bus = businessAuditRecordConverter.req2DTO(request);
        businessTypeService.businessFlowAudit(bus);

        // 查询主表记录
        NewArrivalItemTaskPO newArrivalItemTask = new NewArrivalItemTaskPO();
        newArrivalItemTask.setId(request.getTaskId());
        if (request.getBusinessKey().equals(NewArrivalFlowNodeEnum.NUMBER_MAP_GROUP_UPLOAD.getCode())) {
            newArrivalItemTask.setWhiteBackgroundImg(request.getWhiteBackgroundImg());
            newArrivalItemTask.setSceneImg(request.getSceneImg());
        }
        if (request.getBusinessKey().equals(NewArrivalFlowNodeEnum.DISPLAY_CHART_DESCRIPTION_UPLOAD.getCode())) {
            newArrivalItemTask.setColumnImg(request.getColumnImg());
            newArrivalItemTask.setColumnExplain(request.getColumnExplain());
        }
        if (request.getBusinessKey().equals(NewArrivalFlowNodeEnum.DISPLAY_SINGLE_CHART_DESCRIPTION_UPLOAD.getCode())) {
            newArrivalItemTask.setProductImg(request.getProductImg());
        }
        if (request.getBusinessKey().equals(NewArrivalFlowNodeEnum.PRODUCT_SUPPORT_UPLOAD.getCode())) {
            newArrivalItemTask.setBrandImg(request.getBrandImg());
            newArrivalItemTask.setBrandExplain(request.getBrandExplain());
            newArrivalItemTask.setBrandRemark(request.getBrandRemark());
        }
        if (request.getBusinessKey().equals(NewArrivalFlowNodeEnum.BRAND_MASTER_PLAN_UPLOAD.getCode())) {
            newArrivalItemTask.setBrandProductImg(request.getBrandProductImg());
            newArrivalItemTask.setBrandTotalImg(request.getBrandTotalImg());
        }
        if (request.getBusinessKey().equals(NewArrivalFlowNodeEnum.PRODUCT_OPERATIONS_DEPARTMENT_APPROVAL.getCode())) {
            newArrivalItemTask.setFlowSellingCard(request.getFlowSellingCard());

        }
        if (request.getBusinessKey().equals(NewArrivalFlowNodeEnum.PRODUCT_COMPLIANCE_APPROVAL.getCode())) {
            newArrivalItemTask.setIsSharp(request.getIsSharp());
            newArrivalItemTask.setIsCodeWrinkle(request.getIsCodeWrinkle());
            newArrivalItemTask.setIsDateConceal(request.getIsDateConceal());
            newArrivalItemTask.setQualityLable(request.getQualityLable());
            newArrivalItemTask.setComplianceDocument(request.getComplianceDocument());
            newArrivalItemTask.setQualityTestingResult(request.getQualityTestingResult());
            newArrivalItemTask.setCommitmentLetter(request.getCommitmentLetter());
            newArrivalItemTask.setHighRiskReason(request.getHighRiskReason());
        }
        if (request.getBusinessKey().equals(NewArrivalFlowNodeEnum.ORDER_GROUP_APPROVAL.getCode())) {
            newArrivalItemTask.setRelatedAccessory(request.getRelatedAccessory());
        }
        if (request.getBusinessKey().equals(NewArrivalFlowNodeEnum.PRODUCT_OPERATIONS_DIRECTOR_APPROVAL.getCode())) {
            newArrivalItemTask.setFlowFirstComingTime(request.getFlowFirstComingTime());
        }
        // 更新主表记录
        this.updateById(newArrivalItemTask);


    }


    private static class Result {
        Map<Integer, Long> idMap;
        List<Long> idList;

        public Result(Map<Integer, Long> idMap, List<Long> idList) {
            this.idMap = idMap;
            this.idList = idList;
        }
    }

}
