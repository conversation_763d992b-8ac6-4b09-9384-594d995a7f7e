package io.terminus.lshm.product.server.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.terminus.lshm.mdm.facade.MdmApiFacade;
import io.terminus.lshm.mdm.request.*;
import io.terminus.lshm.mdm.response.MdmCheckGoodsResponse;
import io.terminus.lshm.mdm.response.MdmGetSortCodeResponse;
import io.terminus.lshm.mdm.response.MdmSaveGoodsResponse;
import io.terminus.lshm.product.common.brand.dto.BrandQueryLikeResponseDTO;
import io.terminus.lshm.product.common.recordfiling.request.RecordFilingItemTaskRequest;
import io.terminus.lshm.product.server.domain.brand.service.read.BrandReadService;
import io.terminus.lshm.product.server.domain.recordFiling.dao.RecordFilingItemTaskDao;
import io.terminus.lshm.product.server.domain.recordFiling.dao.RecordFilingItemTaskSpecificationPriceDao;
import io.terminus.lshm.product.server.domain.recordFiling.model.RecordFilingItemTaskPO;
import io.terminus.lshm.product.server.domain.recordFiling.model.RecordFilingItemTaskSpecificationPricePO;
import io.terminus.lshm.product.server.domain.suppliers.model.SuppliersPO;
import io.terminus.lshm.product.server.domain.suppliers.service.read.SuppliersReadService;
import io.terminus.lshm.product.server.enums.ItemUnitEnum;
import io.terminus.lshm.product.server.external.CommDualSystemAdapter;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/4
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessSendMdmManager {

    private final BrandReadService brandReadService;

    private final SuppliersReadService suppliersReadService;

    private final RecordFilingItemTaskDao recordFilingItemTaskDao;

    private final RecordFilingItemTaskSpecificationPriceDao recordFilingItemTaskSpecificationPriceDao;

    @Resource
    private MdmApiFacade mdmApiFacade;

    private final  String businessUnit = "BUSINESS_UNIT";

    private final String unit = "UNIT";

    private final String materialClass = "MATERIAL_CLASS";

    //其它： 商品类型
    private final  String lookup = "LOOKUP";

    //itemSpecificationList
    public MdmCheckGoodsResponse checkMdmData(RecordFilingItemTaskRequest request) {
        MdmCheckGoodsRequest checkGoodsRequest = new MdmCheckGoodsRequest();
        checkGoodsRequest.setMdmName(request.getItemName());   //商品名称
        checkGoodsRequest.setMaterialCode(request.getItemCode()); //商品编码
        checkGoodsRequest.setMatBarcode(request.getBarCodeList()); //外箱条码
        checkGoodsRequest.setBarcodes(
                request.getItemSpecificationList().stream()
                        .map(price -> Optional.ofNullable(price.getBarCodeList()).orElse(""))
                        .flatMap(barcodeStr -> Arrays.stream(barcodeStr.split(",")))
                        .filter(barcode -> !barcode.trim().isEmpty()).toArray(String[]::new)); //一品多码

        /*if (!"ok".equals(mdmCheckGoodsResponse.getCode())) {
            log.error("mdm校验失败,原因：{}", mdmCheckGoodsResponse.getMessage());
            throw new BusinessException("mdm校验失败,原因：" + mdmCheckGoodsResponse.getMessage());
        }*/
        return mdmApiFacade.checkMdmGoods(checkGoodsRequest);
    }

    public void sendManagerMdm(String businessId, Map<String, Object> context) {

        RecordFilingItemTaskPO recordFilingItemTask = recordFilingItemTaskDao.selectById(businessId);
        if (Objects.isNull(recordFilingItemTask)) {
            return;

        }

        if(recordFilingItemTask.getNccSynchronousState() != 0) {
            return;
        }

        // 获取商品规格和价格信息
        List<RecordFilingItemTaskSpecificationPricePO> itemTaskSpecificationPriceList =
                recordFilingItemTaskSpecificationPriceDao.selectList(
                        new LambdaQueryWrapper<RecordFilingItemTaskSpecificationPricePO>()
                                .eq(RecordFilingItemTaskSpecificationPricePO::getIsDeleted, 0)
                                .eq(RecordFilingItemTaskSpecificationPricePO::getRecordFilingId, recordFilingItemTask.getId())
                );

        if (null != context && null != context.get("recordFilingItemTask")) {
            RecordFilingItemTaskPO recordFilingItem = (RecordFilingItemTaskPO) context.get("recordFilingItemTask");
            final CopyOptions copyOptions = CopyOptions.create();
            copyOptions.setOverride(true);
            copyOptions.setIgnoreNullValue(true);
            BeanUtil.copyProperties(recordFilingItem, recordFilingItemTask, copyOptions);
        }

        try{
            // 调用sendNcc方法发送商品信息
            String mdmGoddsCode = sendMdm(recordFilingItemTask, itemTaskSpecificationPriceList);

            // 更新同步状态为已发送
            recordFilingItemTask.setNccSynchronousState(1);
            // 如果barCodeList 为空则将 商品编码写入
            if (StringUtils.isBlank(recordFilingItemTask.getBarCodeList())) {
                recordFilingItemTask.setBarCodeList(mdmGoddsCode);
            }
            recordFilingItemTask.setItemCode(mdmGoddsCode);
            recordFilingItemTaskDao.updateById(recordFilingItemTask);
        }catch (Exception e){
            // 捕获异常并记录日志，跳过当前商品，继续处理下一个
            log.error("推送MDM失败,错误信息: {}", e.getMessage());
            throw new BusinessException("推送MDM失败,失败原因: "+e);
        }
    }

    /**
     * 发送MDM请求保存数据
     * @param recordFilingItemTask           建档数据
     * @param itemTaskSpecificationPriceList 商品规格构成和价格信息
     * @return 商品编码
     * @throws JsonProcessingException
     */
    public String sendMdm(RecordFilingItemTaskPO recordFilingItemTask, List<RecordFilingItemTaskSpecificationPricePO> itemTaskSpecificationPriceList) throws Exception {
        //组装上新商品请求体
        MdmMaterialRequest material = new MdmMaterialRequest();
        BeanUtil.copyProperties(recordFilingItemTask, material);
        material.setUuid(UUID.randomUUID().toString().replace("-",""));
        material.setMdmCode(recordFilingItemTask.getItemCode());
        if(StringUtils.isNotEmpty(recordFilingItemTask.getSupplierCode())) {
            material.setSupplier(recordFilingItemTask.getSupplierCode());
        }else if(StringUtils.isNotEmpty(recordFilingItemTask.getSupplierName())){
            List<SuppliersPO> suppliersList = suppliersReadService.list(new LambdaQueryWrapper<SuppliersPO>().eq(SuppliersPO::getSupplierName, recordFilingItemTask.getSupplierName()));
            if(suppliersList != null && suppliersList.size() > 0) {
                material.setSupplier(suppliersList.get(0).getSupplierNumber());
            }
        }
        //品牌
        if(StringUtils.isNotEmpty(recordFilingItemTask.getBrandCode())) {
            material.setBrandCode(recordFilingItemTask.getBrandCode());
        }else if(StringUtils.isNotEmpty(recordFilingItemTask.getBrandName())){
            List<BrandQueryLikeResponseDTO> brandQueryLikeResponseList = brandReadService.brandSearchLike(recordFilingItemTask.getBrandName());
            if(ObjectUtil.isNotEmpty(brandQueryLikeResponseList)) {
                material.setBrandCode(brandQueryLikeResponseList.get(0).getBrandNumber());
            }
        }
        //组装物料/商品基本分类编码
        material.setGoodsType(queryMdmCode(material.getGoodsType(), materialClass));

        //商品类型查询
        material.setItemType(queryMdmCode(recordFilingItemTask.getItemType(), lookup));

        // 基础信息
        Optional<RecordFilingItemTaskSpecificationPricePO> mainUnit = itemTaskSpecificationPriceList.stream().filter(itemTaskSpecificationPrice -> itemTaskSpecificationPrice.getUnitType().equals(ItemUnitEnum.MAIN_UNIT.getName())).findFirst();
        if (!mainUnit.isPresent()) {
            // 处理找不到主单位的情况，例如抛出异常或设置默认值
            throw new BusinessException("主单位信息未找到");
        }

        String unitName = mainUnit.get().getUnitName();
        if("KG".equalsIgnoreCase(mainUnit.get().getUnitName())) {
            unitName = "公斤";
        }

        //组装计量单位分类编码请求体
        material.setUnitType(queryMdmCode(unitName, unit));

        // 主单位换算系数 转换成整数
        int mainUnitConversionFactor = mainUnit.get().getConversionFactor().intValue();

        // 内包装 如果是库存单位和销售单位 就取系数放入内包装
        Optional<RecordFilingItemTaskSpecificationPricePO> innerPackUnit = itemTaskSpecificationPriceList.stream().filter(
                itemTaskSpecificationPrice -> itemTaskSpecificationPrice.getIsSaleUnit()==1 && itemTaskSpecificationPrice.getIsInventoryUnit()==1).findFirst();
        innerPackUnit.ifPresent(recordFilingItemTaskSpecificationPricePO -> material.setDef52(String.valueOf(recordFilingItemTaskSpecificationPricePO.getConversionFactor().intValue())));

        // 一品多码
        List<MdmMatBarcodesRequest> matBarcodesMdmRequestList = new ArrayList<>();
        for(RecordFilingItemTaskSpecificationPricePO specificationPrice :itemTaskSpecificationPriceList) {
            MdmMatBarcodesRequest matBarcodesRequest = new MdmMatBarcodesRequest();
            if(ObjectUtil.isNotEmpty(specificationPrice.getBarCodeList())) {
                matBarcodesRequest.setPrimaryKeyKey(String.valueOf(specificationPrice.getId()));
                matBarcodesRequest.setUnit(queryMdmCode(specificationPrice.getUnitName(), unit));
                matBarcodesRequest.setIsPumEasDoc(null != specificationPrice.getIsPurchaseUnit() && specificationPrice.getIsPurchaseUnit() == 1 ? "Y" : "N");
                matBarcodesRequest.setIsStockMeasDoc(null != specificationPrice.getIsInventoryUnit() && specificationPrice.getIsInventoryUnit() == 1 ? "Y" : "N");
                matBarcodesRequest.setIsSaleMeasDoc(null != specificationPrice.getIsSaleUnit() && specificationPrice.getIsSaleUnit() == 1 ? "Y" : "N");
                matBarcodesRequest.setBarCode(specificationPrice.getBarCodeList());
                matBarcodesRequest.setSpecification(specificationPrice.getSpecificationRemark());
                matBarcodesRequest.setConversionRate(specificationPrice.getConversionFactor().intValue() + "/" + mainUnitConversionFactor);
            }
            matBarcodesMdmRequestList.add(matBarcodesRequest);
        }
        material.setMatBarcodesMdmList(matBarcodesMdmRequestList);

        //采购信息 默认为 湖南鸣鸣很忙商业连锁股份有限公司 编码 9999
        List<MdmMatPurchaseRequest> matPurchaseList = new ArrayList<>();
        MdmMatPurchaseRequest matPurchase = new MdmMatPurchaseRequest();
        matPurchase.setPurchaseOrg("9999");
        matPurchase.setMainSupplier(recordFilingItemTask.getSupplierCode());
        matPurchaseList.add(matPurchase);
        material.setMatPurchaseMdmList(matPurchaseList);

        // 物料品牌信息  建档时只能选择一个品牌
        List<MdmMatBrandRequest> mdmMatBrandRequestList = new ArrayList<>();
        /*MdmMatBrandRequest mdmMatBrandRequest = new MdmMatBrandRequest();
        if(StringUtils.isNotEmpty(recordFilingItemTask.getBrandCode()) && recordFilingItemTask.getBrandId() !=null) {
            mdmMatBrandRequest.setPrimaryKeyKey(String.valueOf(recordFilingItemTask.getBrandId()));
            mdmMatBrandRequest.setPkBank(recordFilingItemTask.getBrandCode());
        }else if(StringUtils.isNotEmpty(recordFilingItemTask.getBrandName())){
            List<BrandQueryLikeResponseDTO> brandQueryLikeResponseList = brandReadService.brandSearchLike(recordFilingItemTask.getBrandName());
            if(ObjectUtil.isNotEmpty(brandQueryLikeResponseList)) {
                mdmMatBrandRequest.setPkBank(brandQueryLikeResponseList.get(0).getBrandNumber());
                mdmMatBrandRequest.setPrimaryKeyKey(String.valueOf(brandQueryLikeResponseList.get(0).getId()));
            }
        }
        mdmMatBrandRequestList.add(mdmMatBrandRequest);
        material.setMatBrandMdmList(mdmMatBrandRequestList);*/

        //已在售品牌
        String onSaleBrand = recordFilingItemTask.getOnSaleBrand();
        // 格式为 ZYM,LSHM 或者 赵一鸣,零食很忙
        if (StringUtils.isNotBlank(onSaleBrand)) {
            String[] brands = onSaleBrand.split(",");
            for (String brand : brands) {
                // 统一转换为小写英文编码
                MdmMatBrandRequest mdmMatBrandRequest = getMaterialBrandInfo(recordFilingItemTask.getItemDepartment(), brand);
                mdmMatBrandRequestList.add(mdmMatBrandRequest);
                material.setMatBrandMdmList(mdmMatBrandRequestList);
            }
        }

        //库存信息 (上新商品暂无库存信息) 不传

        // 发送MDM请求保存数据
        MdmSaveGoodsRequest saveGoodsRequest = new MdmSaveGoodsRequest();
        saveGoodsRequest.setPuuid(UUID.randomUUID().toString().replace("-",""));
        List<MdmMaterialRequest> materialList = new ArrayList<>();
        materialList.add(material);
        saveGoodsRequest.setData(materialList);
        try{
            MdmSaveGoodsResponse saveaveGoodsResponse = mdmApiFacade.saveMdmGoods(saveGoodsRequest);
            if (saveaveGoodsResponse == null || saveaveGoodsResponse.getErrorCode() != null || saveaveGoodsResponse.getData() == null) {
                log.error("发送MDM失败，商品ID：{}，返回结果：{}", recordFilingItemTask.getId(), saveaveGoodsResponse);
                throw new BusinessException("发送MDM未响应");
            }
            if(saveaveGoodsResponse.getData().get(0).getErrorCode() != null) {
                log.error("saveMdmGoodsTest 较验未通过,原因为：{}, 错误代码：{}", saveaveGoodsResponse.getData().get(0).getMessage(), saveaveGoodsResponse.getData().get(0).getErrorCode());
                throw new BusinessException("MDM推送失败！错误码：" + saveaveGoodsResponse.getData().get(0).getErrorCode() + "  原因：" + saveaveGoodsResponse.getData().get(0).getMessage());
            }
            log.info("发送MDM成功，商品ID：{}，返回结果：{}", recordFilingItemTask.getId(), saveaveGoodsResponse);
            return saveaveGoodsResponse.getData().get(0).getMdmCode();
        }catch (Exception e){
            throw new BusinessException("MDM 请求异常，异常信息：" + e.getMessage());
        }
    }

    protected String queryMdmCode(String mdmName,String fromCode) {
        MdmGetSortCodeRequest request = new MdmGetSortCodeRequest();
        request.setUuid(UUID.randomUUID().toString().replace("-",""));
        switch (fromCode) {
            case unit:
                //计量单位分类编码请求体
                request.setFormCode(unit);
                MdmGetSortCodeRequest.RequestData unitRequestData = new MdmGetSortCodeRequest.RequestData();
                unitRequestData.setMdmName(mdmName);
                request.setData(unitRequestData);
                MdmGetSortCodeResponse unitSortCodeResponse = mdmApiFacade.getMdmUnitCode(request);
                if(ObjectUtil.isNotEmpty(unitSortCodeResponse) && ObjectUtil.isNotEmpty(unitSortCodeResponse.getDATA())) {
                    return Objects.requireNonNull(unitSortCodeResponse.getDATA().stream().findFirst().orElse(null)).getMdmCode();
                }else {
                    log.error("查询计量单位分类编码失败，请求参数：{}", request);
                    throw new BusinessException("查询计量单位分类编码失败,请求参数："+ request);
                }
            case materialClass:
                //组装物料/商品基本分类编码请求体
                request.setFormCode(materialClass);
                MdmGetSortCodeRequest.RequestData materialRequestData = new MdmGetSortCodeRequest.RequestData();
                materialRequestData.setMdmName(mdmName);
                request.setData(materialRequestData);
                MdmGetSortCodeResponse materialSortCodeResponse = mdmApiFacade.getMdmMaterialCode(request);
                if(ObjectUtil.isNotEmpty(materialSortCodeResponse) && ObjectUtil.isNotEmpty(materialSortCodeResponse.getDATA())) {
                    return Objects.requireNonNull(materialSortCodeResponse.getDATA().stream().findFirst().orElse(null)).getMdmCode();
                }else {
                    log.error("查询物料/商品基本分类编码失败，请求参数：{}", request);
                    throw new BusinessException("查询物料/商品基本分类编码失败,请求参数："+ request);
                }
            case lookup:
                //商品类型查询编码请求体
                request.setFormCode(lookup);
                MdmGetSortCodeRequest.RequestData matTypeRequestData = new MdmGetSortCodeRequest.RequestData();
                matTypeRequestData.setMdmName(mdmName);
                request.setData(matTypeRequestData);
                MdmGetSortCodeResponse matTypeCodeResonse = mdmApiFacade.getMdmMatTypeCode(request);
                if(ObjectUtil.isNotEmpty(matTypeCodeResonse) && ObjectUtil.isNotEmpty(matTypeCodeResonse.getDATA())) {
                    return Objects.requireNonNull(matTypeCodeResonse.getDATA().stream().findFirst().orElse(null)).getMdmCode();
                }else {
                    log.error("查询商品类型分类编码失败，请求参数：{}", request);
                    throw new BusinessException("查询商品类型分类编码失败,请求参数："+ request);
                }
            default:
                return null;
        }
    }

    public static @NotNull MdmMatBrandRequest getMaterialBrandInfo(String itemDepartment, String brand) {
        String standardizedBrand = "";
        // 判断是否为纯字母（如 ZYM）
        if (brand.matches("[a-zA-Z]+")) {
            // 转换为小写
            standardizedBrand = brand.toLowerCase();
        } else {
            // 假设其他情况为中文名称或其他格式，手动映射或规则处理
            // 示例：根据实际业务逻辑替换为具体映射关系
            switch (brand.trim()) {
                case "赵一鸣":
                    standardizedBrand = "zym";
                    break;
                case "零食很忙":
                    standardizedBrand = "lshm";
                    break;
                default:
                    // 默认尝试转为小写
                    standardizedBrand = brand.trim().toLowerCase();
                    break;
            }
        }

        MdmMatBrandRequest bandInfo = new MdmMatBrandRequest();
        // 使用标准化的品牌编码
        bandInfo.setPkBank(standardizedBrand);
        bandInfo.setDef1(itemDepartment);
        return bandInfo;
    }

}