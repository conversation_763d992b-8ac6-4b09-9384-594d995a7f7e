<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.terminus.lshm</groupId>
        <artifactId>product-center</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>product-server</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>io.terminus.parana</groupId>-->
<!--            <artifactId>elasticsearch-http-client</artifactId>-->
<!--        </dependency>-->
        <!-- MQ -->
<!--        <dependency>-->
<!--            <groupId>io.terminus.common</groupId>-->
<!--            <artifactId>terminus-spring-boot-starter-rocketmq</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>lshm-mybatis-plus</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>io.terminus.parana</groupId>-->
        <!--            <artifactId>elasticsearch-http-client</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.terminus.parana</groupId>-->
        <!--            <artifactId>search-retrieval-service</artifactId>-->
        <!--            <version>${terminus.parana.search.version}</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.alibaba.mqtt</groupId>-->
        <!--            <artifactId>server-sdk</artifactId>-->
        <!--            <version>${mqtt.version}</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>price-inner-api</artifactId>
        </dependency>
        <!--   门店中心  -->
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>store-inner-api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--   bizops中心，统一了IPAAS对接，需要SLS配置  -->
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>bizops-sdk</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--   通知中心-对接企业微信，短信，站内信  -->
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>trantor-notice-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>item-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>inventory-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--   组织中心  -->
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>organization-inner-api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.lshm</groupId>
                    <artifactId>organization-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus</groupId>
                    <artifactId>terminus-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>organization-api</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>store-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>price-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--任务中心-->
        <!--        <dependency>-->
        <!--            <groupId>io.terminus.lshm</groupId>-->
        <!--            <artifactId>taskplatformapi</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.terminus.taurus</groupId>-->
        <!--            <artifactId>taurus-common</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>lshm-server-common</artifactId>
        </dependency>
        <!-- 用户中心 -->
        <dependency>
            <groupId>io.terminus.draco</groupId>
            <artifactId>draco-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 促销中心 -->
        <dependency>
            <groupId>io.terminus.gaia</groupId>
            <artifactId>promotion-campaign-api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <!--                    <groupId>*</groupId>-->
                    <!--                    <artifactId>*</artifactId>-->
                    <groupId>io.terminus.trantor</groupId>
                    <artifactId>trantor-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.lshm</groupId>
                    <artifactId>proxy-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.gaia</groupId>
                    <artifactId>benefit-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.gaia</groupId>
                    <artifactId>play-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.shardingsphere.elasticjob</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.draco</groupId>
                    <artifactId>draco-web-autoconfigure</artifactId>
                </exclusion>

            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
        </dependency>


        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-anno</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-flow-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-ncc-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-mdm-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>item-api</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>


</project>
