package io.terminus.lshm.product.job.notice;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.base.Throwables;
import io.terminus.lshm.product.server.manager.BusinessAuditRecordNoticeManager;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/21
 * @Version 1.0
 */
@Component
@RequiredArgsConstructor
public class BusinessAuditNoticeJob extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger("schedulerx");

    private final BusinessAuditRecordNoticeManager businessAuditRecordNoticeManager;


    @Override
    public ProcessResult process(JobContext context) throws Exception {
        log.info("BusinessAuditNoticeJob start:{}", JSON.toJSONString(context));
        try {
            businessAuditRecordNoticeManager.sendWechatAppMsg();
        } catch (Exception e) {
            log.error("BusinessAuditNoticeJob Exception: {}", Throwables.getStackTraceAsString(e));
        }
        log.info("BusinessAuditNoticeJob end");
        return new ProcessResult(true);
    }


}
