package io.terminus.lshm.product.job.offshelf;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.base.Throwables;
import io.terminus.lshm.product.server.domain.offshelf.service.write.OffShelfItemTaskWriteService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class OffShelfItemAuditAutoNoticeWXJob extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger("schedulerx");


    private final OffShelfItemTaskWriteService offShelfItemTaskWriteService;


    @Override
    public ProcessResult process(JobContext context) throws Exception {
        log.info("OffShelfItemAuditAutoNoticeWXJob start:{}", JSON.toJSONString(context));
        try {
            offShelfItemTaskWriteService.sendWechatAppMsg();
        } catch (Exception e) {
            log.error("OffShelfItemAuditAutoNoticeWXJob Exception: {}", Throwables.getStackTraceAsString(e));
        }
        log.info("OffShelfItemAuditAutoNoticeWXJob end");
        return new ProcessResult(true);
    }

}
