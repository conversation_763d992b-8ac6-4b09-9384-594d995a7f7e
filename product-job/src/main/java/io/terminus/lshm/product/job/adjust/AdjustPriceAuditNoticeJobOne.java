package io.terminus.lshm.product.job.adjust;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.base.Throwables;
import io.terminus.lshm.product.server.manager.AdjustPriceAuditNoticeManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/21
 * @Version 1.0
 */
@Component
@RequiredArgsConstructor
public class AdjustPriceAuditNoticeJobOne extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger("schedulerx");

    private final AdjustPriceAuditNoticeManager adjustPriceAuditNoticeManager;


    @Override
    public ProcessResult process(JobContext context) throws Exception {
        log.info("AdjustPriceAuditNoticeJobOne start");
        try {
            adjustPriceAuditNoticeManager.sendWechatAppMsg();
        } catch (Exception e) {
            log.error("AdjustPriceAuditNoticeJobOne Exception: {}", Throwables.getStackTraceAsString(e));
        }
        log.info("AdjustPriceAuditNoticeJobOne end");

        return new ProcessResult(true);
    }

}
