<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.terminus.lshm</groupId>
        <artifactId>product-center</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>product-job</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>io.terminus.lshm</groupId>
            <artifactId>product-server</artifactId>
            <version>${project.version}</version>
        </dependency>
       <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
            <version>1.13.0</version>
           <exclusions>
               <exclusion>
                   <groupId>log4j</groupId>
                   <artifactId>log4j</artifactId>
               </exclusion>
           </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx-plugin-trace-opentelemetry</artifactId>
            <version>1.0.8</version>
        </dependency>
    </dependencies>

</project>
