<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.lshm</groupId>
        <artifactId>lshm-parent</artifactId>
        <version>1.0.0.LSHM-TEST-SNAPSHOT</version>
    </parent>
    <artifactId>product-center</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <modules>
        <module>product-api</module>
        <!--        <module>product-inner-api</module>-->
        <module>product-server</module>
        <module>product-job</module>
        <module>product-mq</module>
        <module>product-server-starter</module>
        <module>product-app</module>
        <module>product-starter</module>
    </modules>

    <properties>
        <revision>1.0.0.LSHM-TEST-SNAPSHOT</revision>

        <metastore.url>http://ms-dev.noprod.hnlshm.com/lshm/bsc</metastore.url>

        <project.name>product-center</project.name>
        <project.description>product-center</project.description>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>

        <trantor.version>2.0.17.RELEASE</trantor.version>
        <mqtt.version>1.0.5.Final</mqtt.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.15.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>lshm</id>
            <name>lshm release repository</name>
            <url>http://nexus-sys.noprod.hnlshm.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>lshm</id>
            <name>lshm snapshot repository</name>
            <url>http://nexus-sys.noprod.hnlshm.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.7.1</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!--<plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>2.30.0</version>
                <executions>
                    <execution>
                        <id>spotless-check</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <upToDateChecking>
                        <enabled>false</enabled>
                        <indexFile>${project.basedir}/custom-index-file</indexFile> &lt;!&ndash; optional, default is ${project.build.directory}/spotless-index &ndash;&gt;
                    </upToDateChecking>
                    <java>
                        <prettier>
                            <devDependencies>
                                <prettier>2.8.8</prettier>
                                <prettier-plugin-java>2.3.0</prettier-plugin-java>
                            </devDependencies>
                            <configFile>.editorconfig</configFile>
                        </prettier>
                    </java>
                </configuration>
            </plugin>-->

        </plugins>
    </build>
    <profiles>
        <profile>
            <id>staging</id>
            <properties>
                <revision>2.0.0.LSHM-STAGING-SNAPSHOT</revision>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <revision>2.0.0.LSHM-PROD-SNAPSHOT</revision>
            </properties>
        </profile>
    </profiles>
</project>
