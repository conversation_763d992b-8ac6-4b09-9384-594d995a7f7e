<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="off" monitorInterval="30">
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%X{TRACE_ID}] [%thread] %logger{50} - %msg%n</Property>
        <Property name="LOG_HOME">${sys:user.dir}</Property>
    </Properties>
    <Appenders>
        <!-- 控制台输出 -->
        <Console name="CONSOLE" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>

        <SchedulerxLog4j2Appender name="schedulerxLog"
                                  timeFormat="yyyy-MM-dd'T'HH:mmZ"
                                  timeZone="UTC"
                                  ignoreExceptions="true">
            <PatternLayout pattern="%d %-5level [%thread] %logger{0}: %msg"/>
        </SchedulerxLog4j2Appender>

        <!-- 文件输出 -->
        <RollingFile name="APPLICATION"
                     fileName="${LOG_HOME}/product/logs/application.log"
                     filePattern="${LOG_HOME}/product/logs/application.log.%d{yyyy-MM-dd}.%i.log">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="2GB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="14">
                <Delete basePath="${LOG_HOME}/product/logs/" maxDepth="1">
                    <IfFileName glob="application*.log.*"/>
                    <IfLastModified age="14d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!--        <Async name = "AsyncLogFile">-->
        <!--            <AppenderRef ref="APPLICATION"/>-->
        <!--        </Async>-->

    </Appenders>


    <Loggers>

        <!-- 特定日志配置 -->
        <Logger name="ShardingSphere-SQL" level="WARN"/>
        <Logger name="io.terminus.autumn.search.scheduler" level="WARN" additivity="false">
            <AppenderRef ref="CONSOLE"/>
        </Logger>
        <Logger name="schedulerx" level="debug" additivity="false">
            <AppenderRef ref="schedulerxLog"/>
        </Logger>

        <AsyncLogger name="io.terminus.lshm.product" level="debug" includeLocation="false" additivity="false">
            <AppenderRef ref="CONSOLE"/>
<!--            <AppenderRef ref="APPLICATION"/>-->
        </AsyncLogger>


        <!-- 根日志记录器 -->
        <Root level="info">
            <AppenderRef ref="CONSOLE"/>
<!--            <AppenderRef ref="APPLICATION"/>-->
        </Root>

    </Loggers>
</Configuration>
