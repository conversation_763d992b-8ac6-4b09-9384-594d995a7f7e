spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_ADDRESS:127.0.0.1:8848}
        namespace: ${NACOS_TENANT_ID:}
        enabled: ${DISCOVERY_ENABLE_NACOS:true}
  redis:
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DATABASE:10}
    password: ${REDIS_PASSWORD:}
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:product_center}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&useSSL=false&rewriteBatchedStatements=true
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:anywhere}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      max-lifetime: ${DS_MAX_POOL_SIZE:1765000}
      maximum-pool-size: ${DS_MAX_MIN_POOL_SIZE:100}
  schedulerx2:
    endpoint: ${SCHEDULERX2_ENDPOINT:acm.aliyun.com}
    namespace: ${SCHEDULERX2_NAMESPACE:64021d75-c26e-41ae-a4aa-7d12b5e2c2cb}
    groupId: ${spring.application.name}
    appKey: ${SCHEDULERX2_APP_KEY:4CicrlU8IwYqAsbuPHn2ibfw}
    label: ${spring.application.name}


aliyun:
  sls:
    project: ${SLS_OPERATION_LOG_PROJECT:k8s-log-c40c6f910f475424bbf578231af1e6627}
    store: ${SLS_OPERATION_LOG_STORE:operation-log-staging}
    endpoint: ${SLS_OPERATION_LOG_ENDPOINT:cn-hangzhou-intranet.log.aliyuncs.com}
    accessKeyId: ${SLS_OPERATION_LOG_ACCESS_KEY_ID:LTAI5t9R3rhQ9oGTXxxijp7y}
    accessKeySecret: ${SLS_OPERATION_LOG_ACCESS_KEY_SECRET:******************************}
    topic: ${TOPIC:topic_ops_staging}

mq:
  clientType: ${MQ_CLIENT_TYPE:rocketmq}
  nameServer: ${ROCKETMQ_NAMESRV_HOST:***********}:${ROCKETMQ_NAMESRV_PORT:9876}

instruction:
  mq:
    topic: BUSINESS_PROD
    tag: ISSUE_INSTRUCTION

data.transfer:
  storage:
    active: oss
    local:
      baseDir: ~/data/
    oss:
      baseDir: data/
      endpoint: ${OSS_ENDPOINT}
      key: ${OSS_ACCESS_KEY_ID}
      secret: ${OSS_ACCESS_KEY_SECRET}
      bucket: ${OSS_BUCKET}
      privateBucket: ${OSS_PRIVATE_BUCKET}

jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson
      limit: 100
      expireAfterAccessInMillis: 30000
  remote:
    default:
      type: redis
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:10}
      password: ${REDIS_PASSWORD:}
#product:
#  search:
#    index:
#      saleproduct: staging_store_sale_product_line
#      deliveryproduct: staging_store_delivery_product_line
#      saleproductAdjust: staging_store_sale_product_adjust
#      deliveryproductAdjust: staging_store_delivery_product_adjust
#    mappingPath:
#      saleproduct: MAPPING/store_sale_product_line_mapping.json
#      deliveryproduct: MAPPING/store_delivery_product_line_mapping.json
#      saleproductAdjust: MAPPING/store_sale_product_adjust_mapping.json
#      deliveryproductAdjust: MAPPING/store_delivery_product_adjust_mapping.json
#search:
#  event:
#  datasource:
#    address: ${ELASTICSEARCH_HOST:127.0.0.1}:${ELASTICSEARCH_PORT:9200}
#    cluster: ${CLUSTER_NAME:elasticsearch}
#    username: ${ELASTICSEARCH_NAME:}
#    password: ${ELASTICSEARCH_SECRET:}
#    sniff: false
#  retrieval:
#    multiTenant: false
#    maxResultWindow: 10000

elasticjob:
  enabled: true
  reg-center:
    server-lists: ${ZOOKEEPER_HOSTS:127.0.0.1:2181}
    namespace: ${spring.application.name}
    connection-timeout-milliseconds: 50000
  jobs:
    OffShelfItemAuditAutoNoticeWXJob:
      description: 每10分钟将查询是否通知下架商品审批
      elasticJobClass: io.terminus.lshm.product.job.offshelf.OffShelfItemAuditAutoNoticeWXJob
      cron: 0 0/10 8-23 * * ?
      shardingTotalCount: 1
      failover: true
      overwrite: true
      misfire: true
    BusinessAuditNoticeJob:
      description: 每10分钟将查询是否通知审批通知（a)每天早上8点开始发送通知，0点结束）
      elasticJobClass: io.terminus.lshm.product.job.notice.BusinessAuditNoticeJob
      cron: 0 0/10 8-23 * * ?
      shardingTotalCount: 1
      failover: true
      overwrite: true
      misfire: true
    AdjustPriceAuditNoticeJobOne:
      description: 每6小时调用调价未审批的单据
      elasticJobClass: io.terminus.lshm.product.job.adjust.AdjustPriceAuditNoticeJobOne
      cron: 0 0 0/6 * * ?
      shardingTotalCount: 1
      failover: true
      overwrite: true
      misfire: true
    AdjustPriceAuditNoticeJobTwo:
      description: 每天晚上59分开始调价未审批的单据
      elasticJobClass: io.terminus.lshm.product.job.adjust.AdjustPriceAuditNoticeJobTwo
      cron: 0 0 23 * * ?
      shardingTotalCount: 1
      failover: true
      overwrite: true
      misfire: true

wx:
  notice:
    offset: ${OFFSET_VALUE:-4}
#instruction:
#  mq:
#    topic: BUSINESS_STAGING
#    tag: ISSUE_INSTRUCTION

#redis:
#  bloomFilterInit:
#    - key: "bf:delivery:fee:name"
#      expectedInsertions: 10000
#      falseProbability: 0.01
#    - key: "bf:delivery:discount:name"
#      expectedInsertions: 10000
#      falseProbability: 0.01
terminus:
  mqServerAddress: ${MQ_SERVER_ADDRESS:rmq-cn-x0r3ajfqb0e-vpc.cn-hangzhou.rmq.aliyuncs.com:8080}
  clientType: ${CLIENT_TYPE:ONS}
  producerGroup: ${MQ_PRODUCER_GROUP:product_producer_group}
  consumerGroup: ${MQ_CONSUMER_GROUP:product_consumer_group}
  aliyun:
    accessKey: ${ALIYUN_ACCESSKEY:LTAI5t9R3rhQ9oGTXxxijp7y}
    secretKey: ${ALIYUN_SECRETKEY:******************************}
    regionId: ${DS_MQ_ONS_REGIONID:cn-hangzhou}
    instanceId: ${MQ_SEND_ONS_INSTANCE_ID:rmq-cn-x0r3ajfqb0e}
  mq:
    consumer:
      group:
        product: ${product_GROUP:GID_product_center_staging}
    topic:
      product: ${product_TOPIC:product_center_topic_staging}
#    tag:
#      product:
#        notice:
#          audit: product_adjust_audit_notice
#          discard: product_adjust_discard_notice
#          issue: product_adjust_issue_notice
#        sync:
#          lm:
#            sale: sync_lm_sale_product
#            delivery: sync_lm_delivery_product


ops:
  message:
    topic: ${OPS_TOPIC:topic_ops_test}

third:
  ipaasNcc:
    url: ${IPAAS_NCC_HOST:https://ipaas-gw.hnlshm.com}
    tokey: ${IPAAS_NCC_TOKEY:Wj5nq50v5nUYTbtaOTKSQoODH}

notice:
  template:
    companyAdjustproductIssued: ${notice_template_companyAdjustproductIssued:STATION_LETTER_1721286286319}
    storeAdjustproductAudit: ${notice_template_storeAdjustproductAudit:STATION_LETTER_1721286394225}
    storeAdjustproductDiscard: ${notice_template_storeAdjustproductDiscard:STATION_LETTER_1721286432398}
ipaas:
  host: ${IPAAS_HOST:https://ipaas-gw.hnlshm.com}
  apps:
    userId: ${IPAAS_USER_ID:lshm_business_center}
    password: ${IPAAS_PASSWORD:NqJ@M8gQ}
    loginUrl: ${IPAAS_LOGIN_URL:https://ipaas-gw.hnlshm.com/restcloud/rest/core/auth/login}

audit:
  flow:
    mockUser: ${AUDIT_FLOW_MOCK_USER:false}
    mockUserId: ${AUDIT_FLOW_MOCK_USERIDS:1}
    mockAccountId: ${AUDIT_FLOW_MOCK_ACCOUNTIDS:1}
    noticeUrl: ${AUDIT_FLOW_NOTICE_URL:pages/entry/index?tagUrl=WebPage&tagParams=}
    frontUrl: ${AUDIT_FLOW_FRONTURL:https://task-center-staging.noprod.hnlshm.com/}

off-shelf:
  notice:
    mock: ${OFF_SHELF_NOTICE_MOCK:false}
    frontUrl: ${OFF_SHELF_FRONT_URL:https://product-center-dev.noprod.hnlshm.com/}?loginInfo=
    sub-hour: ${OFF_SHELF_NOTICE_SUBHOUR:-6}
  audit:
    flow-nodes:
      - key: PRODUCT_SPECIALIST_APPLICATION
        name: 产品专员申请建档
        approverJob: ${PRODUCT_SPECIALIST_JOB:LSHM21}
        num: 1
      - key: PRODUCT_MANAGER_REVIEW
        name: 待产品经理审核
        approverJob: ${PRODUCT_MANAGER_JOB:LSHM272}
        isOrg: ${PRODUCT_MANAGER_JOB_ISORG:true}
        num: 2
      - key: COMMODITY_DIRECTOR_REVIEW
        name: 待商品总监审核
        approverJob: ${COMMODITY_DIRECTOR_JOB:LSHM822}
        num: 3
      - key: PRODUCT_DIRECTOR_REVIEW
        name: 待产品总监审核
        approverJob: ${PRODUCT_DIRECTOR_JOB:LSHM36}
        num: 4
      - key: ORDER_TEAM_LEADER_REVIEW
        name: 待订单组长审核
        approverJob: ${ORDER_TEAM_LEADER_JOB:LSHM96}
        num: 5
      - key: ORDER_SPECIALIST_REVIEW
        name: 待订单专员审核
        approverJob: ${ORDER_SPECIALIST_JOB:LSHM117}
        isOrg: ${ORDER_SPECIALIST_JOB_ISORG:true}
        num: 6
      - key: PRODUCT_SUPPORT_REVIEW
        name: 待产品支持审核
        approverJob: ${PRODUCT_SUPPORT_JOB:LSHM458,LSHM14}
        num: 7
      - key: COMPLETED
        name: 已完成
        num: 8
    off-shelf-reasons:
      - key: QUALITY_ISSUE
        name: 产品质量问题
      - key: END_OF_SALES
        name: 销售末尾
      - key: REPLACED_BY_BETTER_PRODUCT
        name: 更好产品替换
      - key: CATEGORY_OPTIMIZATION
        name: 品类优化
      - key: PRODUCTION_CAPACITY_ISSUE
        name: 产能不能供应
      - key: SPECIFICATION_CHANGES
        name: 规格变更
product:
  flow:
    url: https://authine.hnlshm.com
    app-key: offerCenter
    app-secret: 27f7d23ffc7046be82cec757251bc406
  ncc:
    enabled: false
    url: ${IPAAS_HOST:https://ipaas-gw.hnlshm.com}
    appKey: ${NCC_APP_KEY:lshm_business_center}
    appSecret: ${NCC_SECRET:NqJ@M8gQ}
  mdm:
    app_id: ${MDM_PUSH_APP_ID:006c6d9c015041cc87710b3ba89d99cf}
    app_key: ${MDM_PUSH_APP_KEY:43c3d4a0e7ee4846ae59ba09b61e73ee}
    open: ${MDM_PUSH_OPEN:true}
    url: ${MDM_PUSH_URL:https://mdmtest.hnlshm.com}
